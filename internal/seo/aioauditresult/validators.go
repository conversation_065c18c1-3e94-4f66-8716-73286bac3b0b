package aioauditresult

import (
	"github.com/vendasta/gosdks/validation"
	"github.com/vendasta/gosdks/verrors"
)

// Validate ensures that the AuditScores it is run on is in a valid state for saving to storage and returning to consumers of the Service interface.
func (a *AuditScores) Validate() error {
	return validation.NewValidator().
		Rule(validation.StringNotEmpty(a.AuditScoreScopeName, verrors.InvalidArgument, "AuditScoreScopeName is required")).
		Validate()
}

// Validate ensures that the AuditScoreResults it is run on is in a valid state for saving to storage and returning to consumers of the Service interface.
func (a *AuditScoreResults) Validate() error {
	for _, t := range a.AuditScores {
		err := validation.NewValidator().Rule(t).Validate()
		if err != nil {
			return err
		}
	}
	return validation.NewValidator().
		Rule(validation.StringNotEmpty(a.AuditPageURL, verrors.InvalidArgument, "AuditPageURL is required")).
		Validate()
}

// Validate ensures that the AuditPages it is run on is in a valid state for saving to storage and returning to consumers of the Service interface.
func (a *AuditPageData) Validate() error {
	return validation.NewValidator().
		Rule(validation.StringNotEmpty(a.PageURL, verrors.InvalidArgument, "PageURL is required")).
		Rule(validation.StringNotEmpty(a.PageData, verrors.InvalidArgument, "PageData is required")).
		Validate()
}

// Validate ensures that the AIOAuditResult it is run on is in a valid state for saving to storage and returning to consumers of the Service interface.
func (a *AIOAuditResult) Validate() error {
	for _, t := range a.AuditPages {
		err := validation.NewValidator().Rule(t).Validate()
		if err != nil {
			return err
		}
	}
	for _, t := range a.AuditScoreResults {
		err := validation.NewValidator().Rule(t).Validate()
		if err != nil {
			return err
		}
	}
	return validation.NewValidator().Rule(validation.StringNotEmpty(a.BusinessID, verrors.InvalidArgument, "BusinessID is required")).
		Rule(validation.StringNotEmpty(a.BrandName, verrors.InvalidArgument, "BrandName is required")).
		Rule(validation.StringNotEmpty(a.WebsiteURL, verrors.InvalidArgument, "WebsiteURL is required")).
		Rule(validation.StringNotEmpty(a.AuditDate, verrors.InvalidArgument, "AuditDate is required")).
		Validate()
}
