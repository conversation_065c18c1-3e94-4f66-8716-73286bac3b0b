// *********************************
// Code generated by sdkgen
// DO NOT EDIT!.
//
// Objects.
// *********************************
import * as i from '../interfaces';
import { FieldMask } from './field-mask';
import { Geo } from './listing-profile';
import * as e from '../enums';

export function enumStringToValue<E>(enumRef: any, value: string): E {
  if (typeof value === 'number') {
    return value;
  }
  return enumRef[value];
}

export class AIOAuditResults implements i.AIOAuditResultsInterface {
    businessId: string;
    brandName: string;
    websiteUrl: string;
    auditDate: string;
    startDate: Date;
    totalPages: number;
    auditStatus: string;
    auditSummary: string;
    auditPages: AuditPageData[];
    auditScoreResults: AuditScoreResults[];
    keyword: string;
    auditId: string;
    auditUrl: string;

    static fromProto(proto: any): AIOAuditResults {
        let m = new AIOAuditResults();
        m = Object.assign(m, proto);
        if (proto.startDate) {m.startDate = new Date(proto.startDate);}
        if (proto.totalPages) {m.totalPages = parseInt(proto.totalPages, 10);}
        if (proto.auditPages) {m.auditPages = proto.auditPages.map(AuditPageData.fromProto);}
        if (proto.auditScoreResults) {m.auditScoreResults = proto.auditScoreResults.map(AuditScoreResults.fromProto);}
        return m;
    }

    constructor(kwargs?: i.AIOAuditResultsInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.businessId !== 'undefined') {toReturn['businessId'] = this.businessId;}
        if (typeof this.brandName !== 'undefined') {toReturn['brandName'] = this.brandName;}
        if (typeof this.websiteUrl !== 'undefined') {toReturn['websiteUrl'] = this.websiteUrl;}
        if (typeof this.auditDate !== 'undefined') {toReturn['auditDate'] = this.auditDate;}
        if (typeof this.startDate !== 'undefined' && this.startDate !== null) {toReturn['startDate'] = 'toApiJson' in this.startDate ? (this.startDate as any).toApiJson() : this.startDate;}
        if (typeof this.totalPages !== 'undefined') {toReturn['totalPages'] = this.totalPages;}
        if (typeof this.auditStatus !== 'undefined') {toReturn['auditStatus'] = this.auditStatus;}
        if (typeof this.auditSummary !== 'undefined') {toReturn['auditSummary'] = this.auditSummary;}
        if (typeof this.auditPages !== 'undefined' && this.auditPages !== null) {toReturn['auditPages'] = 'toApiJson' in this.auditPages ? (this.auditPages as any).toApiJson() : this.auditPages;}
        if (typeof this.auditScoreResults !== 'undefined' && this.auditScoreResults !== null) {toReturn['auditScoreResults'] = 'toApiJson' in this.auditScoreResults ? (this.auditScoreResults as any).toApiJson() : this.auditScoreResults;}
        if (typeof this.keyword !== 'undefined') {toReturn['keyword'] = this.keyword;}
        if (typeof this.auditId !== 'undefined') {toReturn['auditId'] = this.auditId;}
        if (typeof this.auditUrl !== 'undefined') {toReturn['auditUrl'] = this.auditUrl;}
        return toReturn;
    }
}

export class AddonActivation implements i.AddonActivationInterface {
    businessId: string;
    appId: string;
    addonId: string;
    activated: Date;
    deactivated: Date;
    status: e.AddonActivationAddonActivationStatus;
    isTrial: boolean;
    count: number;

    static fromProto(proto: any): AddonActivation {
        let m = new AddonActivation();
        m = Object.assign(m, proto);
        if (proto.activated) {m.activated = new Date(proto.activated);}
        if (proto.deactivated) {m.deactivated = new Date(proto.deactivated);}
        if (proto.status) {m.status = enumStringToValue<e.AddonActivationAddonActivationStatus>(e.AddonActivationAddonActivationStatus, proto.status);}
        return m;
    }

    constructor(kwargs?: i.AddonActivationInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.businessId !== 'undefined') {toReturn['businessId'] = this.businessId;}
        if (typeof this.appId !== 'undefined') {toReturn['appId'] = this.appId;}
        if (typeof this.addonId !== 'undefined') {toReturn['addonId'] = this.addonId;}
        if (typeof this.activated !== 'undefined' && this.activated !== null) {toReturn['activated'] = 'toApiJson' in this.activated ? (this.activated as any).toApiJson() : this.activated;}
        if (typeof this.deactivated !== 'undefined' && this.deactivated !== null) {toReturn['deactivated'] = 'toApiJson' in this.deactivated ? (this.deactivated as any).toApiJson() : this.deactivated;}
        if (typeof this.status !== 'undefined') {toReturn['status'] = this.status;}
        if (typeof this.isTrial !== 'undefined') {toReturn['isTrial'] = this.isTrial;}
        if (typeof this.count !== 'undefined') {toReturn['count'] = this.count;}
        return toReturn;
    }
}

export class AuditPageData implements i.AuditPageDataInterface {
    pageUrl: string;
    pageData: string;

    static fromProto(proto: any): AuditPageData {
        let m = new AuditPageData();
        m = Object.assign(m, proto);
        return m;
    }

    constructor(kwargs?: i.AuditPageDataInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.pageUrl !== 'undefined') {toReturn['pageUrl'] = this.pageUrl;}
        if (typeof this.pageData !== 'undefined') {toReturn['pageData'] = this.pageData;}
        return toReturn;
    }
}

export class AuditScoreResults implements i.AuditScoreResultsInterface {
    auditPageUrl: string;
    auditScores: AuditScores[];

    static fromProto(proto: any): AuditScoreResults {
        let m = new AuditScoreResults();
        m = Object.assign(m, proto);
        if (proto.auditScores) {m.auditScores = proto.auditScores.map(AuditScores.fromProto);}
        return m;
    }

    constructor(kwargs?: i.AuditScoreResultsInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.auditPageUrl !== 'undefined') {toReturn['auditPageUrl'] = this.auditPageUrl;}
        if (typeof this.auditScores !== 'undefined' && this.auditScores !== null) {toReturn['auditScores'] = 'toApiJson' in this.auditScores ? (this.auditScores as any).toApiJson() : this.auditScores;}
        return toReturn;
    }
}

export class AuditScores implements i.AuditScoresInterface {
    auditScoreScopeName: string;
    auditScoreScopeValue: number;
    auditScoreScopeSummary: string[];
    auditScoreRecommendations: string[];

    static fromProto(proto: any): AuditScores {
        let m = new AuditScores();
        m = Object.assign(m, proto);
        if (proto.auditScoreScopeValue) {m.auditScoreScopeValue = parseInt(proto.auditScoreScopeValue, 10);}
        return m;
    }

    constructor(kwargs?: i.AuditScoresInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.auditScoreScopeName !== 'undefined') {toReturn['auditScoreScopeName'] = this.auditScoreScopeName;}
        if (typeof this.auditScoreScopeValue !== 'undefined') {toReturn['auditScoreScopeValue'] = this.auditScoreScopeValue;}
        if (typeof this.auditScoreScopeSummary !== 'undefined') {toReturn['auditScoreScopeSummary'] = this.auditScoreScopeSummary;}
        if (typeof this.auditScoreRecommendations !== 'undefined') {toReturn['auditScoreRecommendations'] = this.auditScoreRecommendations;}
        return toReturn;
    }
}

export class BusinessKeywords implements i.BusinessKeywordsInterface {
    businessId: string;
    keywords: string[];

    static fromProto(proto: any): BusinessKeywords {
        let m = new BusinessKeywords();
        m = Object.assign(m, proto);
        return m;
    }

    constructor(kwargs?: i.BusinessKeywordsInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.businessId !== 'undefined') {toReturn['businessId'] = this.businessId;}
        if (typeof this.keywords !== 'undefined') {toReturn['keywords'] = this.keywords;}
        return toReturn;
    }
}

export class GetAIOAuditRequest implements i.GetAIOAuditRequestInterface {
    businessId: string;
    brandName: string;
    websiteUrl: string;

    static fromProto(proto: any): GetAIOAuditRequest {
        let m = new GetAIOAuditRequest();
        m = Object.assign(m, proto);
        return m;
    }

    constructor(kwargs?: i.GetAIOAuditRequestInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.businessId !== 'undefined') {toReturn['businessId'] = this.businessId;}
        if (typeof this.brandName !== 'undefined') {toReturn['brandName'] = this.brandName;}
        if (typeof this.websiteUrl !== 'undefined') {toReturn['websiteUrl'] = this.websiteUrl;}
        return toReturn;
    }
}

export class GetAIOAuditResponse implements i.GetAIOAuditResponseInterface {
    audit: AIOAuditResults;

    static fromProto(proto: any): GetAIOAuditResponse {
        let m = new GetAIOAuditResponse();
        m = Object.assign(m, proto);
        if (proto.audit) {m.audit = AIOAuditResults.fromProto(proto.audit);}
        return m;
    }

    constructor(kwargs?: i.GetAIOAuditResponseInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.audit !== 'undefined' && this.audit !== null) {toReturn['audit'] = 'toApiJson' in this.audit ? (this.audit as any).toApiJson() : this.audit;}
        return toReturn;
    }
}

export class GetAIOAuditStatusRequest implements i.GetAIOAuditStatusRequestInterface {
    businessId: string;
    brandName: string;
    websiteUrl: string;

    static fromProto(proto: any): GetAIOAuditStatusRequest {
        let m = new GetAIOAuditStatusRequest();
        m = Object.assign(m, proto);
        return m;
    }

    constructor(kwargs?: i.GetAIOAuditStatusRequestInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.businessId !== 'undefined') {toReturn['businessId'] = this.businessId;}
        if (typeof this.brandName !== 'undefined') {toReturn['brandName'] = this.brandName;}
        if (typeof this.websiteUrl !== 'undefined') {toReturn['websiteUrl'] = this.websiteUrl;}
        return toReturn;
    }
}

export class GetAIOAuditStatusResponse implements i.GetAIOAuditStatusResponseInterface {
    auditStatus: string;

    static fromProto(proto: any): GetAIOAuditStatusResponse {
        let m = new GetAIOAuditStatusResponse();
        m = Object.assign(m, proto);
        return m;
    }

    constructor(kwargs?: i.GetAIOAuditStatusResponseInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.auditStatus !== 'undefined') {toReturn['auditStatus'] = this.auditStatus;}
        return toReturn;
    }
}

export class GetActiveSEOAddonsRequest implements i.GetActiveSEOAddonsRequestInterface {
    businessId: string;

    static fromProto(proto: any): GetActiveSEOAddonsRequest {
        let m = new GetActiveSEOAddonsRequest();
        m = Object.assign(m, proto);
        return m;
    }

    constructor(kwargs?: i.GetActiveSEOAddonsRequestInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.businessId !== 'undefined') {toReturn['businessId'] = this.businessId;}
        return toReturn;
    }
}

export class GetActiveSEOAddonsResponse implements i.GetActiveSEOAddonsResponseInterface {
    activeAddons: AddonActivation[];

    static fromProto(proto: any): GetActiveSEOAddonsResponse {
        let m = new GetActiveSEOAddonsResponse();
        m = Object.assign(m, proto);
        if (proto.activeAddons) {m.activeAddons = proto.activeAddons.map(AddonActivation.fromProto);}
        return m;
    }

    constructor(kwargs?: i.GetActiveSEOAddonsResponseInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.activeAddons !== 'undefined' && this.activeAddons !== null) {toReturn['activeAddons'] = 'toApiJson' in this.activeAddons ? (this.activeAddons as any).toApiJson() : this.activeAddons;}
        return toReturn;
    }
}

export class GetAllAIOAuditRequest implements i.GetAllAIOAuditRequestInterface {
    businessId: string;

    static fromProto(proto: any): GetAllAIOAuditRequest {
        let m = new GetAllAIOAuditRequest();
        m = Object.assign(m, proto);
        return m;
    }

    constructor(kwargs?: i.GetAllAIOAuditRequestInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.businessId !== 'undefined') {toReturn['businessId'] = this.businessId;}
        return toReturn;
    }
}

export class GetAllAIOAuditResponse implements i.GetAllAIOAuditResponseInterface {
    audit: AIOAuditResults[];

    static fromProto(proto: any): GetAllAIOAuditResponse {
        let m = new GetAllAIOAuditResponse();
        m = Object.assign(m, proto);
        if (proto.audit) {m.audit = proto.audit.map(AIOAuditResults.fromProto);}
        return m;
    }

    constructor(kwargs?: i.GetAllAIOAuditResponseInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.audit !== 'undefined' && this.audit !== null) {toReturn['audit'] = 'toApiJson' in this.audit ? (this.audit as any).toApiJson() : this.audit;}
        return toReturn;
    }
}

export class GetAllAIOAuditScoreResultsRequest implements i.GetAllAIOAuditScoreResultsRequestInterface {
    businessId: string;
    brandName: string;
    websiteUrl: string;

    static fromProto(proto: any): GetAllAIOAuditScoreResultsRequest {
        let m = new GetAllAIOAuditScoreResultsRequest();
        m = Object.assign(m, proto);
        return m;
    }

    constructor(kwargs?: i.GetAllAIOAuditScoreResultsRequestInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.businessId !== 'undefined') {toReturn['businessId'] = this.businessId;}
        if (typeof this.brandName !== 'undefined') {toReturn['brandName'] = this.brandName;}
        if (typeof this.websiteUrl !== 'undefined') {toReturn['websiteUrl'] = this.websiteUrl;}
        return toReturn;
    }
}

export class GetAllAIOAuditScoreResultsResponse implements i.GetAllAIOAuditScoreResultsResponseInterface {
    auditScoreResults: AuditScoreResults[];

    static fromProto(proto: any): GetAllAIOAuditScoreResultsResponse {
        let m = new GetAllAIOAuditScoreResultsResponse();
        m = Object.assign(m, proto);
        if (proto.auditScoreResults) {m.auditScoreResults = proto.auditScoreResults.map(AuditScoreResults.fromProto);}
        return m;
    }

    constructor(kwargs?: i.GetAllAIOAuditScoreResultsResponseInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.auditScoreResults !== 'undefined' && this.auditScoreResults !== null) {toReturn['auditScoreResults'] = 'toApiJson' in this.auditScoreResults ? (this.auditScoreResults as any).toApiJson() : this.auditScoreResults;}
        return toReturn;
    }
}

export class GetDataForSEOCategoryRequest implements i.GetDataForSEOCategoryRequestInterface {
    businessId: string;

    static fromProto(proto: any): GetDataForSEOCategoryRequest {
        let m = new GetDataForSEOCategoryRequest();
        m = Object.assign(m, proto);
        return m;
    }

    constructor(kwargs?: i.GetDataForSEOCategoryRequestInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.businessId !== 'undefined') {toReturn['businessId'] = this.businessId;}
        return toReturn;
    }
}

export class GetDataForSEOCategoryResponse implements i.GetDataForSEOCategoryResponseInterface {
    businessId: string;
    primaryCategoryId: string;
    categoryIds: string[];
    taskId: string;
    rawResponse: string;
    created: Date;
    updated: Date;
    deleted: Date;

    static fromProto(proto: any): GetDataForSEOCategoryResponse {
        let m = new GetDataForSEOCategoryResponse();
        m = Object.assign(m, proto);
        if (proto.created) {m.created = new Date(proto.created);}
        if (proto.updated) {m.updated = new Date(proto.updated);}
        if (proto.deleted) {m.deleted = new Date(proto.deleted);}
        return m;
    }

    constructor(kwargs?: i.GetDataForSEOCategoryResponseInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.businessId !== 'undefined') {toReturn['businessId'] = this.businessId;}
        if (typeof this.primaryCategoryId !== 'undefined') {toReturn['primaryCategoryId'] = this.primaryCategoryId;}
        if (typeof this.categoryIds !== 'undefined') {toReturn['categoryIds'] = this.categoryIds;}
        if (typeof this.taskId !== 'undefined') {toReturn['taskId'] = this.taskId;}
        if (typeof this.rawResponse !== 'undefined') {toReturn['rawResponse'] = this.rawResponse;}
        if (typeof this.created !== 'undefined' && this.created !== null) {toReturn['created'] = 'toApiJson' in this.created ? (this.created as any).toApiJson() : this.created;}
        if (typeof this.updated !== 'undefined' && this.updated !== null) {toReturn['updated'] = 'toApiJson' in this.updated ? (this.updated as any).toApiJson() : this.updated;}
        if (typeof this.deleted !== 'undefined' && this.deleted !== null) {toReturn['deleted'] = 'toApiJson' in this.deleted ? (this.deleted as any).toApiJson() : this.deleted;}
        return toReturn;
    }
}

export class GetLocalSearchSEODataRequest implements i.GetLocalSearchSEODataRequestInterface {
    businessId: string;
    keyword: string;
    startDate: Date;
    endDate: Date;

    static fromProto(proto: any): GetLocalSearchSEODataRequest {
        let m = new GetLocalSearchSEODataRequest();
        m = Object.assign(m, proto);
        if (proto.startDate) {m.startDate = new Date(proto.startDate);}
        if (proto.endDate) {m.endDate = new Date(proto.endDate);}
        return m;
    }

    constructor(kwargs?: i.GetLocalSearchSEODataRequestInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.businessId !== 'undefined') {toReturn['businessId'] = this.businessId;}
        if (typeof this.keyword !== 'undefined') {toReturn['keyword'] = this.keyword;}
        if (typeof this.startDate !== 'undefined' && this.startDate !== null) {toReturn['startDate'] = 'toApiJson' in this.startDate ? (this.startDate as any).toApiJson() : this.startDate;}
        if (typeof this.endDate !== 'undefined' && this.endDate !== null) {toReturn['endDate'] = 'toApiJson' in this.endDate ? (this.endDate as any).toApiJson() : this.endDate;}
        return toReturn;
    }
}

export class GetLocalSearchSEODataResponse implements i.GetLocalSearchSEODataResponseInterface {
    keyword: string;
    localSearchData: LocalSearchData[];

    static fromProto(proto: any): GetLocalSearchSEODataResponse {
        let m = new GetLocalSearchSEODataResponse();
        m = Object.assign(m, proto);
        if (proto.localSearchData) {m.localSearchData = proto.localSearchData.map(LocalSearchData.fromProto);}
        return m;
    }

    constructor(kwargs?: i.GetLocalSearchSEODataResponseInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.keyword !== 'undefined') {toReturn['keyword'] = this.keyword;}
        if (typeof this.localSearchData !== 'undefined' && this.localSearchData !== null) {toReturn['localSearchData'] = 'toApiJson' in this.localSearchData ? (this.localSearchData as any).toApiJson() : this.localSearchData;}
        return toReturn;
    }
}

export class GetSEODataRequest implements i.GetSEODataRequestInterface {
    businessId: string;
    keywords: string[];
    startDate: Date;
    endDate: Date;

    static fromProto(proto: any): GetSEODataRequest {
        let m = new GetSEODataRequest();
        m = Object.assign(m, proto);
        if (proto.startDate) {m.startDate = new Date(proto.startDate);}
        if (proto.endDate) {m.endDate = new Date(proto.endDate);}
        return m;
    }

    constructor(kwargs?: i.GetSEODataRequestInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.businessId !== 'undefined') {toReturn['businessId'] = this.businessId;}
        if (typeof this.keywords !== 'undefined') {toReturn['keywords'] = this.keywords;}
        if (typeof this.startDate !== 'undefined' && this.startDate !== null) {toReturn['startDate'] = 'toApiJson' in this.startDate ? (this.startDate as any).toApiJson() : this.startDate;}
        if (typeof this.endDate !== 'undefined' && this.endDate !== null) {toReturn['endDate'] = 'toApiJson' in this.endDate ? (this.endDate as any).toApiJson() : this.endDate;}
        return toReturn;
    }
}

export class GetSEODataResponse implements i.GetSEODataResponseInterface {
    data: SEOData[];
    previousData: SEOData[];

    static fromProto(proto: any): GetSEODataResponse {
        let m = new GetSEODataResponse();
        m = Object.assign(m, proto);
        if (proto.data) {m.data = proto.data.map(SEOData.fromProto);}
        if (proto.previousData) {m.previousData = proto.previousData.map(SEOData.fromProto);}
        return m;
    }

    constructor(kwargs?: i.GetSEODataResponseInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.data !== 'undefined' && this.data !== null) {toReturn['data'] = 'toApiJson' in this.data ? (this.data as any).toApiJson() : this.data;}
        if (typeof this.previousData !== 'undefined' && this.previousData !== null) {toReturn['previousData'] = 'toApiJson' in this.previousData ? (this.previousData as any).toApiJson() : this.previousData;}
        return toReturn;
    }
}

export class GetSEODataSummaryRequest implements i.GetSEODataSummaryRequestInterface {
    businessId: string;
    keywords: string[];
    startDate: Date;
    endDate: Date;

    static fromProto(proto: any): GetSEODataSummaryRequest {
        let m = new GetSEODataSummaryRequest();
        m = Object.assign(m, proto);
        if (proto.startDate) {m.startDate = new Date(proto.startDate);}
        if (proto.endDate) {m.endDate = new Date(proto.endDate);}
        return m;
    }

    constructor(kwargs?: i.GetSEODataSummaryRequestInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.businessId !== 'undefined') {toReturn['businessId'] = this.businessId;}
        if (typeof this.keywords !== 'undefined') {toReturn['keywords'] = this.keywords;}
        if (typeof this.startDate !== 'undefined' && this.startDate !== null) {toReturn['startDate'] = 'toApiJson' in this.startDate ? (this.startDate as any).toApiJson() : this.startDate;}
        if (typeof this.endDate !== 'undefined' && this.endDate !== null) {toReturn['endDate'] = 'toApiJson' in this.endDate ? (this.endDate as any).toApiJson() : this.endDate;}
        return toReturn;
    }
}

export class GetSEODataSummaryResponse implements i.GetSEODataSummaryResponseInterface {
    data: SEODataSummary[];
    previousData: SEODataSummary[];

    static fromProto(proto: any): GetSEODataSummaryResponse {
        let m = new GetSEODataSummaryResponse();
        m = Object.assign(m, proto);
        if (proto.data) {m.data = proto.data.map(SEODataSummary.fromProto);}
        if (proto.previousData) {m.previousData = proto.previousData.map(SEODataSummary.fromProto);}
        return m;
    }

    constructor(kwargs?: i.GetSEODataSummaryResponseInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.data !== 'undefined' && this.data !== null) {toReturn['data'] = 'toApiJson' in this.data ? (this.data as any).toApiJson() : this.data;}
        if (typeof this.previousData !== 'undefined' && this.previousData !== null) {toReturn['previousData'] = 'toApiJson' in this.previousData ? (this.previousData as any).toApiJson() : this.previousData;}
        return toReturn;
    }
}

export class GetSEOSettingsRequest implements i.GetSEOSettingsRequestInterface {
    businessId: string;

    static fromProto(proto: any): GetSEOSettingsRequest {
        let m = new GetSEOSettingsRequest();
        m = Object.assign(m, proto);
        return m;
    }

    constructor(kwargs?: i.GetSEOSettingsRequestInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.businessId !== 'undefined') {toReturn['businessId'] = this.businessId;}
        return toReturn;
    }
}

export class LocalSearchData implements i.LocalSearchDataInterface {
    keyword: string;
    vicinity: e.Vicinity;
    searchLocation: Geo;
    results: LocalSearchResult[];

    static fromProto(proto: any): LocalSearchData {
        let m = new LocalSearchData();
        m = Object.assign(m, proto);
        if (proto.vicinity) {m.vicinity = enumStringToValue<e.Vicinity>(e.Vicinity, proto.vicinity);}
        if (proto.searchLocation) {m.searchLocation = Geo.fromProto(proto.searchLocation);}
        if (proto.results) {m.results = proto.results.map(LocalSearchResult.fromProto);}
        return m;
    }

    constructor(kwargs?: i.LocalSearchDataInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.keyword !== 'undefined') {toReturn['keyword'] = this.keyword;}
        if (typeof this.vicinity !== 'undefined') {toReturn['vicinity'] = this.vicinity;}
        if (typeof this.searchLocation !== 'undefined' && this.searchLocation !== null) {toReturn['searchLocation'] = 'toApiJson' in this.searchLocation ? (this.searchLocation as any).toApiJson() : this.searchLocation;}
        if (typeof this.results !== 'undefined' && this.results !== null) {toReturn['results'] = 'toApiJson' in this.results ? (this.results as any).toApiJson() : this.results;}
        return toReturn;
    }
}

export class LocalSearchResult implements i.LocalSearchResultInterface {
    businessName: string;
    address: string;
    url: string;
    rank: string;
    isMainBusiness: boolean;
    reviews: LocalSearchReviews;
    phoneNumber: string;
    claimStatus: e.GBPClaimStatus;

    static fromProto(proto: any): LocalSearchResult {
        let m = new LocalSearchResult();
        m = Object.assign(m, proto);
        if (proto.reviews) {m.reviews = LocalSearchReviews.fromProto(proto.reviews);}
        if (proto.claimStatus) {m.claimStatus = enumStringToValue<e.GBPClaimStatus>(e.GBPClaimStatus, proto.claimStatus);}
        return m;
    }

    constructor(kwargs?: i.LocalSearchResultInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.businessName !== 'undefined') {toReturn['businessName'] = this.businessName;}
        if (typeof this.address !== 'undefined') {toReturn['address'] = this.address;}
        if (typeof this.url !== 'undefined') {toReturn['url'] = this.url;}
        if (typeof this.rank !== 'undefined') {toReturn['rank'] = this.rank;}
        if (typeof this.isMainBusiness !== 'undefined') {toReturn['isMainBusiness'] = this.isMainBusiness;}
        if (typeof this.reviews !== 'undefined' && this.reviews !== null) {toReturn['reviews'] = 'toApiJson' in this.reviews ? (this.reviews as any).toApiJson() : this.reviews;}
        if (typeof this.phoneNumber !== 'undefined') {toReturn['phoneNumber'] = this.phoneNumber;}
        if (typeof this.claimStatus !== 'undefined') {toReturn['claimStatus'] = this.claimStatus;}
        return toReturn;
    }
}

export class LocalSearchReviews implements i.LocalSearchReviewsInterface {
    rating: number;
    count: string;

    static fromProto(proto: any): LocalSearchReviews {
        let m = new LocalSearchReviews();
        m = Object.assign(m, proto);
        return m;
    }

    constructor(kwargs?: i.LocalSearchReviewsInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.rating !== 'undefined') {toReturn['rating'] = this.rating;}
        if (typeof this.count !== 'undefined') {toReturn['count'] = this.count;}
        return toReturn;
    }
}

export class SEOData implements i.SEODataInterface {
    keyword: string;
    date: Date;
    localRank: number;
    organicRank: number;
    difficulty: number;
    searchVolume: number;
    localSearches: LocalSearchData[];
    searchRadius: number;
    workflowUrl: string;

    static fromProto(proto: any): SEOData {
        let m = new SEOData();
        m = Object.assign(m, proto);
        if (proto.date) {m.date = new Date(proto.date);}
        if (proto.difficulty) {m.difficulty = parseInt(proto.difficulty, 10);}
        if (proto.searchVolume) {m.searchVolume = parseInt(proto.searchVolume, 10);}
        if (proto.localSearches) {m.localSearches = proto.localSearches.map(LocalSearchData.fromProto);}
        return m;
    }

    constructor(kwargs?: i.SEODataInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.keyword !== 'undefined') {toReturn['keyword'] = this.keyword;}
        if (typeof this.date !== 'undefined' && this.date !== null) {toReturn['date'] = 'toApiJson' in this.date ? (this.date as any).toApiJson() : this.date;}
        if (typeof this.localRank !== 'undefined') {toReturn['localRank'] = this.localRank;}
        if (typeof this.organicRank !== 'undefined') {toReturn['organicRank'] = this.organicRank;}
        if (typeof this.difficulty !== 'undefined') {toReturn['difficulty'] = this.difficulty;}
        if (typeof this.searchVolume !== 'undefined') {toReturn['searchVolume'] = this.searchVolume;}
        if (typeof this.localSearches !== 'undefined' && this.localSearches !== null) {toReturn['localSearches'] = 'toApiJson' in this.localSearches ? (this.localSearches as any).toApiJson() : this.localSearches;}
        if (typeof this.searchRadius !== 'undefined') {toReturn['searchRadius'] = this.searchRadius;}
        if (typeof this.workflowUrl !== 'undefined') {toReturn['workflowUrl'] = this.workflowUrl;}
        return toReturn;
    }
}

export class SEODataSummary implements i.SEODataSummaryInterface {
    keyword: string;
    date: Date;
    localRank: number;
    organicRank: number;
    difficulty: number;
    searchVolume: number;
    searchRadius: number;
    workflowUrl: string;

    static fromProto(proto: any): SEODataSummary {
        let m = new SEODataSummary();
        m = Object.assign(m, proto);
        if (proto.date) {m.date = new Date(proto.date);}
        if (proto.difficulty) {m.difficulty = parseInt(proto.difficulty, 10);}
        if (proto.searchVolume) {m.searchVolume = parseInt(proto.searchVolume, 10);}
        return m;
    }

    constructor(kwargs?: i.SEODataSummaryInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.keyword !== 'undefined') {toReturn['keyword'] = this.keyword;}
        if (typeof this.date !== 'undefined' && this.date !== null) {toReturn['date'] = 'toApiJson' in this.date ? (this.date as any).toApiJson() : this.date;}
        if (typeof this.localRank !== 'undefined') {toReturn['localRank'] = this.localRank;}
        if (typeof this.organicRank !== 'undefined') {toReturn['organicRank'] = this.organicRank;}
        if (typeof this.difficulty !== 'undefined') {toReturn['difficulty'] = this.difficulty;}
        if (typeof this.searchVolume !== 'undefined') {toReturn['searchVolume'] = this.searchVolume;}
        if (typeof this.searchRadius !== 'undefined') {toReturn['searchRadius'] = this.searchRadius;}
        if (typeof this.workflowUrl !== 'undefined') {toReturn['workflowUrl'] = this.workflowUrl;}
        return toReturn;
    }
}

export class SEOSettingsResponse implements i.SEOSettingsResponseInterface {
    businessId: string;
    localSearchRadius: number;
    favoriteKeywords: string[];
    isFullSearchEnabled: boolean;

    static fromProto(proto: any): SEOSettingsResponse {
        let m = new SEOSettingsResponse();
        m = Object.assign(m, proto);
        return m;
    }

    constructor(kwargs?: i.SEOSettingsResponseInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.businessId !== 'undefined') {toReturn['businessId'] = this.businessId;}
        if (typeof this.localSearchRadius !== 'undefined') {toReturn['localSearchRadius'] = this.localSearchRadius;}
        if (typeof this.favoriteKeywords !== 'undefined') {toReturn['favoriteKeywords'] = this.favoriteKeywords;}
        if (typeof this.isFullSearchEnabled !== 'undefined') {toReturn['isFullSearchEnabled'] = this.isFullSearchEnabled;}
        return toReturn;
    }
}

export class SaveSEOSettingsRequest implements i.SaveSEOSettingsRequestInterface {
    businessId: string;
    localSearchRadius: number;
    favoriteKeywords: string[];
    fieldMask: FieldMask;
    isFullSearchEnabled: boolean;

    static fromProto(proto: any): SaveSEOSettingsRequest {
        let m = new SaveSEOSettingsRequest();
        m = Object.assign(m, proto);
        if (proto.fieldMask) {m.fieldMask = FieldMask.fromProto(proto.fieldMask);}
        return m;
    }

    constructor(kwargs?: i.SaveSEOSettingsRequestInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.businessId !== 'undefined') {toReturn['businessId'] = this.businessId;}
        if (typeof this.localSearchRadius !== 'undefined') {toReturn['localSearchRadius'] = this.localSearchRadius;}
        if (typeof this.favoriteKeywords !== 'undefined') {toReturn['favoriteKeywords'] = this.favoriteKeywords;}
        if (typeof this.fieldMask !== 'undefined' && this.fieldMask !== null) {toReturn['fieldMask'] = 'toApiJson' in this.fieldMask ? (this.fieldMask as any).toApiJson() : this.fieldMask;}
        if (typeof this.isFullSearchEnabled !== 'undefined') {toReturn['isFullSearchEnabled'] = this.isFullSearchEnabled;}
        return toReturn;
    }
}

export class StartLocalSEODataWorkflowRequest implements i.StartLocalSEODataWorkflowRequestInterface {
    businesses: BusinessKeywords[];
    forceSerpWorkflow: boolean;
    date: Date;
    forceKeywordInfoWorkflow: boolean;
    forceSuggestedKeywordsWorkflow: boolean;
    ignoreDataLakeResults: boolean;

    static fromProto(proto: any): StartLocalSEODataWorkflowRequest {
        let m = new StartLocalSEODataWorkflowRequest();
        m = Object.assign(m, proto);
        if (proto.businesses) {m.businesses = proto.businesses.map(BusinessKeywords.fromProto);}
        if (proto.date) {m.date = new Date(proto.date);}
        return m;
    }

    constructor(kwargs?: i.StartLocalSEODataWorkflowRequestInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.businesses !== 'undefined' && this.businesses !== null) {toReturn['businesses'] = 'toApiJson' in this.businesses ? (this.businesses as any).toApiJson() : this.businesses;}
        if (typeof this.forceSerpWorkflow !== 'undefined') {toReturn['forceSerpWorkflow'] = this.forceSerpWorkflow;}
        if (typeof this.date !== 'undefined' && this.date !== null) {toReturn['date'] = 'toApiJson' in this.date ? (this.date as any).toApiJson() : this.date;}
        if (typeof this.forceKeywordInfoWorkflow !== 'undefined') {toReturn['forceKeywordInfoWorkflow'] = this.forceKeywordInfoWorkflow;}
        if (typeof this.forceSuggestedKeywordsWorkflow !== 'undefined') {toReturn['forceSuggestedKeywordsWorkflow'] = this.forceSuggestedKeywordsWorkflow;}
        if (typeof this.ignoreDataLakeResults !== 'undefined') {toReturn['ignoreDataLakeResults'] = this.ignoreDataLakeResults;}
        return toReturn;
    }
}

export class StartSEOCategoryWorkflowRequest implements i.StartSEOCategoryWorkflowRequestInterface {
    businessIds: string[];

    static fromProto(proto: any): StartSEOCategoryWorkflowRequest {
        let m = new StartSEOCategoryWorkflowRequest();
        m = Object.assign(m, proto);
        return m;
    }

    constructor(kwargs?: i.StartSEOCategoryWorkflowRequestInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.businessIds !== 'undefined') {toReturn['businessIds'] = this.businessIds;}
        return toReturn;
    }
}

export class TriggerAIOAuditRequest implements i.TriggerAIOAuditRequestInterface {
    businessId: string;
    brandName: string;
    websiteUrl: string;

    static fromProto(proto: any): TriggerAIOAuditRequest {
        let m = new TriggerAIOAuditRequest();
        m = Object.assign(m, proto);
        return m;
    }

    constructor(kwargs?: i.TriggerAIOAuditRequestInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.businessId !== 'undefined') {toReturn['businessId'] = this.businessId;}
        if (typeof this.brandName !== 'undefined') {toReturn['brandName'] = this.brandName;}
        if (typeof this.websiteUrl !== 'undefined') {toReturn['websiteUrl'] = this.websiteUrl;}
        return toReturn;
    }
}

export class TriggerAIOAuditResponse implements i.TriggerAIOAuditResponseInterface {
    businessId: string;
    brandName: string;
    websiteUrl: string;
    message: string;
    auditStatus: string;

    static fromProto(proto: any): TriggerAIOAuditResponse {
        let m = new TriggerAIOAuditResponse();
        m = Object.assign(m, proto);
        return m;
    }

    constructor(kwargs?: i.TriggerAIOAuditResponseInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.businessId !== 'undefined') {toReturn['businessId'] = this.businessId;}
        if (typeof this.brandName !== 'undefined') {toReturn['brandName'] = this.brandName;}
        if (typeof this.websiteUrl !== 'undefined') {toReturn['websiteUrl'] = this.websiteUrl;}
        if (typeof this.message !== 'undefined') {toReturn['message'] = this.message;}
        if (typeof this.auditStatus !== 'undefined') {toReturn['auditStatus'] = this.auditStatus;}
        return toReturn;
    }
}

