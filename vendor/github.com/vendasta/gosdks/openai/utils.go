package openai

import (
	"context"
	"github.com/vendasta/gosdks/vax"
	"google.golang.org/grpc/codes"
	"time"
)

type retryerWithMaxRetries struct {
	maxRetries  int
	numRetries  int
	codeRetrier vax.Retryer
}

func (r *retryerWithMaxRetries) Retry(ctx context.Context, err error) (pause time.Duration, shouldRetry bool) {
	if r.numRetries >= r.maxRetries {
		return 0, false
	}
	r.numRetries++
	return r.codeRetrier.Retry(ctx, err)
}

func retryer(maxRetries int) vax.CallOption {
	onCodes := vax.OnCodes([]codes.Code{
		codes.Unknown,
	}, vax.Backoff{
		Initial:    100 * time.Millisecond,
		Max:        time.Second * 1,
		Multiplier: 2,
	})

	untilMax := &retryerWithMaxRetries{
		maxRetries:  maxRetries,
		codeRetrier: onCodes,
	}

	return vax.WithRetry(func() vax.Retryer {
		return untilMax
	})
}
