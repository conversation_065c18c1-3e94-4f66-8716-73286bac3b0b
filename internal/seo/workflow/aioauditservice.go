package seoworkflow

import (
	"context"
	"fmt"
	"time"

	"go.temporal.io/api/enums/v1"
	"go.temporal.io/sdk/client"
	"go.temporal.io/sdk/temporal"

	"github.com/google/uuid"
	"github.com/vendasta/listing-products/internal/constants"
	"github.com/vendasta/listing-products/internal/temporalwrapper"
)

type AIOAuditService struct {
	temporal temporalwrapper.TemporalWorkflowExecutor
}

func NewAIOAuditWorkflowService(temporal temporalwrapper.TemporalWorkflowExecutor) *AIOAuditService {
	return &AIOAuditService{
		temporal: temporal,
	}
}

func (s *AIOAuditService) StartAIOAuditWorkflow(ctx context.Context, businessID string, website string, businessName string, auditDate string) error {
	uuid := uuid.New().String()
	id := fmt.Sprintf("AIOAuditWorkflow-%s-%s-%s", businessID, time.Now().Format("2006-01-02"), uuid)

	options := client.StartWorkflowOptions{
		ID:                                       id,
		TaskQueue:                                constants.AIOAuditTaskList,
		WorkflowExecutionTimeout:                 time.Hour * 24 * 7,
		WorkflowIDReusePolicy:                    enums.WORKFLOW_ID_REUSE_POLICY_ALLOW_DUPLICATE,
		WorkflowExecutionErrorWhenAlreadyStarted: false,
		RetryPolicy: &temporal.RetryPolicy{
			InitialInterval:    time.Minute * 5,
			BackoffCoefficient: 2.0,
			MaximumInterval:    time.Hour,
			MaximumAttempts:    3,
		},
	}

	_, err := s.temporal.ExecuteWorkflow(ctx, options, AIOAuditWorkflow, &AIOAuditWorkflowParams{
		BusinessID:   businessID,
		Website:      website,
		BusinessName: businessName,
		AuditDate:    auditDate,
	})

	return err
}
