package openai

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"reflect"
	"strings"
	"time"

	jsonrepair "github.com/RealAlexandreAI/json-repair"
	"github.com/vendasta/gosdks/validation"
	"github.com/vendasta/gosdks/validation/rules"
	"github.com/vendasta/gosdks/vax"
	"github.com/vendasta/gosdks/verrors"

	"github.com/vendasta/gosdks/logging"
	"github.com/vendasta/gosdks/openai/jsonschema"

	secretmanager "cloud.google.com/go/secretmanager/apiv1"
	ddstatsd "github.com/DataDog/datadog-go/v5/statsd"
	"github.com/pkoukk/tiktoken-go"
	tiktokenloader "github.com/pkoukk/tiktoken-go-loader"
	gogpt "github.com/sashabaranov/go-openai"
	"github.com/vendasta/IAM/sdks/go/iaminterceptor"
	"github.com/vendasta/gosdks/config"
	"github.com/vendasta/gosdks/statsd"
)

const (
	// ChatMessagePartTypeText informs OpenAI that the message part is a text
	ChatMessagePartTypeText ChatMessagePartType = "text"
	// ChatMessagePartTypeImageURL informs OpenAI that the message part is an image
	ChatMessagePartTypeImageURL ChatMessagePartType = "image_url"

	// ImageURLDetailHigh From the OpenAI Docs: 'high will enable "high res" mode, which first allows the model to first see the low res image (using 85 tokens) and then creates detailed crops using 170 tokens for each 512px x 512px tile.'
	ImageURLDetailHigh ImageURLDetail = "high"
	// ImageURLDetailLow From the OpenAI Docs: 'low will enable the "low res" mode. The model will receive a low-res 512px x 512px version of the image, and represent the image with a budget of 85 tokens. This allows the API to return faster responses and consume fewer input tokens for use cases that do not require high detail.'
	ImageURLDetailLow ImageURLDetail = "low"
	// ImageURLDetailAuto By default, the model will use the auto setting which will look at the image input size and decide if it should use the low or high setting
	ImageURLDetailAuto ImageURLDetail = "auto"
)

type (
	// Client is the interface to the OpenAI client
	Client interface {
		CreateChatCompletion(ctx context.Context, messages []ChatCompletionMessageRequest, opts ...ClientOption) (ChatCompletionMessageResponse, error)
		CreateImages(ctx context.Context, request CreateImagesRequest) ([]CreateImagesResponse, error)
		CreateImageVariations(ctx context.Context, request ImageVariationsRequest) ([]CreateImagesResponse, error)
		GetNumberOfTokens(request []ChatCompletionMessageRequest, opts ...ClientOption) (int, error)
		CreateEmbeddings(ctx context.Context, model EmbeddingModel, inputs []string) (CreateEmbeddingsResponse, error)
	}

	// OpenAIClient is the implementation of the Client interface for openai
	OpenAIClient struct {
		client *gogpt.Client
		statsd ddstatsd.ClientInterface
	}

	// 	Role of a message object (either "system", "user", "assistant", or "tool")
	Role string

	// Model to use for completion. Must be one of "gpt-3.5-turbo","gpt-4", "gpt-4o"
	Model string

	//ResponseFormat for completion. Must be one of "text" or "json_object"
	ResponseFormat string

	EmbeddingModel string

	// ToolType is the type of tool that the chat completion is requesting to call (currently only "function")
	ToolType string

	// FunctionDefinition is the definition
	FunctionDefinition struct {
		Name        string
		Description string
		Strict      bool
		// Parameters is a struct that describes the parameters that the function accepts
		// The struct should be annotated with description tags to describe each property
		// Properties that require a value should be annotated with the required tag
		Parameters interface{}
		// SchemaParameters describes the parameters that the function accepts in JSON Schema format
		// This can be used when it is not possible to provide a struct as Parameters
		// This cannot be provided if Parameters is provided
		SchemaParameters *jsonschema.Schema
	}

	// ToolDefinition is a tool definition that is being provided to the chat completion
	ToolDefinition struct {
		Type     ToolType           `json:"type"`
		Function FunctionDefinition `json:"function"`
	}

	// ToolCall is a call to a tool that the chat completion is requesting
	ToolCall struct {
		ID       string       `json:"id"`
		Type     ToolType     `json:"type"`
		Function FunctionCall `json:"function"`
	}

	// FunctionCall is the name and arguments of a function call that the chat completion is requesting
	FunctionCall struct {
		Name string `json:"name,omitempty"`
		// Arguments are filled in values of the same type as the parameters of the function definition, and should be used to call the function
		Arguments interface{} `json:"arguments,omitempty"`
	}

	// ImageURLDetail is the image level of detail
	ImageURLDetail string

	// ChatMessageImageURL is the image URL and Detail of a chat message. The URL can also contain a base64 encoded image bytes.
	ChatMessageImageURL struct {
		// URL is either the public URL of the image or a base64 encoded image bytes
		URL    string         `json:"url,omitempty"`
		Detail ImageURLDetail `json:"detail,omitempty"`
	}

	// ChatMessagePartType is the type of a chat message part
	ChatMessagePartType string

	// ChatMessagePart is the part of a chat message that can be used to send attachments, like images
	ChatMessagePart struct {
		Type     ChatMessagePartType  `json:"type,omitempty"`
		Text     string               `json:"text,omitempty"`
		ImageURL *ChatMessageImageURL `json:"image_url,omitempty"`
	}

	// ChatCompletionMessageRequest is the chat message to the OpenAI API chat completion API
	ChatCompletionMessageRequest struct {
		Role         string `json:"role"`
		Content      string `json:"content"`
		MultiContent []ChatMessagePart
		// For tool role prompts this should be set to the ID given in the assistant's prior request to call a tool.
		ToolCallID string `json:"tool_call_id,omitempty"`
		// For assistant role prompts this should be set to the tool calls generated by the model, such as function calls.
		ToolCalls []ToolCall `json:"tool_calls,omitempty"`
	}

	// ChatCompletionMessageResponse is the response from the OpenAI API chat completion API
	ChatCompletionMessageResponse struct {
		Role    Role   `json:"role"`
		Content string `json:"content"`
		// For assistant role prompts this may be set to the tool calls generated by the model, such as function calls.
		// If this is set, a new request should be made with the response message appended to the previous request messages,
		// followed by messages containing the function result for each function call requested
		// See README for an example and further information
		ToolCalls []ToolCall `json:"tool_calls,omitempty"`
		Usage     Usage      `json:"usage,omitempty"`
	}

	Usage struct {
		TotalTokens            int                     `json:"total_tokens"`
		PromptTokens           int                     `json:"prompt_tokens"`
		CompletionTokens       int                     `json:"completion_tokens"`
		PromptTokenDetails     *PromptTokenDetails     `json:"prompt_token_details,omitempty"`
		CompletionTokenDetails *CompletionTokenDetails `json:"completion_token_details,omitempty"`
	}

	PromptTokenDetails struct {
		CachedTokens int `json:"cached_tokens"`
		AudioTokens  int `json:"audio_tokens"`
	}

	CompletionTokenDetails struct {
		ReasoningTokens int `json:"reasoning_tokens"`
		AudioTokens     int `json:"audio_tokens"`
	}

	// CreateImagesRequest represents the request structure for the image API.
	CreateImagesRequest struct {
		// A text description of the desired image(s)
		Prompt string `json:"prompt,omitempty"`
		// The number of images to generate. Must be between 1 and 10.
		N int `json:"n,omitempty"`
		// The size of the generated images. Must be one of 256x256, 512x512, or 1024x1024
		Size string `json:"size,omitempty"`
		// The format in which the generated images are returned. Must be one of url or b64_json.
		ResponseFormat string `json:"response_format,omitempty"`
		// The model to use for image generation. Can be "dall-e-2", "dall-e-3", or "gpt-image-1"
		Model string `json:"model,omitempty"`
		// The quality of the generated images. Can be "standard" or "hd" for DALL-E models, or "high", "medium", "low" for GPT-image-1
		Quality string `json:"quality,omitempty"`
		// The style of the generated images. Can be "vivid" or "natural" (DALL-E-3 only)
		Style string `json:"style,omitempty"`
		// The background of the generated images. Can be "transparent" or "opaque" (GPT-image-1 only)
		Background string `json:"background,omitempty"`
		// The moderation level for content filtering. Can be "low" (GPT-image-1 only)
		Moderation string `json:"moderation,omitempty"`
		// The output compression level (1-100). Higher values mean more compression (GPT-image-1 only)
		OutputCompression int `json:"output_compression,omitempty"`
		// The output format. Can be "png", "jpeg", or "webp" (GPT-image-1 only)
		OutputFormat string `json:"output_format,omitempty"`
	}

	// ImageVariationsRequest represents the request structure for the image API.
	ImageVariationsRequest struct {
		//Image file to generate the variations
		Image *os.File `json:"image,omitempty"`
		// The number of images to generate. Must be between 1 and 10.
		N int `json:"n,omitempty"`
		// The size of the generated images. Must be one of 256x256, 512x512, or 1024x1024
		Size string `json:"size,omitempty"`
	}

	// CreateImagesResponse represents the images response from the create images API
	CreateImagesResponse struct {
		URL           string `json:"url"`
		B64JSON       string `json:"b64_json"`
		RevisedPrompt string `json:"revised_prompt,omitempty"`
	}

	CreateEmbeddingsRequest struct {
		Input string `json:"input"`
		Model string `json:"model"`
	}

	EmbeddingData struct {
		Object    string    `json:"object"`
		Embedding []float32 `json:"embedding"`
		Index     int       `json:"index"`
	}

	UsageData struct {
		PromptTokens int `json:"prompt_tokens"`
		TotalTokens  int `json:"total_tokens"`
	}

	CreateEmbeddingsResponse struct {
		Object string          `json:"object"`
		Data   []EmbeddingData `json:"data"`
		Model  EmbeddingModel  `json:"model"`
		Usage  UsageData       `json:"usage"`
	}

	// JSONSchemaDefinition represents a definition for generating JSON schema
	JSONSchemaDefinition struct {
		Name        string
		Description string
		Strict      bool
		// Parameters is a struct that describes the schema properties
		// The struct should be annotated with description tags to describe each property
		// Properties that require a value should be annotated with the required tag
		Parameters interface{}
		// SchemaParameters describes the schema in JSON Schema format
		// This can be used when it is not possible to provide a struct as Parameters
		// This cannot be provided if Parameters is provided
		SchemaParameters *jsonschema.Schema
	}
)

var (
	// System message helps set the behavior of the assistant
	System Role = "system"
	// User messages help instruct the assistant
	User Role = "user"
	// Assistant messages help store prior responses
	Assistant Role = "assistant"
	// Tool messages extend the conversation with the results of a function call
	Tool Role = "tool"

	// FunctionType is used to describe the function the chat completion is requesting to call
	FunctionType ToolType = "function"

	// GPT4Dot1 is the GPT 4.1 model
	GPT4Dot1 Model = "gpt-4.1"
	// GPT4Dot1Mini is the GPT 4.1 mini model
	GPT4Dot1Mini Model = "gpt-4.1-mini"
	// GPT4Dot1Nano is the GPT 4.1 nano model
	GPT4Dot1Nano Model = "gpt-4.1-nano"
	// GPT4OmniMini is the GPT4 Omni Mini (gpt-4o-mini) model
	GPT4OmniMini Model = "gpt-4o-mini"
	// GPT4Omni is the GPT4 Omni (gpt-4o) model
	GPT4Omni Model = "gpt-4o"
	// GPT4 is the GPT4 model
	// Note: Consider using GPT4Omni, which is cheaper and faster, instead of GPT4, it is likely GPT4 will be deprecated in the future
	GPT4 Model = "gpt-4"
	// GPT3Dot5Turbo is the GPT3.5 turbo model
	GPT3Dot5Turbo Model = "gpt-3.5-turbo"

	TokenLimits = map[Model]int{
		GPT3Dot5Turbo: 4096,
		GPT4:          8192,
		GPT4Omni:      128000,
		GPT4OmniMini:  128000,
		GPT4Dot1:      1047576,
		GPT4Dot1Mini:  1047576,
		GPT4Dot1Nano:  1047576,
	}

	// ErrGetNumberOfTokens is returned when the number of tokens cannot be determined
	ErrGetNumberOfTokens = errors.New("unable to determine number of tokens")

	//Embedding Models
	AdaSimilarity  EmbeddingModel = "text-similarity-ada-001"
	AdaEmbeddingV2 EmbeddingModel = "text-embedding-ada-002"

	// ResponseFormatText is the default response format for completion
	ResponseFormatText ResponseFormat = "text"
	// ResponseFormatJsonObject is one of the response formats for completion
	ResponseFormatJsonObject ResponseFormat = "json_object"
	// ResponseFormatJsonSchema is one of the response formats for completion
	ResponseFormatJsonSchema ResponseFormat = "json_schema"
)

// Verify that OpenAIClient implements the Client interface.
// https://golang.org/doc/faq#guarantee_satisfies_interface
var _ Client = &OpenAIClient{}

// NewClient creates a new client with the given API key
func NewClient(ctx context.Context, secretManager *secretmanager.Client, opts ...ClientOption) (*OpenAIClient, error) {
	clientOptions := &options{
		projectID:     "repcore-prod",
		secretKey:     getSecretManagerKey(config.CurEnv()),
		secretVersion: "latest",
	}

	for _, o := range opts {
		err := o(clientOptions)
		if err != nil {
			return nil, err
		}
	}

	manager := NewSecretManager(secretManager)
	authToken, err := manager.GetSecret(ctx, clientOptions.projectID, clientOptions.secretKey, clientOptions.secretVersion)
	if err != nil {
		return nil, err
	}

	tags := []string{fmt.Sprintf("secret_key:%s", clientOptions.secretKey)}
	if clientOptions.attributionID != "" {
		tags = append(tags, fmt.Sprintf("attribution_id:%s", clientOptions.attributionID))
	}

	statsdClient, err := statsd.InitializeCustomClient("openai", tags)
	if err != nil {
		return nil, err
	}

	tiktoken.SetBpeLoader(tiktokenloader.NewOfflineLoader())

	client := gogpt.NewClient(authToken)
	return &OpenAIClient{
		client: client,
		statsd: statsdClient,
	}, nil
}

// CreateChatCompletion returns a completion given a list of chat messages
func (c *OpenAIClient) CreateChatCompletion(ctx context.Context, messages []ChatCompletionMessageRequest, opts ...ClientOption) (ChatCompletionMessageResponse, error) {
	callerUserID := iaminterceptor.GetRequestInfo(ctx).EffectiveCallerTokenInfo().UserID()

	clientOptions := &options{
		maxTokens:            1024,
		temperature:          0.7,
		model:                string(GPT3Dot5Turbo),
		responseFormat:       string(ResponseFormatText),
		tools:                []ToolDefinition{},
		functionParamTypeMap: make(map[string]interface{}),
		// Defaults to no retries
		maxRetries: 0,
	}

	for _, o := range opts {
		err := o(clientOptions)
		if err != nil {
			return ChatCompletionMessageResponse{}, err
		}
	}

	if clientOptions.trimmer != nil {
		var err error
		messages, err = c.trimTokens(messages, clientOptions.tools, clientOptions.maxTokens, Model(clientOptions.model), clientOptions.trimmer)
		if err != nil {
			return ChatCompletionMessageResponse{}, err
		}
	}

	requestMessage := make([]gogpt.ChatCompletionMessage, len(messages))
	for i, message := range messages {
		toolCalls := make([]gogpt.ToolCall, len(message.ToolCalls))
		for i, toolCall := range message.ToolCalls {
			toolCalls[i] = gogpt.ToolCall{
				ID:   toolCall.ID,
				Type: gogpt.ToolType(toolCall.Type),
				Function: gogpt.FunctionCall{
					Name:      toolCall.Function.Name,
					Arguments: fmt.Sprintf("%v", toolCall.Function.Arguments),
				},
			}
		}

		var multiContent []gogpt.ChatMessagePart
		for _, chatMessagePart := range message.MultiContent {
			imageURL := &gogpt.ChatMessageImageURL{}
			if chatMessagePart.ImageURL != nil {
				imageURL.URL = chatMessagePart.ImageURL.URL
				imageURL.Detail = gogpt.ImageURLDetail(chatMessagePart.ImageURL.Detail)
			}
			multiContent = append(multiContent, gogpt.ChatMessagePart{
				Type:     gogpt.ChatMessagePartType(chatMessagePart.Type),
				Text:     chatMessagePart.Text,
				ImageURL: imageURL,
			})
		}

		requestMessage[i] = gogpt.ChatCompletionMessage{
			Role:         message.Role,
			Content:      message.Content,
			MultiContent: multiContent,
			ToolCalls:    toolCalls,
			ToolCallID:   message.ToolCallID,
		}
	}

	responseFormat, err := c.getResponseFormat(clientOptions)
	if err != nil {
		return ChatCompletionMessageResponse{}, err
	}

	tools := make([]gogpt.Tool, len(clientOptions.tools))
	for i, tool := range clientOptions.tools {
		params, err := tool.Function.JSONParameters()
		if err != nil {
			return ChatCompletionMessageResponse{}, err
		}

		tools[i] = gogpt.Tool{
			Type: gogpt.ToolType(tool.Type),
			Function: &gogpt.FunctionDefinition{
				Name:        tool.Function.Name,
				Description: tool.Function.Description,
				Strict:      tool.Function.Strict,
				Parameters:  params,
			},
		}
	}

	now := time.Now()
	chatCompletionMessageResponse := ChatCompletionMessageResponse{}
	err = vax.Invoke(ctx, func(ctx context.Context, settings vax.CallSettings) error {
		request := gogpt.ChatCompletionRequest{
			Model:          clientOptions.model,
			Messages:       requestMessage,
			MaxTokens:      clientOptions.maxTokens,
			ResponseFormat: responseFormat,
			Temperature:    clientOptions.temperature,
			TopP:           1,
			N:              1,
			Stream:         false,
			User:           callerUserID,
			Tools:          tools,
			ToolChoice:     clientOptions.toolChoice,
		}
		if clientOptions.parallelToolCalls != nil {
			request.ParallelToolCalls = *clientOptions.parallelToolCalls
		}
		response, err := c.client.CreateChatCompletion(ctx, request)
		if err != nil {
			_ = c.statsd.Incr("chat", []string{"status:error", fmt.Sprintf("model:%s", clientOptions.model)}, 1)
			logging.Errorf(ctx, "Error calling CreateChatCompletion: %v", err)
			return err
		}
		_ = c.statsd.Distribution("chat.latency", float64(time.Now().Sub(now).Milliseconds()), nil, 1)
		_ = c.statsd.Incr("chat", []string{"status:success", fmt.Sprintf("model:%s", clientOptions.model)}, 1)
		_ = c.statsd.Count("tokens", int64(response.Usage.TotalTokens), []string{fmt.Sprintf("model:%s", clientOptions.model)}, 1)
		if response.Usage.PromptTokensDetails != nil {
			_ = c.statsd.Count("tokens.prompt.audio", int64(response.Usage.PromptTokensDetails.AudioTokens), []string{fmt.Sprintf("model:%s", clientOptions.model)}, 1)
			_ = c.statsd.Count("tokens.prompt.cached", int64(response.Usage.PromptTokensDetails.CachedTokens), []string{fmt.Sprintf("model:%s", clientOptions.model)}, 1)
		}
		if response.Usage.CompletionTokensDetails != nil {
			_ = c.statsd.Count("tokens.completion.audio", int64(response.Usage.CompletionTokensDetails.AudioTokens), []string{fmt.Sprintf("model:%s", clientOptions.model)}, 1)
			_ = c.statsd.Count("tokens.completion.reasoning", int64(response.Usage.CompletionTokensDetails.ReasoningTokens), []string{fmt.Sprintf("model:%s", clientOptions.model)}, 1)
		}

		estimatedTokens, _ := c.GetNumberOfTokens(messages, WithTools(clientOptions.tools), WithModel(Model(clientOptions.model)))
		if estimatedTokens != response.Usage.PromptTokens {
			_ = c.statsd.Incr("wrongTokenEstimate", []string{"status:error", fmt.Sprintf("model:%s", clientOptions.model)}, 1)
		}

		if len(response.Choices) > 0 {
			toolCalls, tErr := getToolCallsFromChatCompletionMessage(ctx, response.Choices[0].Message, clientOptions.functionParamTypeMap)
			if tErr != nil {
				responseStr := fmt.Sprintf("%#v", response.Choices[0].Message)
				logging.Errorf(ctx, "Error getting tool calls from chat completion message: [%v]. API response: [%s].", tErr, responseStr)
				return tErr
			}

			chatCompletionMessageResponse = ChatCompletionMessageResponse{
				Role:      Role(response.Choices[0].Message.Role),
				Content:   strings.TrimSpace(response.Choices[0].Message.Content),
				ToolCalls: toolCalls,
				Usage: Usage{
					TotalTokens:      response.Usage.TotalTokens,
					PromptTokens:     response.Usage.PromptTokens,
					CompletionTokens: response.Usage.CompletionTokens,
				},
			}
			if response.Usage.PromptTokensDetails != nil {
				chatCompletionMessageResponse.Usage.PromptTokenDetails = &PromptTokenDetails{
					CachedTokens: response.Usage.PromptTokensDetails.CachedTokens,
					AudioTokens:  response.Usage.PromptTokensDetails.AudioTokens,
				}
			}
			if response.Usage.CompletionTokensDetails != nil {
				chatCompletionMessageResponse.Usage.CompletionTokenDetails = &CompletionTokenDetails{
					ReasoningTokens: response.Usage.CompletionTokensDetails.ReasoningTokens,
					AudioTokens:     response.Usage.CompletionTokensDetails.AudioTokens,
				}
			}
		} else {
			logging.Warningf(ctx, "No choices returned from OpenAI API response: [%v]", response)
		}
		return nil
	}, retryer(clientOptions.maxRetries))
	if err != nil {
		return chatCompletionMessageResponse, verrors.FromErrorWithContext(ctx, err).WithInternalMessage("CreateChatCompletion: all retries failed")
	}

	return chatCompletionMessageResponse, nil
}

func (c *OpenAIClient) trimTokens(messages []ChatCompletionMessageRequest, tools []ToolDefinition, maxTokens int, model Model, trimmer ChatCompletionWindower) ([]ChatCompletionMessageRequest, error) {
	// get the limit for the model
	inputLimit, ok := TokenLimits[model]
	if !ok {
		return nil, fmt.Errorf("token limit undefined for model %v", model)
	}
	// the input limit is the model's overall limit minus amount of tokens reserved for completion (maxTokens)
	inputLimit = inputLimit - maxTokens
	overLimit := true
	for overLimit {
		numTokens, err := c.GetNumberOfTokens(messages, WithTools(tools), WithModel(model))
		if err != nil {
			return nil, err
		}
		overLimit = numTokens > inputLimit
		if overLimit {
			messages, err = trimmer(messages)
			if err != nil {
				return nil, err
			}
		}
	}
	return messages, nil
}

func getToolCallsFromChatCompletionMessage(ctx context.Context, message gogpt.ChatCompletionMessage, funcParamMap map[string]interface{}) ([]ToolCall, error) {
	if len(message.ToolCalls) == 0 {
		return nil, nil
	}
	toolCalls := make([]ToolCall, len(message.ToolCalls))
	for i, toolCall := range message.ToolCalls {
		funcParams, ok := funcParamMap[toolCall.Function.Name]
		if !ok {
			// unknown function, skipping
			continue
		}

		args := reflect.New(reflect.TypeOf(funcParams)).Interface()
		err := json.Unmarshal([]byte(toolCall.Function.Arguments), args)
		if err != nil {
			logging.Infof(ctx, "Problem found while unmarshalling JSON: %v", err)
			fixedJSON, err := jsonrepair.RepairJSON(toolCall.Function.Arguments)
			if err != nil {
				logging.Errorf(ctx, "Error fixing JSON: %v", err)
				return nil, err
			}

			logging.Infof(ctx, "Attempting to fix JSON... \nPrevious: %s\nFixed: %s", toolCall.Function.Arguments, fixedJSON)
			err = json.Unmarshal([]byte(fixedJSON), args)
			if err != nil {
				logging.Errorf(ctx, "Error unmarshalling fixed JSON: %v", err)
				return nil, err
			}
		}

		toolCalls[i] = ToolCall{
			ID:   toolCall.ID,
			Type: ToolType(toolCall.Type),
			Function: FunctionCall{
				Name:      toolCall.Function.Name,
				Arguments: args,
			},
		}
	}
	return toolCalls, nil
}

// CreateImages returns a list of images given a prompt
func (c *OpenAIClient) CreateImages(ctx context.Context, request CreateImagesRequest) ([]CreateImagesResponse, error) {
	callerUserID := iaminterceptor.GetRequestInfo(ctx).EffectiveCallerTokenInfo().UserID()
	now := time.Now()

	// Set default model if not provided
	if request.Model == "" {
		request.Model = gogpt.CreateImageModelDallE2
	}

	// Prepare request based on model type
	var imageRequest gogpt.ImageRequest
	if request.Model == gogpt.CreateImageModelGptImage1 {
		imageRequest = c.prepareGPTImage1Request(request, callerUserID)
	} else {
		imageRequest = c.prepareDallEImageRequest(request, callerUserID)
	}

	response, err := c.client.CreateImage(ctx, imageRequest)

	if err != nil {
		_ = c.statsd.Incr("createImage", []string{"status:error", fmt.Sprintf("model:%s", request.Model)}, 1)
		return nil, err
	}

	_ = c.statsd.Distribution("createImage.latency", float64(time.Now().Sub(now).Milliseconds()), nil, 1)
	_ = c.statsd.Incr("createImage", []string{"status:success", fmt.Sprintf("model:%s", request.Model)}, 1)

	createImagesResponse := make([]CreateImagesResponse, len(response.Data))
	for i, image := range response.Data {
		createImagesResponse[i] = CreateImagesResponse{
			URL:           image.URL,
			B64JSON:       image.B64JSON,
			RevisedPrompt: image.RevisedPrompt,
		}
	}

	return createImagesResponse, nil
}

func (c *OpenAIClient) CreateImageVariations(ctx context.Context, request ImageVariationsRequest) ([]CreateImagesResponse, error) {
	now := time.Now()

	response, err := c.client.CreateVariImage(ctx, gogpt.ImageVariRequest{
		Image: request.Image,
		N:     request.N,
		Size:  request.Size,
	})

	if err != nil {
		_ = c.statsd.Incr("createImagesVariation", []string{"status:error"}, 1)
		return nil, err
	}

	_ = c.statsd.Distribution("createImagesVariation.latency", float64(time.Now().Sub(now).Milliseconds()), nil, 1)
	_ = c.statsd.Incr("createImagesVariation", []string{"status:success"}, 1)

	createImagesResponse := make([]CreateImagesResponse, len(response.Data))
	for i, image := range response.Data {
		createImagesResponse[i] = CreateImagesResponse{
			URL:     image.URL,
			B64JSON: image.B64JSON,
		}
	}

	return createImagesResponse, nil
}

// GetNumberOfTokens returns the number of tokens in a given chat completion message request
// Accepts the WithModel option to determine the number of tokens based on the model
// Accepts the WithTools option to determine the number of tokens based on the tool definitions
func (c *OpenAIClient) GetNumberOfTokens(messages []ChatCompletionMessageRequest, opts ...ClientOption) (int, error) {
	optionsStruct := &options{}
	for _, o := range opts {
		err := o(optionsStruct)
		if err != nil {
			return 0, fmt.Errorf("%w, error setting options: %v", ErrGetNumberOfTokens, err)
		}
	}
	model := optionsStruct.model
	// assume GPT3Dot5Turbo if no model is specified
	if model == "" {
		model = string(GPT3Dot5Turbo)
	}

	// These models are currently not included in the tiktoken library
	// So we need to manually set the model to GPT4Omni since they all use the same encoding
	if model == string(GPT4OmniMini) || model == string(GPT4Dot1) || model == string(GPT4Dot1Mini) || model == string(GPT4Dot1Nano) {
		model = string(GPT4Omni)
	}

	tkm, err := tiktoken.EncodingForModel(model)
	if err != nil {
		return 0, fmt.Errorf("%w, error finding encoding for model: %v", ErrGetNumberOfTokens, err)
	}

	var numTokens int
	for _, message := range messages {
		// we need to consider the length of the message's keys as part of the prompt (content, role, name)
		// note that we do not use `name`, but it will still be part of the prompt send to the model
		// see https://github.com/openai/openai-cookbook/blob/main/examples/How_to_count_tokens_with_tiktoken.ipynb for an official reference on how to count tokens for chat completion
		numTokens += 3
		numTokens += len(tkm.Encode(message.Content, nil, nil))
		numTokens += len(tkm.Encode(message.Role, nil, nil))
	}

	numTokens += 3 // every reply is primed with <|start|>assistant<|message|>

	// Add tokens for the tool calls
	toolTokens, err := c.getToolCallNumberOfTokens(optionsStruct.tools, tkm)
	if err != nil {
		return 0, fmt.Errorf("%w, error getting tool call number of tokens: %v", ErrGetNumberOfTokens, err)
	}
	numTokens += toolTokens

	return numTokens, nil
}

// getToolCallNumberOfTokens returns the number of tokens for the tool calls
// Note: objects nested more than one level deep will over-estimate tokens by a little bit
func (c *OpenAIClient) getToolCallNumberOfTokens(tools []ToolDefinition, encoder *tiktoken.Tiktoken) (int, error) {
	numTokens := 0

	// We don't need to run this code if there are no tools
	if len(tools) == 0 {
		return numTokens, nil
	}

	// Formats the function definitions as a typescript function definitions
	funcDefSpecialFormat, err := c.formatFunctionDefinition(tools)
	if err != nil {
		return 0, err
	}

	// Encode the javascript functions text using tiktoken
	funcDefEncodedSize := len(encoder.Encode(funcDefSpecialFormat, nil, nil))
	// Add tokens for the function definitions
	numTokens += funcDefEncodedSize
	// Dark magic
	// Probably something related to the ChatML formatting and/or the extra fixed tool specific tokens that are included
	numTokens += 9

	return numTokens, nil
}

// formatFunctionDefinition formats the function as a typescript function
func (c *OpenAIClient) formatFunctionDefinition(tools []ToolDefinition) (string, error) {
	lines := []string{"namespace functions {", ""}

	for _, t := range tools {
		// Only add the function description if it's present
		// Including it without any description results in extra tokens
		if t.Function.Description != "" {
			lines = append(lines, fmt.Sprintf("// %s", t.Function.Description))
		}

		var paramsSchema *jsonschema.Schema
		if t.Function.SchemaParameters != nil {
			paramsSchema = t.Function.SchemaParameters
		} else {
			params, err := t.Function.JSONParameters()
			if err != nil {
				return "", err
			}
			if params != nil {
				err := json.Unmarshal(params, &paramsSchema)
				if err != nil {
					return "", err
				}
			}
		}
		if paramsSchema != nil {
			lines = append(lines, fmt.Sprintf("type %s = (_: {", t.Function.Name))
			formattedParams, err := formatParams(*paramsSchema, false)
			if err != nil {
				return "", err
			}
			lines = append(lines, strings.Join(formattedParams, "\n"))
			lines = append(lines, "}) => any;")
		}
		lines = append(lines, "")
	}

	lines = append(lines, "} // namespace functions")
	return strings.Join(lines, "\n"), nil
}

func formatParams(schema jsonschema.Schema, indent bool) ([]string, error) {
	paramLines := []string{}
	for name, property := range schema.Properties {
		// Only add parameter description if it's present and on the top-level definition
		// Including it without any description results in extra tokens
		if !indent && len(property.Description) > 0 {
			paramLines = append(paramLines, fmt.Sprintf("// %s", property.Description))
		}

		requiredSymbol := "?"
		for _, required := range schema.Required {
			if required == name {
				requiredSymbol = ""
				break
			}
		}

		if property.Type == jsonschema.DataTypeObject {
			formattedProperty, err := formatObject(name, property, requiredSymbol)
			if err != nil {
				return nil, err
			}
			paramLines = append(paramLines, formattedProperty...)
			continue
		}

		if property.Type == jsonschema.DataTypeSlice {
			formattedProperty, err := formatSlice(name, property, requiredSymbol)
			if err != nil {
				return nil, err
			}
			paramLines = append(paramLines, formattedProperty...)
			continue
		}
		paramLines = append(paramLines, fmt.Sprintf("%s%s: %s;", name, requiredSymbol, property.Type))
	}
	if schema.Items != nil {
		if schema.Items.Type == jsonschema.DataTypeObject || schema.Items.Type == jsonschema.DataTypeSlice {
			formattedItems, err := formatParams(*schema.Items, true)
			if err != nil {
				return nil, err
			}
			paramLines = append(paramLines, formattedItems...)
		} else {
			paramLines = append(paramLines, string(schema.Items.Type))
		}
	}
	if indent {
		for i, line := range paramLines {
			paramLines[i] = "    " + line
		}
	}
	return paramLines, nil
}

func formatObject(name string, property jsonschema.Schema, requiredSymbol string) ([]string, error) {
	paramLines := []string{}
	paramLines = append(paramLines, fmt.Sprintf("%s%s: {", name, requiredSymbol))

	formattedParams, err := formatParams(property, true)
	if err != nil {
		return nil, err
	}
	paramLines = append(paramLines, formattedParams...)
	paramLines = append(paramLines, "}")
	return paramLines, nil
}

func formatSlice(name string, property jsonschema.Schema, requiredSymbol string) ([]string, error) {
	paramLines := []string{}

	if property.Items != nil {
		if property.Items.Type == jsonschema.DataTypeObject {
			formattedParams, err := formatParams(property, true)
			if err != nil {
				return nil, err
			}

			paramLines = append(paramLines, fmt.Sprintf("%s%s: {", name, requiredSymbol))
			paramLines = append(paramLines, formattedParams...)
			paramLines = append(paramLines, "}[]")
		} else {
			paramLines = append(paramLines, fmt.Sprintf("%s%s: %s[]", name, requiredSymbol, property.Items.Type))
		}
	}
	return paramLines, nil
}

func (c *OpenAIClient) CreateEmbeddings(ctx context.Context, model EmbeddingModel, inputs []string) (CreateEmbeddingsResponse, error) {
	var goGPTModel gogpt.EmbeddingModel
	if model == AdaSimilarity {
		goGPTModel = gogpt.AdaSimilarity
	}
	if model == AdaEmbeddingV2 {
		goGPTModel = gogpt.AdaEmbeddingV2
	}

	response, err := c.client.CreateEmbeddings(ctx, gogpt.EmbeddingRequest{
		Model: goGPTModel,
		Input: inputs,
	})
	if err != nil {
		_ = c.statsd.Incr("embeddings", []string{"status:error", fmt.Sprintf("model:%s", model)}, 1)
		return CreateEmbeddingsResponse{}, fmt.Errorf("error generating embedding: %w", err)
	}

	_ = c.statsd.Incr("embeddings", []string{"status:success", fmt.Sprintf("model:%s", model)}, 1)
	_ = c.statsd.Count("tokens", int64(response.Usage.TotalTokens), []string{"model:" + string(model)}, 1)

	createResp := CreateEmbeddingsResponse{
		Object: response.Object,
		Model:  EmbeddingModel(response.Model),
		Usage: UsageData{
			PromptTokens: response.Usage.PromptTokens,
			TotalTokens:  response.Usage.TotalTokens,
		},
	}

	for _, embedding := range response.Data {
		createResp.Data = append(createResp.Data, EmbeddingData{
			Object:    embedding.Object,
			Embedding: embedding.Embedding,
			Index:     embedding.Index,
		})
	}

	return createResp, nil
}

func getSecretManagerKey(env config.Env) string {
	secretKey := "open-ai-api-key-demo"
	if env == config.Prod {
		secretKey = "open-ai-api-key-prod"
	}
	return secretKey
}

func (f *FunctionDefinition) JSONParameters() (json.RawMessage, error) {
	if f == nil || (f.Parameters == nil && f.SchemaParameters == nil) {
		return nil, nil
	}
	var jsonParams []byte
	var err error
	if f.Parameters != nil {
		jsonParams, err = jsonschema.FunctionDefinitionParamtersToJSONSchema(f.Parameters, f.Strict)
	} else {
		jsonParams, err = json.MarshalIndent(f.SchemaParameters, "", "    ")
	}
	if err != nil {
		return nil, err
	}

	var rawMessage json.RawMessage
	err = json.Unmarshal(jsonParams, &rawMessage)
	return rawMessage, err
}

func (f *FunctionDefinition) Validate() error {
	return validation.NewValidator().
		Rule(rules.StringNotEmpty(f.Name, "name is required")).
		Rule(rules.BoolTrue(f.Parameters != nil || f.SchemaParameters != nil, "one of Parameters or SchemaParameters are required, to describe a function that accepts no parameters provide SchemaParameters with no Properties")).
		Rule(rules.BoolFalse(f.Parameters != nil && f.SchemaParameters != nil, "only one of Parameters or SchemaParameters can be provided")).
		Rule(rules.BoolFalse(f.Parameters != nil && reflect.TypeOf(f.Parameters).Kind() != reflect.Struct,
			"Parameters should be a struct with properties that describe the parameters used by the function")).
		Rule(rules.BoolFalse(f.SchemaParameters != nil && f.SchemaParameters.Type != jsonschema.DataTypeObject,
			"SchemaParameters should be an object Type")).
		Validate()
}

// getResponseFormat creates a ChatCompletionResponseFormat based on the client options
func (c *OpenAIClient) getResponseFormat(clientOptions *options) (*gogpt.ChatCompletionResponseFormat, error) {
	responseFormat := &gogpt.ChatCompletionResponseFormat{}

	if clientOptions.responseFormatJSONSchemaDefn != nil {
		responseFormat.Type = gogpt.ChatCompletionResponseFormatTypeJSONSchema
		schemaJSON, err := clientOptions.responseFormatJSONSchemaDefn.JSONParameters()
		if err != nil {
			return nil, err
		}
		responseFormat.JSONSchema = &gogpt.ChatCompletionResponseFormatJSONSchema{
			Name:        clientOptions.responseFormatJSONSchemaDefn.Name,
			Description: clientOptions.responseFormatJSONSchemaDefn.Description,
			Schema:      schemaJSON,
			Strict:      clientOptions.responseFormatJSONSchemaDefn.Strict,
		}
	} else {
		responseFormat.Type = gogpt.ChatCompletionResponseFormatType(clientOptions.responseFormat)
	}

	return responseFormat, nil
}

// JSONParameters generates the JSON schema based on the definition
func (f *JSONSchemaDefinition) JSONParameters() (json.RawMessage, error) {
	if f == nil || (f.Parameters == nil && f.SchemaParameters == nil) {
		return nil, nil
	}
	var jsonParams []byte
	var err error
	if f.Parameters != nil {
		jsonParams, err = jsonschema.ParametersToJSONSchema(f.Parameters, f.Strict)
	} else {
		jsonParams, err = json.MarshalIndent(f.SchemaParameters, "", "    ")
	}
	if err != nil {
		return nil, err
	}

	var rawMessage json.RawMessage
	err = json.Unmarshal(jsonParams, &rawMessage)
	return rawMessage, err
}

func (f *JSONSchemaDefinition) Validate() error {
	return validation.NewValidator().
		Rule(rules.StringNotEmpty(f.Name, "name is required")).
		Rule(rules.BoolTrue(f.Parameters != nil || f.SchemaParameters != nil, "one of Parameters or SchemaParameters are required, to describe a function that accepts no parameters provide SchemaParameters with no Properties")).
		Rule(rules.BoolFalse(f.Parameters != nil && f.SchemaParameters != nil, "only one of Parameters or SchemaParameters can be provided")).
		Rule(rules.BoolFalse(f.Parameters != nil && reflect.TypeOf(f.Parameters).Kind() != reflect.Struct,
			"Parameters should be a struct with properties that describe the parameters used by the function")).
		Rule(rules.BoolFalse(f.SchemaParameters != nil && f.SchemaParameters.Type != jsonschema.DataTypeObject,
			"SchemaParameters should be an object Type")).
		Validate()
}

// prepareGPTImage1Request sets defaults for GPT-image-1 model and creates the request
func (c *OpenAIClient) prepareGPTImage1Request(request CreateImagesRequest, callerUserID string) gogpt.ImageRequest {
	// Set defaults for GPT-image-1 model
	if request.Background == "" {
		request.Background = gogpt.CreateImageBackgroundOpaque
	}
	if request.Quality == "" {
		request.Quality = gogpt.CreateImageQualityMedium
	}
	if request.OutputFormat == "" {
		request.OutputFormat = gogpt.CreateImageOutputFormatPNG
	}
	if request.OutputCompression == 0 {
		request.OutputCompression = 100
	}

	return gogpt.ImageRequest{
		Prompt:            request.Prompt,
		N:                 request.N,
		Size:              request.Size,
		Model:             request.Model,
		Quality:           request.Quality,
		Background:        request.Background,
		Moderation:        request.Moderation,
		OutputCompression: request.OutputCompression,
		OutputFormat:      request.OutputFormat,
		User:              callerUserID,
	}
}

// prepareDallEImageRequest sets defaults for DALL-E models and creates the request
func (c *OpenAIClient) prepareDallEImageRequest(request CreateImagesRequest, callerUserID string) gogpt.ImageRequest {
	// Set defaults for DALL-E models
	if request.Quality == "" {
		request.Quality = gogpt.CreateImageQualityStandard
	}

	return gogpt.ImageRequest{
		Prompt:         request.Prompt,
		N:              request.N,
		Size:           request.Size,
		ResponseFormat: request.ResponseFormat,
		Model:          request.Model,
		Quality:        request.Quality,
		Style:          request.Style,
		User:           callerUserID,
	}
}