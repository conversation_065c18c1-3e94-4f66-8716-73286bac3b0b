# Changelog

## Unreleased

## v0.0.8

- More test cases.
- Sync upstream.

## v0.0.7

- Enhance README
- Add golangci-lint

## v0.0.6

- Use bytes.IndexByte instead of Contains
- fix upstream #29
- Split MustRepairJSON

## v0.0.5

### Improvements
- Tuning goreleaser, remove draft
- UT action
- Change minimal Go version

## v0.0.4

### New Features
- Add Homebrew tap.

## v0.0.3

### New Features
- Introduced `jsonrepair` as a command-line interface for easier use.

### Improvements
- Initialized `goreleaser` to streamline the release process.

### Bug Fixes
- Resolved an issue reported in upstream repository (#26).

## v0.0.2

### New Features
- Implemented `markerRecord` to enhance the tracking of nested data structures.

### Bug Fixes
- Addressed a bug identified in the upstream repository (#24).

### Deprecations
- (This section is left empty as there are no items to be removed in this release.)

## v0.0.1

### Initial Release
- Launched the initial version of `json-repair`, a tool designed to fix malformed JSON files.

### Deprecations
- (This section is left empty as there are no items to be removed in this initial release.)
