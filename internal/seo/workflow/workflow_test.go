package seoworkflow

import (
	"context"
	"errors"
	"fmt"
	"testing"
	"time"

	"github.com/vendasta/listing-products/internal/seo/workflow/seofailedworkflowinfo"
	seofailedworkflowinfoservice "github.com/vendasta/listing-products/internal/seo/workflow/seofailedworkflowinfo/service"
	"go.temporal.io/sdk/temporal"

	"github.com/golang/mock/gomock"
	"github.com/short-hop/vmockhelper"
	"github.com/stretchr/testify/assert"
	cssdk "github.com/vendasta/CS/sdks/go/v1"
	accounts_v2 "github.com/vendasta/generated-protos-go/accounts/v2"
	nap_v1 "github.com/vendasta/generated-protos-go/nap/v1"
	"github.com/vendasta/generated-protos-go/vstorepb"
	"github.com/vendasta/listing-products/internal/constants"
	"github.com/vendasta/listing-products/internal/dataforseo"
	listingprofilemodel "github.com/vendasta/listing-products/internal/listingprofile/model"
	listingprofileservice "github.com/vendasta/listing-products/internal/listingprofile/service"
	seodata "github.com/vendasta/listing-products/internal/seo/model"
	seodataservice "github.com/vendasta/listing-products/internal/seo/service"
	"github.com/vendasta/listing-products/internal/seo/workflow/mocks"
	seosettingsmodel "github.com/vendasta/listing-products/internal/seosettings/model"
	seosettingsservice "github.com/vendasta/listing-products/internal/seosettings/service"
	"go.temporal.io/sdk/testsuite"
	"go.temporal.io/sdk/worker"
)

type DataForSeoPost struct {
	Req  []*dataforseo.GoogleMapsSERPTaskPostRequest
	Resp *dataforseo.GoogleMapsSERPResponse
}

type DataForSeoGet struct {
	Req  *dataforseo.GoogleMapsSERPTaskPostRequest
	Resp *dataforseo.GoogleMapsSERPResponse
}

type DataForSeoOrganicPost struct {
	Req []*dataforseo.GoogleOrganicSERPTaskPostRequest
}

type LocalSEODataEntries struct {
	businessID string
	keyword    string
	date       string
	entries    []*seodata.LocalSearchData
}

type OrganicSEODataEntries struct {
	businessID string
	keyword    string
	date       string
	rank       int
}

func Test_LocalSEOWorkflow(t *testing.T) {
	type keywordInfoCall struct {
		BusinessID   string
		Keyword      string
		Date         string
		LocationCode int64
		LocationName string
		Fetched      bool
	}

	type testCase struct {
		name                           string
		workflowParams                 *SERPWorkflowParams
		listingProfile                 *listingprofilemodel.ListingProfile
		seoSettings                    *seosettingsmodel.SEOSettings
		country                        *nap_v1.Country
		state                          *nap_v1.ListStatesResponse
		activations                    *accounts_v2.GetMultiActivationStatusesResponse
		expectedKeywordInfoCalls       []*keywordInfoCall
		dataForSEOPosts                []*DataForSeoPost
		dataForSEOOrganicPosts         []*DataForSeoOrganicPost
		dataForSEOGets                 []*DataForSeoGet
		taskItems                      []*dataforseo.GoogleMapsItem
		organicTaskItems               []*dataforseo.GoogleOrganicItem
		expectedLocalSearchDataCalls   []*LocalSEODataEntries
		expectedOrganicSearchDataCalls []*OrganicSEODataEntries
		expectedResponse               interface{}
		expectedWorkflowErr            error
	}
	cases := []*testCase{
		{
			name: "Test completes workflow for accounts with 50 total tasks",
			workflowParams: &SERPWorkflowParams{
				Date:       "2023-06-01",
				Force:      true,
				BusinessID: "AG-1",
				Keywords:   []string{"alpha", "beta"},
			},
			listingProfile: &listingprofilemodel.ListingProfile{
				BusinessID:  "AG-1",
				CompanyName: "Name1",
				Address:     "Address1",
				City:        "City1",
				State:       "State1",
				Website:     "Website1",
				Country:     "Country1",
				WorkNumber:  []string{"**************"},
				Location:    &vstorepb.GeoPoint{Latitude: 10, Longitude: 20},
			},
			seoSettings: &seosettingsmodel.SEOSettings{
				BusinessID:        "AG-1",
				LocalSearchRadius: 1.25,
			},
			country: &nap_v1.Country{
				Id:   "Country1",
				Name: "Country1",
			},
			state: &nap_v1.ListStatesResponse{
				States: []*nap_v1.State{
					{
						Id:   "State1",
						Name: "State1",
					},
				},
			},
			activations: &accounts_v2.GetMultiActivationStatusesResponse{
				ActivationStatuses: []*accounts_v2.GetMultiActivationStatusesResponse_AppsAndAddonsActivationStatuses{
					{
						BusinessId: "AG-M8GZ83PL",
						ProductId:  constants.ListingBuilderMarketplaceAppID,
						Status:     accounts_v2.ActivationStatus_ACTIVATION_STATUS_ACTIVATED,
						Count:      1,
						EditionId:  constants.ListingBuilderDemoPaidEditionID,
					},
				},
			},
			expectedKeywordInfoCalls: []*keywordInfoCall{
				{
					BusinessID:   "AG-1",
					Keyword:      "alpha",
					Date:         "2023-06-01",
					LocationCode: 111,
					LocationName: "City1,State1,Country1",
					Fetched:      true,
				},
				{
					BusinessID:   "AG-1",
					Keyword:      "beta",
					Date:         "2023-06-01",
					LocationCode: 111,
					LocationName: "City1,State1,Country1",
					Fetched:      true,
				},
			},
			dataForSEOPosts: []*DataForSeoPost{
				{
					Req: []*dataforseo.GoogleMapsSERPTaskPostRequest{
						{
							BusinessID: "AG-1", Keyword: "alpha", LanguageCode: "en", Location: &dataforseo.MapLocation{
								Latitude: 10.***************, Longitude: 19.**************, Zoom: 13,
							}, Vicinity: "VICINITY_A1",
						}, {
							BusinessID: "AG-1", Keyword: "alpha", LanguageCode: "en", Location: &dataforseo.MapLocation{
								Latitude: 10.***************, Longitude: 19.990825038597745, Zoom: 13,
							}, Vicinity: "VICINITY_A2",
						}, {
							BusinessID: "AG-1", Keyword: "alpha", LanguageCode: "en",
							Location: &dataforseo.MapLocation{Latitude: 10.***************, Longitude: 20, Zoom: 13},
							Vicinity: "VICINITY_A3",
						}, {
							BusinessID: "AG-1", Keyword: "alpha", LanguageCode: "en", Location: &dataforseo.MapLocation{
								Latitude: 10.***************, Longitude: 20.009174961402255, Zoom: 13,
							}, Vicinity: "VICINITY_A4",
						}, {
							BusinessID: "AG-1", Keyword: "alpha", LanguageCode: "en", Location: &dataforseo.MapLocation{
								Latitude: 10.***************, Longitude: 20.01834992280451, Zoom: 13,
							}, Vicinity: "VICINITY_A5",
						}, {
							BusinessID: "AG-1", Keyword: "alpha", LanguageCode: "en", Location: &dataforseo.MapLocation{
								Latitude: 10.009099330559074, Longitude: 19.**************, Zoom: 13,
							}, Vicinity: "VICINITY_B1",
						}, {
							BusinessID: "AG-1", Keyword: "alpha", LanguageCode: "en", Location: &dataforseo.MapLocation{
								Latitude: 10.009099330559074, Longitude: 19.990825038597745, Zoom: 13,
							}, Vicinity: "VICINITY_B2",
						}, {
							BusinessID: "AG-1", Keyword: "alpha", LanguageCode: "en",
							Location: &dataforseo.MapLocation{Latitude: 10.009099330559074, Longitude: 20, Zoom: 13},
							Vicinity: "VICINITY_B3",
						}, {
							BusinessID: "AG-1", Keyword: "alpha", LanguageCode: "en", Location: &dataforseo.MapLocation{
								Latitude: 10.009099330559074, Longitude: 20.009174961402255, Zoom: 13,
							}, Vicinity: "VICINITY_B4",
						}, {
							BusinessID: "AG-1", Keyword: "alpha", LanguageCode: "en", Location: &dataforseo.MapLocation{
								Latitude: 10.009099330559074, Longitude: 20.01834992280451, Zoom: 13,
							}, Vicinity: "VICINITY_B5",
						}, {
							BusinessID: "AG-1", Keyword: "alpha", LanguageCode: "en",
							Location: &dataforseo.MapLocation{Latitude: 10, Longitude: 19.**************, Zoom: 13},
							Vicinity: "VICINITY_C1",
						}, {
							BusinessID: "AG-1", Keyword: "alpha", LanguageCode: "en",
							Location: &dataforseo.MapLocation{Latitude: 10, Longitude: 19.990825038597745, Zoom: 13},
							Vicinity: "VICINITY_C2",
						},
					},
				},
				{
					Req: []*dataforseo.GoogleMapsSERPTaskPostRequest{
						{
							BusinessID: "AG-1", Keyword: "alpha", LanguageCode: "en",
							Location: &dataforseo.MapLocation{Latitude: 10, Longitude: 20, Zoom: 13},
							Vicinity: "VICINITY_C3",
						}, {
							BusinessID: "AG-1", Keyword: "alpha", LanguageCode: "en",
							Location: &dataforseo.MapLocation{Latitude: 10, Longitude: 20.009174961402255, Zoom: 13},
							Vicinity: "VICINITY_C4",
						}, {
							BusinessID: "AG-1", Keyword: "alpha", LanguageCode: "en",
							Location: &dataforseo.MapLocation{Latitude: 10, Longitude: 20.01834992280451, Zoom: 13},
							Vicinity: "VICINITY_C5",
						}, {
							BusinessID: "AG-1", Keyword: "alpha", LanguageCode: "en", Location: &dataforseo.MapLocation{
								Latitude: 9.990900669440926, Longitude: 19.**************, Zoom: 13,
							}, Vicinity: "VICINITY_D1",
						}, {
							BusinessID: "AG-1", Keyword: "alpha", LanguageCode: "en", Location: &dataforseo.MapLocation{
								Latitude: 9.990900669440926, Longitude: 19.990825038597745, Zoom: 13,
							}, Vicinity: "VICINITY_D2",
						}, {
							BusinessID: "AG-1", Keyword: "alpha", LanguageCode: "en",
							Location: &dataforseo.MapLocation{Latitude: 9.990900669440926, Longitude: 20, Zoom: 13},
							Vicinity: "VICINITY_D3",
						}, {
							BusinessID: "AG-1", Keyword: "alpha", LanguageCode: "en", Location: &dataforseo.MapLocation{
								Latitude: 9.990900669440926, Longitude: 20.009174961402255, Zoom: 13,
							}, Vicinity: "VICINITY_D4",
						}, {
							BusinessID: "AG-1", Keyword: "alpha", LanguageCode: "en", Location: &dataforseo.MapLocation{
								Latitude: 9.990900669440926, Longitude: 20.01834992280451, Zoom: 13,
							}, Vicinity: "VICINITY_D5",
						}, {
							BusinessID: "AG-1", Keyword: "alpha", LanguageCode: "en", Location: &dataforseo.MapLocation{
								Latitude: 9.981801338881853, Longitude: 19.**************, Zoom: 13,
							}, Vicinity: "VICINITY_E1",
						}, {
							BusinessID: "AG-1", Keyword: "alpha", LanguageCode: "en", Location: &dataforseo.MapLocation{
								Latitude: 9.981801338881853, Longitude: 19.990825038597745, Zoom: 13,
							}, Vicinity: "VICINITY_E2",
						}, {
							BusinessID: "AG-1", Keyword: "alpha", LanguageCode: "en",
							Location: &dataforseo.MapLocation{Latitude: 9.981801338881853, Longitude: 20, Zoom: 13},
							Vicinity: "VICINITY_E3",
						}, {
							BusinessID: "AG-1", Keyword: "alpha", LanguageCode: "en", Location: &dataforseo.MapLocation{
								Latitude: 9.981801338881853, Longitude: 20.009174961402255, Zoom: 13,
							}, Vicinity: "VICINITY_E4",
						},
					},
				},
				{
					Req: []*dataforseo.GoogleMapsSERPTaskPostRequest{
						{
							BusinessID: "AG-1", Keyword: "alpha", LanguageCode: "en", Location: &dataforseo.MapLocation{
								Latitude: 9.981801338881853, Longitude: 20.01834992280451, Zoom: 13,
							}, Vicinity: "VICINITY_E5",
						}, {
							BusinessID: "AG-1", Keyword: "beta", LanguageCode: "en", Location: &dataforseo.MapLocation{
								Latitude: 10.***************, Longitude: 19.**************, Zoom: 13,
							}, Vicinity: "VICINITY_A1",
						}, {
							BusinessID: "AG-1", Keyword: "beta", LanguageCode: "en", Location: &dataforseo.MapLocation{
								Latitude: 10.***************, Longitude: 19.990825038597745, Zoom: 13,
							}, Vicinity: "VICINITY_A2",
						}, {
							BusinessID: "AG-1", Keyword: "beta", LanguageCode: "en",
							Location: &dataforseo.MapLocation{Latitude: 10.***************, Longitude: 20, Zoom: 13},
							Vicinity: "VICINITY_A3",
						}, {
							BusinessID: "AG-1", Keyword: "beta", LanguageCode: "en", Location: &dataforseo.MapLocation{
								Latitude: 10.***************, Longitude: 20.009174961402255, Zoom: 13,
							}, Vicinity: "VICINITY_A4",
						}, {
							BusinessID: "AG-1", Keyword: "beta", LanguageCode: "en", Location: &dataforseo.MapLocation{
								Latitude: 10.***************, Longitude: 20.01834992280451, Zoom: 13,
							}, Vicinity: "VICINITY_A5",
						}, {
							BusinessID: "AG-1", Keyword: "beta", LanguageCode: "en", Location: &dataforseo.MapLocation{
								Latitude: 10.009099330559074, Longitude: 19.**************, Zoom: 13,
							}, Vicinity: "VICINITY_B1",
						}, {
							BusinessID: "AG-1", Keyword: "beta", LanguageCode: "en", Location: &dataforseo.MapLocation{
								Latitude: 10.009099330559074, Longitude: 19.990825038597745, Zoom: 13,
							}, Vicinity: "VICINITY_B2",
						}, {
							BusinessID: "AG-1", Keyword: "beta", LanguageCode: "en",
							Location: &dataforseo.MapLocation{Latitude: 10.009099330559074, Longitude: 20, Zoom: 13},
							Vicinity: "VICINITY_B3",
						}, {
							BusinessID: "AG-1", Keyword: "beta", LanguageCode: "en", Location: &dataforseo.MapLocation{
								Latitude: 10.009099330559074, Longitude: 20.009174961402255, Zoom: 13,
							}, Vicinity: "VICINITY_B4",
						}, {
							BusinessID: "AG-1", Keyword: "beta", LanguageCode: "en", Location: &dataforseo.MapLocation{
								Latitude: 10.009099330559074, Longitude: 20.01834992280451, Zoom: 13,
							}, Vicinity: "VICINITY_B5",
						}, {
							BusinessID: "AG-1", Keyword: "beta", LanguageCode: "en",
							Location: &dataforseo.MapLocation{Latitude: 10, Longitude: 19.**************, Zoom: 13},
							Vicinity: "VICINITY_C1",
						},
					},
				},
				{
					Req: []*dataforseo.GoogleMapsSERPTaskPostRequest{
						{
							BusinessID: "AG-1", Keyword: "beta", LanguageCode: "en",
							Location: &dataforseo.MapLocation{Latitude: 10, Longitude: 19.990825038597745, Zoom: 13},
							Vicinity: "VICINITY_C2",
						},
						{
							BusinessID: "AG-1", Keyword: "beta", LanguageCode: "en",
							Location: &dataforseo.MapLocation{Latitude: 10, Longitude: 20, Zoom: 13},
							Vicinity: "VICINITY_C3",
						},
						{
							BusinessID: "AG-1", Keyword: "beta", LanguageCode: "en",
							Location: &dataforseo.MapLocation{Latitude: 10, Longitude: 20.009174961402255, Zoom: 13},
							Vicinity: "VICINITY_C4",
						},
						{
							BusinessID: "AG-1", Keyword: "beta", LanguageCode: "en",
							Location: &dataforseo.MapLocation{Latitude: 10, Longitude: 20.01834992280451, Zoom: 13},
							Vicinity: "VICINITY_C5",
						},
						{
							BusinessID: "AG-1", Keyword: "beta", LanguageCode: "en", Location: &dataforseo.MapLocation{
								Latitude: 9.990900669440926, Longitude: 19.**************, Zoom: 13,
							}, Vicinity: "VICINITY_D1",
						},
						{
							BusinessID: "AG-1", Keyword: "beta", LanguageCode: "en", Location: &dataforseo.MapLocation{
								Latitude: 9.990900669440926, Longitude: 19.990825038597745, Zoom: 13,
							}, Vicinity: "VICINITY_D2",
						},
						{
							BusinessID: "AG-1", Keyword: "beta", LanguageCode: "en",
							Location: &dataforseo.MapLocation{Latitude: 9.990900669440926, Longitude: 20, Zoom: 13},
							Vicinity: "VICINITY_D3",
						},
						{
							BusinessID: "AG-1", Keyword: "beta", LanguageCode: "en", Location: &dataforseo.MapLocation{
								Latitude: 9.990900669440926, Longitude: 20.009174961402255, Zoom: 13,
							}, Vicinity: "VICINITY_D4",
						},
						{
							BusinessID: "AG-1", Keyword: "beta", LanguageCode: "en", Location: &dataforseo.MapLocation{
								Latitude: 9.990900669440926, Longitude: 20.01834992280451, Zoom: 13,
							}, Vicinity: "VICINITY_D5",
						},
						{
							BusinessID: "AG-1", Keyword: "beta", LanguageCode: "en", Location: &dataforseo.MapLocation{
								Latitude: 9.981801338881853, Longitude: 19.**************, Zoom: 13,
							}, Vicinity: "VICINITY_E1",
						},
						{
							BusinessID: "AG-1", Keyword: "beta", LanguageCode: "en", Location: &dataforseo.MapLocation{
								Latitude: 9.981801338881853, Longitude: 19.990825038597745, Zoom: 13,
							}, Vicinity: "VICINITY_E2",
						},
						{
							BusinessID: "AG-1", Keyword: "beta", LanguageCode: "en",
							Location: &dataforseo.MapLocation{Latitude: 9.981801338881853, Longitude: 20, Zoom: 13},
							Vicinity: "VICINITY_E3",
						},
					},
				},
				{
					Req: []*dataforseo.GoogleMapsSERPTaskPostRequest{
						{
							BusinessID: "AG-1", Keyword: "beta", LanguageCode: "en", Location: &dataforseo.MapLocation{
								Latitude: 9.981801338881853, Longitude: 20.009174961402255, Zoom: 13,
							}, Vicinity: "VICINITY_E4",
						},
						{
							BusinessID: "AG-1", Keyword: "beta", LanguageCode: "en", Location: &dataforseo.MapLocation{
								Latitude: 9.981801338881853, Longitude: 20.01834992280451, Zoom: 13,
							}, Vicinity: "VICINITY_E5",
						},
					},
				},
			},
			dataForSEOOrganicPosts: []*DataForSeoOrganicPost{
				{
					Req: []*dataforseo.GoogleOrganicSERPTaskPostRequest{
						{
							BusinessID:   "AG-1",
							Vicinity:     "VICINITY_CITY",
							Keyword:      "alpha",
							LanguageCode: "en",
							Location:     &dataforseo.OrganicSERPLocation{Latitude: 10, Longitude: 20, Radius: 20000},
							Tags:         nil,
						},
						{
							BusinessID:   "AG-1",
							Vicinity:     "VICINITY_CITY",
							Keyword:      "beta",
							LanguageCode: "en",
							Location:     &dataforseo.OrganicSERPLocation{Latitude: 10, Longitude: 20, Radius: 20000},
							Tags:         nil,
						},
					},
				},
			},
			taskItems: []*dataforseo.GoogleMapsItem{
				{
					Type:         "maps_search",
					Rank:         1,
					BusinessName: "Name1",
					URL:          "Website1",
					Address:      "Address1",
					Phone:        "**************",
				},
				{
					Type:         "maps_search",
					Rank:         2,
					BusinessName: "Name2",
					URL:          "Website2",
					Address:      "Address2",
					Phone:        "**************",
				},
				{
					Type:         "maps_search",
					Rank:         3,
					BusinessName: "NOMATCH",
					URL:          "NOMATCH",
					Address:      "NOMATCH",
					Phone:        "**************",
				},
				{
					Type:         "maps_paid_item",
					Rank:         1,
					BusinessName: "Richman",
					URL:          "IBuyAds.com",
					Address:      "Address1",
					Phone:        "**************",
				},
			},
			organicTaskItems: []*dataforseo.GoogleOrganicItem{
				{
					Type:      "organic",
					RankGroup: 1,
					Title:     "Name1",
					URL:       "Website1",
				},
				{
					Type:      "organic",
					RankGroup: 3,
					Title:     "NOMATCH",
					URL:       "NOMATCH",
				},
				{
					Type:      "organic",
					RankGroup: 1,
					Title:     "Richman",
					URL:       "IBuyAds.com",
				},
			},
			expectedLocalSearchDataCalls: []*LocalSEODataEntries{
				{
					businessID: "AG-1",
					keyword:    "alpha",
					date:       "2023-06-01",
					entries: []*seodata.LocalSearchData{
						{
							Keyword: "alpha", Vicinity: "VICINITY_A1",
							SearchLocation: &seodata.Geo{Latitude: 10.***************, Longitude: 19.**************},
							Results: []*seodata.LocalSearchResult{
								{
									BusinessName: "Name1", Address: "Address1", Url: "Website1", Rank: 1,
									IsMainBusiness: true, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "Name2", Address: "Address2", Url: "Website2", Rank: 2,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "NOMATCH", Address: "NOMATCH", Url: "NOMATCH", Rank: 3,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								},
							},
						}, {
							Keyword: "alpha", Vicinity: "VICINITY_A2",
							SearchLocation: &seodata.Geo{Latitude: 10.***************, Longitude: 19.990825038597745},
							Results: []*seodata.LocalSearchResult{
								{
									BusinessName: "Name1", Address: "Address1", Url: "Website1", Rank: 1,
									IsMainBusiness: true, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "Name2", Address: "Address2", Url: "Website2", Rank: 2,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "NOMATCH", Address: "NOMATCH", Url: "NOMATCH", Rank: 3,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								},
							},
						}, {
							Keyword: "alpha", Vicinity: "VICINITY_A3",
							SearchLocation: &seodata.Geo{Latitude: 10.***************, Longitude: 20},
							Results: []*seodata.LocalSearchResult{
								{
									BusinessName: "Name1", Address: "Address1", Url: "Website1", Rank: 1,
									IsMainBusiness: true, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "Name2", Address: "Address2", Url: "Website2", Rank: 2,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "NOMATCH", Address: "NOMATCH", Url: "NOMATCH", Rank: 3,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								},
							},
						}, {
							Keyword: "alpha", Vicinity: "VICINITY_A4",
							SearchLocation: &seodata.Geo{Latitude: 10.***************, Longitude: 20.009174961402255},
							Results: []*seodata.LocalSearchResult{
								{
									BusinessName: "Name1", Address: "Address1", Url: "Website1", Rank: 1,
									IsMainBusiness: true, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "Name2", Address: "Address2", Url: "Website2", Rank: 2,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "NOMATCH", Address: "NOMATCH", Url: "NOMATCH", Rank: 3,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								},
							},
						}, {
							Keyword: "alpha", Vicinity: "VICINITY_A5",
							SearchLocation: &seodata.Geo{Latitude: 10.***************, Longitude: 20.01834992280451},
							Results: []*seodata.LocalSearchResult{
								{
									BusinessName: "Name1", Address: "Address1", Url: "Website1", Rank: 1,
									IsMainBusiness: true, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "Name2", Address: "Address2", Url: "Website2", Rank: 2,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "NOMATCH", Address: "NOMATCH", Url: "NOMATCH", Rank: 3,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								},
							},
						}, {
							Keyword: "alpha", Vicinity: "VICINITY_B1",
							SearchLocation: &seodata.Geo{Latitude: 10.009099330559074, Longitude: 19.**************},
							Results: []*seodata.LocalSearchResult{
								{
									BusinessName: "Name1", Address: "Address1", Url: "Website1", Rank: 1,
									IsMainBusiness: true, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "Name2", Address: "Address2", Url: "Website2", Rank: 2,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "NOMATCH", Address: "NOMATCH", Url: "NOMATCH", Rank: 3,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								},
							},
						}, {
							Keyword: "alpha", Vicinity: "VICINITY_B2",
							SearchLocation: &seodata.Geo{Latitude: 10.009099330559074, Longitude: 19.990825038597745},
							Results: []*seodata.LocalSearchResult{
								{
									BusinessName: "Name1", Address: "Address1", Url: "Website1", Rank: 1,
									IsMainBusiness: true, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "Name2", Address: "Address2", Url: "Website2", Rank: 2,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "NOMATCH", Address: "NOMATCH", Url: "NOMATCH", Rank: 3,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								},
							},
						}, {
							Keyword: "alpha", Vicinity: "VICINITY_B3",
							SearchLocation: &seodata.Geo{Latitude: 10.009099330559074, Longitude: 20},
							Results: []*seodata.LocalSearchResult{
								{
									BusinessName: "Name1", Address: "Address1", Url: "Website1", Rank: 1,
									IsMainBusiness: true, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "Name2", Address: "Address2", Url: "Website2", Rank: 2,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "NOMATCH", Address: "NOMATCH", Url: "NOMATCH", Rank: 3,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								},
							},
						}, {
							Keyword: "alpha", Vicinity: "VICINITY_B4",
							SearchLocation: &seodata.Geo{Latitude: 10.009099330559074, Longitude: 20.009174961402255},
							Results: []*seodata.LocalSearchResult{
								{
									BusinessName: "Name1", Address: "Address1", Url: "Website1", Rank: 1,
									IsMainBusiness: true, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "Name2", Address: "Address2", Url: "Website2", Rank: 2,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "NOMATCH", Address: "NOMATCH", Url: "NOMATCH", Rank: 3,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								},
							},
						}, {
							Keyword: "alpha", Vicinity: "VICINITY_B5",
							SearchLocation: &seodata.Geo{Latitude: 10.009099330559074, Longitude: 20.01834992280451},
							Results: []*seodata.LocalSearchResult{
								{
									BusinessName: "Name1", Address: "Address1", Url: "Website1", Rank: 1,
									IsMainBusiness: true, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "Name2", Address: "Address2", Url: "Website2", Rank: 2,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "NOMATCH", Address: "NOMATCH", Url: "NOMATCH", Rank: 3,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								},
							},
						}, {
							Keyword: "alpha", Vicinity: "VICINITY_C1",
							SearchLocation: &seodata.Geo{Latitude: 10, Longitude: 19.**************},
							Results: []*seodata.LocalSearchResult{
								{
									BusinessName: "Name1", Address: "Address1", Url: "Website1", Rank: 1,
									IsMainBusiness: true, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "Name2", Address: "Address2", Url: "Website2", Rank: 2,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "NOMATCH", Address: "NOMATCH", Url: "NOMATCH", Rank: 3,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								},
							},
						}, {
							Keyword: "alpha", Vicinity: "VICINITY_C2",
							SearchLocation: &seodata.Geo{Latitude: 10, Longitude: 19.990825038597745},
							Results: []*seodata.LocalSearchResult{
								{
									BusinessName: "Name1", Address: "Address1", Url: "Website1", Rank: 1,
									IsMainBusiness: true, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "Name2", Address: "Address2", Url: "Website2", Rank: 2,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "NOMATCH", Address: "NOMATCH", Url: "NOMATCH", Rank: 3,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								},
							},
						}, {
							Keyword: "alpha", Vicinity: "VICINITY_C3",
							SearchLocation: &seodata.Geo{Latitude: 10, Longitude: 20},
							Results: []*seodata.LocalSearchResult{
								{
									BusinessName: "Name1", Address: "Address1", Url: "Website1", Rank: 1,
									IsMainBusiness: true, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "Name2", Address: "Address2", Url: "Website2", Rank: 2,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "NOMATCH", Address: "NOMATCH", Url: "NOMATCH", Rank: 3,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								},
							},
						}, {
							Keyword: "alpha", Vicinity: "VICINITY_C4",
							SearchLocation: &seodata.Geo{Latitude: 10, Longitude: 20.009174961402255},
							Results: []*seodata.LocalSearchResult{
								{
									BusinessName: "Name1", Address: "Address1", Url: "Website1", Rank: 1,
									IsMainBusiness: true, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "Name2", Address: "Address2", Url: "Website2", Rank: 2,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "NOMATCH", Address: "NOMATCH", Url: "NOMATCH", Rank: 3,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								},
							},
						}, {
							Keyword: "alpha", Vicinity: "VICINITY_C5",
							SearchLocation: &seodata.Geo{Latitude: 10, Longitude: 20.01834992280451},
							Results: []*seodata.LocalSearchResult{
								{
									BusinessName: "Name1", Address: "Address1", Url: "Website1", Rank: 1,
									IsMainBusiness: true, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "Name2", Address: "Address2", Url: "Website2", Rank: 2,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "NOMATCH", Address: "NOMATCH", Url: "NOMATCH", Rank: 3,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								},
							},
						}, {
							Keyword: "alpha", Vicinity: "VICINITY_D1",
							SearchLocation: &seodata.Geo{Latitude: 9.990900669440926, Longitude: 19.**************},
							Results: []*seodata.LocalSearchResult{
								{
									BusinessName: "Name1", Address: "Address1", Url: "Website1", Rank: 1,
									IsMainBusiness: true, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "Name2", Address: "Address2", Url: "Website2", Rank: 2,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "NOMATCH", Address: "NOMATCH", Url: "NOMATCH", Rank: 3,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								},
							},
						}, {
							Keyword: "alpha", Vicinity: "VICINITY_D2",
							SearchLocation: &seodata.Geo{Latitude: 9.990900669440926, Longitude: 19.990825038597745},
							Results: []*seodata.LocalSearchResult{
								{
									BusinessName: "Name1", Address: "Address1", Url: "Website1", Rank: 1,
									IsMainBusiness: true, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "Name2", Address: "Address2", Url: "Website2", Rank: 2,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "NOMATCH", Address: "NOMATCH", Url: "NOMATCH", Rank: 3,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								},
							},
						}, {
							Keyword: "alpha", Vicinity: "VICINITY_D3",
							SearchLocation: &seodata.Geo{Latitude: 9.990900669440926, Longitude: 20},
							Results: []*seodata.LocalSearchResult{
								{
									BusinessName: "Name1", Address: "Address1", Url: "Website1", Rank: 1,
									IsMainBusiness: true, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "Name2", Address: "Address2", Url: "Website2", Rank: 2,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "NOMATCH", Address: "NOMATCH", Url: "NOMATCH", Rank: 3,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								},
							},
						}, {
							Keyword: "alpha", Vicinity: "VICINITY_D4",
							SearchLocation: &seodata.Geo{Latitude: 9.990900669440926, Longitude: 20.009174961402255},
							Results: []*seodata.LocalSearchResult{
								{
									BusinessName: "Name1", Address: "Address1", Url: "Website1", Rank: 1,
									IsMainBusiness: true, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "Name2", Address: "Address2", Url: "Website2", Rank: 2,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "NOMATCH", Address: "NOMATCH", Url: "NOMATCH", Rank: 3,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								},
							},
						}, {
							Keyword: "alpha", Vicinity: "VICINITY_D5",
							SearchLocation: &seodata.Geo{Latitude: 9.990900669440926, Longitude: 20.01834992280451},
							Results: []*seodata.LocalSearchResult{
								{
									BusinessName: "Name1", Address: "Address1", Url: "Website1", Rank: 1,
									IsMainBusiness: true, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "Name2", Address: "Address2", Url: "Website2", Rank: 2,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "NOMATCH", Address: "NOMATCH", Url: "NOMATCH", Rank: 3,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								},
							},
						}, {
							Keyword: "alpha", Vicinity: "VICINITY_E1",
							SearchLocation: &seodata.Geo{Latitude: 9.981801338881853, Longitude: 19.**************},
							Results: []*seodata.LocalSearchResult{
								{
									BusinessName: "Name1", Address: "Address1", Url: "Website1", Rank: 1,
									IsMainBusiness: true, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "Name2", Address: "Address2", Url: "Website2", Rank: 2,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "NOMATCH", Address: "NOMATCH", Url: "NOMATCH", Rank: 3,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								},
							},
						}, {
							Keyword: "alpha", Vicinity: "VICINITY_E2",
							SearchLocation: &seodata.Geo{Latitude: 9.981801338881853, Longitude: 19.990825038597745},
							Results: []*seodata.LocalSearchResult{
								{
									BusinessName: "Name1", Address: "Address1", Url: "Website1", Rank: 1,
									IsMainBusiness: true, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "Name2", Address: "Address2", Url: "Website2", Rank: 2,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "NOMATCH", Address: "NOMATCH", Url: "NOMATCH", Rank: 3,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								},
							},
						}, {
							Keyword: "alpha", Vicinity: "VICINITY_E3",
							SearchLocation: &seodata.Geo{Latitude: 9.981801338881853, Longitude: 20},
							Results: []*seodata.LocalSearchResult{
								{
									BusinessName: "Name1", Address: "Address1", Url: "Website1", Rank: 1,
									IsMainBusiness: true, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "Name2", Address: "Address2", Url: "Website2", Rank: 2,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "NOMATCH", Address: "NOMATCH", Url: "NOMATCH", Rank: 3,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								},
							},
						}, {
							Keyword: "alpha", Vicinity: "VICINITY_E4",
							SearchLocation: &seodata.Geo{Latitude: 9.981801338881853, Longitude: 20.009174961402255},
							Results: []*seodata.LocalSearchResult{
								{
									BusinessName: "Name1", Address: "Address1", Url: "Website1", Rank: 1,
									IsMainBusiness: true, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "Name2", Address: "Address2", Url: "Website2", Rank: 2,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "NOMATCH", Address: "NOMATCH", Url: "NOMATCH", Rank: 3,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								},
							},
						}, {
							Keyword: "alpha", Vicinity: "VICINITY_E5",
							SearchLocation: &seodata.Geo{Latitude: 9.981801338881853, Longitude: 20.01834992280451},
							Results: []*seodata.LocalSearchResult{
								{
									BusinessName: "Name1", Address: "Address1", Url: "Website1", Rank: 1,
									IsMainBusiness: true, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "Name2", Address: "Address2", Url: "Website2", Rank: 2,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "NOMATCH", Address: "NOMATCH", Url: "NOMATCH", Rank: 3,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								},
							},
						},
					},
				},
				{
					businessID: "AG-1",
					keyword:    "beta",
					date:       "2023-06-01",
					entries: []*seodata.LocalSearchData{
						{
							Keyword: "beta", Vicinity: "VICINITY_A1",
							SearchLocation: &seodata.Geo{Latitude: 10.***************, Longitude: 19.**************},
							Results: []*seodata.LocalSearchResult{
								{
									BusinessName: "Name1", Address: "Address1", Url: "Website1", Rank: 1,
									IsMainBusiness: true, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "Name2", Address: "Address2", Url: "Website2", Rank: 2,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "NOMATCH", Address: "NOMATCH", Url: "NOMATCH", Rank: 3,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								},
							},
						}, {
							Keyword: "beta", Vicinity: "VICINITY_A2",
							SearchLocation: &seodata.Geo{Latitude: 10.***************, Longitude: 19.990825038597745},
							Results: []*seodata.LocalSearchResult{
								{
									BusinessName: "Name1", Address: "Address1", Url: "Website1", Rank: 1,
									IsMainBusiness: true, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "Name2", Address: "Address2", Url: "Website2", Rank: 2,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "NOMATCH", Address: "NOMATCH", Url: "NOMATCH", Rank: 3,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								},
							},
						}, {
							Keyword: "beta", Vicinity: "VICINITY_A3",
							SearchLocation: &seodata.Geo{Latitude: 10.***************, Longitude: 20},
							Results: []*seodata.LocalSearchResult{
								{
									BusinessName: "Name1", Address: "Address1", Url: "Website1", Rank: 1,
									IsMainBusiness: true, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "Name2", Address: "Address2", Url: "Website2", Rank: 2,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "NOMATCH", Address: "NOMATCH", Url: "NOMATCH", Rank: 3,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								},
							},
						}, {
							Keyword: "beta", Vicinity: "VICINITY_A4",
							SearchLocation: &seodata.Geo{Latitude: 10.***************, Longitude: 20.009174961402255},
							Results: []*seodata.LocalSearchResult{
								{
									BusinessName: "Name1", Address: "Address1", Url: "Website1", Rank: 1,
									IsMainBusiness: true, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "Name2", Address: "Address2", Url: "Website2", Rank: 2,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "NOMATCH", Address: "NOMATCH", Url: "NOMATCH", Rank: 3,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								},
							},
						}, {
							Keyword: "beta", Vicinity: "VICINITY_A5",
							SearchLocation: &seodata.Geo{Latitude: 10.***************, Longitude: 20.01834992280451},
							Results: []*seodata.LocalSearchResult{
								{
									BusinessName: "Name1", Address: "Address1", Url: "Website1", Rank: 1,
									IsMainBusiness: true, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "Name2", Address: "Address2", Url: "Website2", Rank: 2,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "NOMATCH", Address: "NOMATCH", Url: "NOMATCH", Rank: 3,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								},
							},
						}, {
							Keyword: "beta", Vicinity: "VICINITY_B1",
							SearchLocation: &seodata.Geo{Latitude: 10.009099330559074, Longitude: 19.**************},
							Results: []*seodata.LocalSearchResult{
								{
									BusinessName: "Name1", Address: "Address1", Url: "Website1", Rank: 1,
									IsMainBusiness: true, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "Name2", Address: "Address2", Url: "Website2", Rank: 2,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "NOMATCH", Address: "NOMATCH", Url: "NOMATCH", Rank: 3,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								},
							},
						}, {
							Keyword: "beta", Vicinity: "VICINITY_B2",
							SearchLocation: &seodata.Geo{Latitude: 10.009099330559074, Longitude: 19.990825038597745},
							Results: []*seodata.LocalSearchResult{
								{
									BusinessName: "Name1", Address: "Address1", Url: "Website1", Rank: 1,
									IsMainBusiness: true, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "Name2", Address: "Address2", Url: "Website2", Rank: 2,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "NOMATCH", Address: "NOMATCH", Url: "NOMATCH", Rank: 3,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								},
							},
						}, {
							Keyword: "beta", Vicinity: "VICINITY_B3",
							SearchLocation: &seodata.Geo{Latitude: 10.009099330559074, Longitude: 20},
							Results: []*seodata.LocalSearchResult{
								{
									BusinessName: "Name1", Address: "Address1", Url: "Website1", Rank: 1,
									IsMainBusiness: true, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "Name2", Address: "Address2", Url: "Website2", Rank: 2,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "NOMATCH", Address: "NOMATCH", Url: "NOMATCH", Rank: 3,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								},
							},
						}, {
							Keyword: "beta", Vicinity: "VICINITY_B4",
							SearchLocation: &seodata.Geo{Latitude: 10.009099330559074, Longitude: 20.009174961402255},
							Results: []*seodata.LocalSearchResult{
								{
									BusinessName: "Name1", Address: "Address1", Url: "Website1", Rank: 1,
									IsMainBusiness: true, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "Name2", Address: "Address2", Url: "Website2", Rank: 2,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "NOMATCH", Address: "NOMATCH", Url: "NOMATCH", Rank: 3,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								},
							},
						}, {
							Keyword: "beta", Vicinity: "VICINITY_B5",
							SearchLocation: &seodata.Geo{Latitude: 10.009099330559074, Longitude: 20.01834992280451},
							Results: []*seodata.LocalSearchResult{
								{
									BusinessName: "Name1", Address: "Address1", Url: "Website1", Rank: 1,
									IsMainBusiness: true, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "Name2", Address: "Address2", Url: "Website2", Rank: 2,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "NOMATCH", Address: "NOMATCH", Url: "NOMATCH", Rank: 3,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								},
							},
						}, {
							Keyword: "beta", Vicinity: "VICINITY_C1",
							SearchLocation: &seodata.Geo{Latitude: 10, Longitude: 19.**************},
							Results: []*seodata.LocalSearchResult{
								{
									BusinessName: "Name1", Address: "Address1", Url: "Website1", Rank: 1,
									IsMainBusiness: true, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "Name2", Address: "Address2", Url: "Website2", Rank: 2,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "NOMATCH", Address: "NOMATCH", Url: "NOMATCH", Rank: 3,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								},
							},
						}, {
							Keyword: "beta", Vicinity: "VICINITY_C2",
							SearchLocation: &seodata.Geo{Latitude: 10, Longitude: 19.990825038597745},
							Results: []*seodata.LocalSearchResult{
								{
									BusinessName: "Name1", Address: "Address1", Url: "Website1", Rank: 1,
									IsMainBusiness: true, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "Name2", Address: "Address2", Url: "Website2", Rank: 2,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "NOMATCH", Address: "NOMATCH", Url: "NOMATCH", Rank: 3,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								},
							},
						}, {
							Keyword: "beta", Vicinity: "VICINITY_C3",
							SearchLocation: &seodata.Geo{Latitude: 10, Longitude: 20},
							Results: []*seodata.LocalSearchResult{
								{
									BusinessName: "Name1", Address: "Address1", Url: "Website1", Rank: 1,
									IsMainBusiness: true, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "Name2", Address: "Address2", Url: "Website2", Rank: 2,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "NOMATCH", Address: "NOMATCH", Url: "NOMATCH", Rank: 3,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								},
							},
						}, {
							Keyword: "beta", Vicinity: "VICINITY_C4",
							SearchLocation: &seodata.Geo{Latitude: 10, Longitude: 20.009174961402255},
							Results: []*seodata.LocalSearchResult{
								{
									BusinessName: "Name1", Address: "Address1", Url: "Website1", Rank: 1,
									IsMainBusiness: true, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "Name2", Address: "Address2", Url: "Website2", Rank: 2,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "NOMATCH", Address: "NOMATCH", Url: "NOMATCH", Rank: 3,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								},
							},
						}, {
							Keyword: "beta", Vicinity: "VICINITY_C5",
							SearchLocation: &seodata.Geo{Latitude: 10, Longitude: 20.01834992280451},
							Results: []*seodata.LocalSearchResult{
								{
									BusinessName: "Name1", Address: "Address1", Url: "Website1", Rank: 1,
									IsMainBusiness: true, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "Name2", Address: "Address2", Url: "Website2", Rank: 2,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "NOMATCH", Address: "NOMATCH", Url: "NOMATCH", Rank: 3,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								},
							},
						}, {
							Keyword: "beta", Vicinity: "VICINITY_D1",
							SearchLocation: &seodata.Geo{Latitude: 9.990900669440926, Longitude: 19.**************},
							Results: []*seodata.LocalSearchResult{
								{
									BusinessName: "Name1", Address: "Address1", Url: "Website1", Rank: 1,
									IsMainBusiness: true, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "Name2", Address: "Address2", Url: "Website2", Rank: 2,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "NOMATCH", Address: "NOMATCH", Url: "NOMATCH", Rank: 3,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								},
							},
						}, {
							Keyword: "beta", Vicinity: "VICINITY_D2",
							SearchLocation: &seodata.Geo{Latitude: 9.990900669440926, Longitude: 19.990825038597745},
							Results: []*seodata.LocalSearchResult{
								{
									BusinessName: "Name1", Address: "Address1", Url: "Website1", Rank: 1,
									IsMainBusiness: true, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "Name2", Address: "Address2", Url: "Website2", Rank: 2,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "NOMATCH", Address: "NOMATCH", Url: "NOMATCH", Rank: 3,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								},
							},
						}, {
							Keyword: "beta", Vicinity: "VICINITY_D3",
							SearchLocation: &seodata.Geo{Latitude: 9.990900669440926, Longitude: 20},
							Results: []*seodata.LocalSearchResult{
								{
									BusinessName: "Name1", Address: "Address1", Url: "Website1", Rank: 1,
									IsMainBusiness: true, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "Name2", Address: "Address2", Url: "Website2", Rank: 2,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "NOMATCH", Address: "NOMATCH", Url: "NOMATCH", Rank: 3,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								},
							},
						}, {
							Keyword: "beta", Vicinity: "VICINITY_D4",
							SearchLocation: &seodata.Geo{Latitude: 9.990900669440926, Longitude: 20.009174961402255},
							Results: []*seodata.LocalSearchResult{
								{
									BusinessName: "Name1", Address: "Address1", Url: "Website1", Rank: 1,
									IsMainBusiness: true, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "Name2", Address: "Address2", Url: "Website2", Rank: 2,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "NOMATCH", Address: "NOMATCH", Url: "NOMATCH", Rank: 3,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								},
							},
						}, {
							Keyword: "beta", Vicinity: "VICINITY_D5",
							SearchLocation: &seodata.Geo{Latitude: 9.990900669440926, Longitude: 20.01834992280451},
							Results: []*seodata.LocalSearchResult{
								{
									BusinessName: "Name1", Address: "Address1", Url: "Website1", Rank: 1,
									IsMainBusiness: true, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "Name2", Address: "Address2", Url: "Website2", Rank: 2,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "NOMATCH", Address: "NOMATCH", Url: "NOMATCH", Rank: 3,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								},
							},
						}, {
							Keyword: "beta", Vicinity: "VICINITY_E1",
							SearchLocation: &seodata.Geo{Latitude: 9.981801338881853, Longitude: 19.**************},
							Results: []*seodata.LocalSearchResult{
								{
									BusinessName: "Name1", Address: "Address1", Url: "Website1", Rank: 1,
									IsMainBusiness: true, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "Name2", Address: "Address2", Url: "Website2", Rank: 2,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "NOMATCH", Address: "NOMATCH", Url: "NOMATCH", Rank: 3,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								},
							},
						}, {
							Keyword: "beta", Vicinity: "VICINITY_E2",
							SearchLocation: &seodata.Geo{Latitude: 9.981801338881853, Longitude: 19.990825038597745},
							Results: []*seodata.LocalSearchResult{
								{
									BusinessName: "Name1", Address: "Address1", Url: "Website1", Rank: 1,
									IsMainBusiness: true, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "Name2", Address: "Address2", Url: "Website2", Rank: 2,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "NOMATCH", Address: "NOMATCH", Url: "NOMATCH", Rank: 3,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								},
							},
						}, {
							Keyword: "beta", Vicinity: "VICINITY_E3",
							SearchLocation: &seodata.Geo{Latitude: 9.981801338881853, Longitude: 20},
							Results: []*seodata.LocalSearchResult{
								{
									BusinessName: "Name1", Address: "Address1", Url: "Website1", Rank: 1,
									IsMainBusiness: true, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "Name2", Address: "Address2", Url: "Website2", Rank: 2,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "NOMATCH", Address: "NOMATCH", Url: "NOMATCH", Rank: 3,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								},
							},
						}, {
							Keyword: "beta", Vicinity: "VICINITY_E4",
							SearchLocation: &seodata.Geo{Latitude: 9.981801338881853, Longitude: 20.009174961402255},
							Results: []*seodata.LocalSearchResult{
								{
									BusinessName: "Name1", Address: "Address1", Url: "Website1", Rank: 1,
									IsMainBusiness: true, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "Name2", Address: "Address2", Url: "Website2", Rank: 2,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "NOMATCH", Address: "NOMATCH", Url: "NOMATCH", Rank: 3,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								},
							},
						}, {
							Keyword: "beta", Vicinity: "VICINITY_E5",
							SearchLocation: &seodata.Geo{Latitude: 9.981801338881853, Longitude: 20.01834992280451},
							Results: []*seodata.LocalSearchResult{
								{
									BusinessName: "Name1", Address: "Address1", Url: "Website1", Rank: 1,
									IsMainBusiness: true, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "Name2", Address: "Address2", Url: "Website2", Rank: 2,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								}, {
									BusinessName: "NOMATCH", Address: "NOMATCH", Url: "NOMATCH", Rank: 3,
									IsMainBusiness: false, Reviews: &seodata.LocalSearchReviews{Rating: 0, Count: "0"},
									PhoneNumber: "**************", ClaimStatus: "",
								},
							},
						},
					},
				},
			},
			expectedOrganicSearchDataCalls: []*OrganicSEODataEntries{
				{
					businessID: "AG-1",
					keyword:    "alpha",
					date:       "2023-06-01",
					rank:       1,
				},
				{
					businessID: "AG-1",
					keyword:    "beta",
					date:       "2023-06-01",
					rank:       1,
				},
			},
			expectedResponse:    nil,
			expectedWorkflowErr: nil,
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			ctx := context.Background()
			ctrl := gomock.NewController(t)
			mockLpService := listingprofileservice.NewMockInterface(ctrl)

			// Mock out expected listing profiles
			mockLpService.EXPECT().Get(gomock.Any(), c.listingProfile.BusinessID, false).Return(c.listingProfile, nil).AnyTimes()

			mockSEODataService := seodataservice.NewMockService(ctrl)
			mockSEODataService.EXPECT().QueryDateRange(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
			mockSEODataService.EXPECT().Upsert(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

			mockDataForSEO := dataforseo.NewMockSERPClient(ctrl)

			mockSEOSettingsService := seosettingsservice.NewMockService(ctrl)

			mockAccounts := mocks.NewMockAccountsServiceInterface(ctrl)
			mockAccounts.EXPECT().GetMultiActivationStatuses(gomock.Any(), gomock.Any()).Return(c.activations, nil).AnyTimes()

			// Mock out expected seo settings
			mockSEOSettingsService.EXPECT().Get(gomock.Any(), c.seoSettings.BusinessID).Return(&seosettingsmodel.SEOSettings{
				BusinessID:        c.seoSettings.BusinessID,
				LocalSearchRadius: c.seoSettings.LocalSearchRadius,
			}, nil).AnyTimes()

			// For every task we post to DataForSEO, mock out the initial response with the Task IDs
			// Also mock out the individual responses for when each task have its results fetched later
			for _, post := range c.dataForSEOPosts {
				resp := &dataforseo.GoogleMapsSERPResponse{Tasks: createMockTaskResponseForReq(post.Req)}
				mockDataForSEO.EXPECT().GoogleMapsSERPTaskPost(gomock.Any(), c.workflowParams.Date, c.workflowParams.IgnoreDataLakeResults, post.Req, gomock.Any()).Return(resp, nil).Times(1)

				for _, get := range createMockTaskGetResponseForReq(post.Req, c.taskItems) {
					mockDataForSEO.EXPECT().GoogleMapsSERPTaskGet(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), get.Tasks[0].ID, c.workflowParams.IgnoreDataLakeResults).Return(get, nil).Times(1)
				}
			}

			// For every task we post to DataForSEO, mock out the initial response with the Task IDs
			// Also mock out the individual responses for when each task have its results fetched later
			for _, post := range c.dataForSEOOrganicPosts {
				resp := &dataforseo.GoogleOrganicSERPResponse{Tasks: createMockOrganicTaskResponseForReq(post.Req)}
				mockDataForSEO.EXPECT().GoogleOrganicSERPTaskPost(gomock.Any(), c.workflowParams.Date, c.workflowParams.IgnoreDataLakeResults, post.Req, gomock.Any()).Return(resp, nil).Times(1)

				for _, get := range createMockOrganicTaskGetResponseForReq(post.Req, c.organicTaskItems) {
					mockDataForSEO.EXPECT().GoogleOrganicSERPTaskGet(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), get.Tasks[0].ID, c.workflowParams.IgnoreDataLakeResults).Return(get, nil).Times(1)
				}
			}

			vmockhelper.MockCallsAndPrintExpected(mockDataForSEO, "mockDataForSEO")

			// Mock out expected data service calls to store the data in vstore
			for _, dataCall := range c.expectedLocalSearchDataCalls {
				mockSEODataService.EXPECT().UpsertLocalSEOData(gomock.Any(), dataCall.businessID, dataCall.keyword, dataCall.date, dataCall.entries, gomock.Any(), gomock.Any()).Return(nil).Times(1)
			}

			for _, dataCall := range c.expectedOrganicSearchDataCalls {
				mockSEODataService.EXPECT().UpsertOrganicRank(gomock.Any(), dataCall.businessID, dataCall.keyword, dataCall.date, dataCall.rank).Return(nil)
			}

			vmockhelper.MockCallsAndPrintExpected(mockSEODataService, "mockSEODataService")

			mockCSGMB := cssdk.NewMockGoogleMyBusinessClientInterface(ctrl)
			mockCSGMB.EXPECT().GetGoogleMyBusinessLocation(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&cssdk.GoogleMyBusinessLocation{}, nil).AnyTimes()

			testSuite := &testsuite.WorkflowTestSuite{}
			env := testSuite.NewTestWorkflowEnvironment()
			env.RegisterWorkflow(LocalSEOWorkflow)
			env.RegisterActivity(buildRequests)
			env.RegisterActivity(processMapsRequests)
			env.RegisterActivity(processOrganicRequestBatch)
			env.RegisterActivity(sortTasks)
			env.RegisterActivity(storeMapsResults)
			env.RegisterActivity(storeMapsResultsV1) // New version with searchRadius parameter
			env.RegisterActivity(fetchMapsResult)
			env.RegisterActivity(getAndStoreOrganicResult)
			env.RegisterActivity(getConnectedGooglePlaceID)
			env.RegisterActivity(storeFailedWorkflowInfo)
			ctx = NewActivityContext(ctx, nil, mockLpService, nil, mockSEODataService, mockDataForSEO, mockSEOSettingsService, nil, nil, mockAccounts, nil, nil, nil, nil, mockCSGMB, nil, nil, nil, nil, nil, nil, nil)
			env.SetWorkerOptions(worker.Options{
				BackgroundActivityContext: ctx,
				DeadlockDetectionTimeout:  time.Second * 30,
			})
			env.ExecuteWorkflow(LocalSEOWorkflow, c.workflowParams)

			if !env.IsWorkflowCompleted() {
				t.Errorf("Workflow failed to complete. Error (%s)", env.GetWorkflowError())
			}

			assert.Equal(t, c.expectedWorkflowErr, env.GetWorkflowError())
		})
	}
}

func Test_LocalSEOWorkflowError(t *testing.T) {
	type keywordInfoCall struct {
		BusinessID   string
		Keyword      string
		Date         string
		LocationCode int64
		LocationName string
		Fetched      bool
	}

	type testCase struct {
		name                           string
		workflowParams                 *SERPWorkflowParams
		listingProfile                 *listingprofilemodel.ListingProfile
		seoSettings                    *seosettingsmodel.SEOSettings
		country                        *nap_v1.Country
		state                          *nap_v1.ListStatesResponse
		activations                    *accounts_v2.GetMultiActivationStatusesResponse
		expectedKeywordInfoCalls       []*keywordInfoCall
		dataForSEOPosts                []*DataForSeoPost
		dataForSEOOrganicPosts         []*DataForSeoOrganicPost
		dataForSEOGets                 []*DataForSeoGet
		taskItems                      []*dataforseo.GoogleMapsItem
		organicTaskItems               []*dataforseo.GoogleOrganicItem
		expectedLocalSearchDataCalls   []*LocalSEODataEntries
		expectedOrganicSearchDataCalls []*OrganicSEODataEntries
		expectedResponse               interface{}
		expectedWorkflowErr            error
	}
	cases := []*testCase{
		{
			name: "Test failed -precondition completes workflow for accounts",
			workflowParams: &SERPWorkflowParams{
				Date:       "2023-06-01",
				Force:      true,
				BusinessID: "AG-1",
				Keywords:   []string{},
				SeoFailedInfoWorkFlow: &seofailedworkflowinfo.SEOFailedWorkflowInfo{
					BusinessID:       "AG-1",
					WorkflowId:       "SERP_AG1",
					WorkflowType:     "LocalSEOWorkflow",
					StartDate:        "2023-06-01",
					Keywords:         []string{},
					StatusCode:       "",
					RetryCount:       0,
					IsRetryableError: false,
					IssueResolved:    false,
				},
			},
			listingProfile: &listingprofilemodel.ListingProfile{
				BusinessID:  "AG-1",
				CompanyName: "Name1",
				Address:     "Address1",
				City:        "City1",
				State:       "State1",
				Website:     "Website1",
				Country:     "Country1",
				WorkNumber:  []string{"**************"},
				Location:    &vstorepb.GeoPoint{Latitude: 0, Longitude: 0},
			},
			seoSettings: &seosettingsmodel.SEOSettings{
				BusinessID:        "AG-1",
				LocalSearchRadius: 1.25,
			},
			country: &nap_v1.Country{
				Id:   "Country1",
				Name: "Country1",
			},
			state: &nap_v1.ListStatesResponse{
				States: []*nap_v1.State{
					{
						Id:   "State1",
						Name: "State1",
					},
				},
			},
			activations: &accounts_v2.GetMultiActivationStatusesResponse{
				ActivationStatuses: []*accounts_v2.GetMultiActivationStatusesResponse_AppsAndAddonsActivationStatuses{
					{
						BusinessId: "AG-M8GZ83PL",
						ProductId:  constants.ListingBuilderMarketplaceAppID,
						Status:     accounts_v2.ActivationStatus_ACTIVATION_STATUS_ACTIVATED,
						Count:      1,
						EditionId:  constants.ListingBuilderDemoPaidEditionID,
					},
				},
			},
			expectedResponse:    nil,
			expectedWorkflowErr: errors.New("activity error (type: buildRequests, scheduledEventID: 0, startedEventID: 0, identity: ): listing profile must have geo location set (type: failed_precondition, retryable: true)"),
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			ctx := context.Background()
			ctrl := gomock.NewController(t)
			mockLpService := listingprofileservice.NewMockInterface(ctrl)
			mockSeoFailedWorkflowInfoService := seofailedworkflowinfoservice.NewMockService(ctrl)
			// Mock out expected listing profiles
			mockLpService.EXPECT().Get(gomock.Any(), c.listingProfile.BusinessID, false).Return(c.listingProfile, nil).AnyTimes()

			mockSEODataService := seodataservice.NewMockService(ctrl)
			mockSEODataService.EXPECT().QueryDateRange(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
			mockSEODataService.EXPECT().Upsert(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

			mockDataForSEO := dataforseo.NewMockSERPClient(ctrl)

			mockSEOSettingsService := seosettingsservice.NewMockService(ctrl)

			mockAccounts := mocks.NewMockAccountsServiceInterface(ctrl)
			mockAccounts.EXPECT().GetMultiActivationStatuses(gomock.Any(), gomock.Any()).Return(c.activations, nil).AnyTimes()

			// Mock out expected seo settings
			mockSEOSettingsService.EXPECT().Get(gomock.Any(), c.seoSettings.BusinessID).Return(&seosettingsmodel.SEOSettings{
				BusinessID:        c.seoSettings.BusinessID,
				LocalSearchRadius: c.seoSettings.LocalSearchRadius,
			}, nil).AnyTimes()

			// For every task we post to DataForSEO, mock out the initial response with the Task IDs
			// Also mock out the individual responses for when each task have its results fetched later
			for _, post := range c.dataForSEOPosts {
				resp := &dataforseo.GoogleMapsSERPResponse{Tasks: createMockTaskResponseForReq(post.Req)}
				mockDataForSEO.EXPECT().GoogleMapsSERPTaskPost(gomock.Any(), c.workflowParams.Date, c.workflowParams.IgnoreDataLakeResults, post.Req, gomock.Any()).Return(resp, nil).Times(1)

				for _, get := range createMockTaskGetResponseForReq(post.Req, c.taskItems) {
					mockDataForSEO.EXPECT().GoogleMapsSERPTaskGet(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), get.Tasks[0].ID, c.workflowParams.IgnoreDataLakeResults).Return(get, nil).Times(1)
				}
			}

			// For every task we post to DataForSEO, mock out the initial response with the Task IDs
			// Also mock out the individual responses for when each task have its results fetched later
			for _, post := range c.dataForSEOOrganicPosts {
				resp := &dataforseo.GoogleOrganicSERPResponse{Tasks: createMockOrganicTaskResponseForReq(post.Req)}
				mockDataForSEO.EXPECT().GoogleOrganicSERPTaskPost(gomock.Any(), c.workflowParams.Date, c.workflowParams.IgnoreDataLakeResults, post.Req, gomock.Any()).Return(resp, nil).Times(1)

				for _, get := range createMockOrganicTaskGetResponseForReq(post.Req, c.organicTaskItems) {
					mockDataForSEO.EXPECT().GoogleOrganicSERPTaskGet(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), get.Tasks[0].ID, c.workflowParams.IgnoreDataLakeResults).Return(get, nil).Times(1)
				}
			}

			vmockhelper.MockCallsAndPrintExpected(mockDataForSEO, "mockDataForSEO")

			// Mock out expected data service calls to store the data in vstore
			for _, dataCall := range c.expectedLocalSearchDataCalls {
				mockSEODataService.EXPECT().UpsertLocalSEOData(gomock.Any(), dataCall.businessID, dataCall.keyword, dataCall.date, dataCall.entries, gomock.Any(), gomock.Any()).Return(nil).Times(1)
			}

			for _, dataCall := range c.expectedOrganicSearchDataCalls {
				mockSEODataService.EXPECT().UpsertOrganicRank(gomock.Any(), dataCall.businessID, dataCall.keyword, dataCall.date, dataCall.rank).Return(nil)

			}
			mockSeoFailedWorkflowInfoService.EXPECT().UpdateFailedWorkflowinfo(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
			vmockhelper.MockCallsAndPrintExpected(mockSEODataService, "mockSEODataService")

			mockCSGMB := cssdk.NewMockGoogleMyBusinessClientInterface(ctrl)
			mockCSGMB.EXPECT().GetGoogleMyBusinessLocation(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&cssdk.GoogleMyBusinessLocation{}, nil).AnyTimes()

			mockSeoFailedWorkflowInfoService.EXPECT().Upsert(gomock.Any(), gomock.Any()).Return(nil).Times(1)

			testSuite := &testsuite.WorkflowTestSuite{}
			env := testSuite.NewTestWorkflowEnvironment()
			env.RegisterWorkflow(LocalSEOWorkflow)
			env.RegisterActivity(buildRequests)
			env.RegisterActivity(processMapsRequests)
			env.RegisterActivity(processOrganicRequestBatch)
			env.RegisterActivity(sortTasks)
			env.RegisterActivity(storeMapsResults)
			env.RegisterActivity(storeMapsResultsV1) // New version with searchRadius parameter
			env.RegisterActivity(fetchMapsResult)
			env.RegisterActivity(getAndStoreOrganicResult)
			env.RegisterActivity(getConnectedGooglePlaceID)
			env.RegisterActivity(storeFailedWorkflowInfo)
			ctx = NewActivityContext(ctx, nil, mockLpService, nil, mockSEODataService, mockDataForSEO, mockSEOSettingsService, nil, nil, mockAccounts, nil, nil, nil, nil, mockCSGMB, nil, nil, nil, nil, mockSeoFailedWorkflowInfoService, nil, nil)
			env.SetWorkerOptions(worker.Options{
				BackgroundActivityContext: ctx,
				DeadlockDetectionTimeout:  time.Second * 30,
			})
			env.ExecuteWorkflow(LocalSEOWorkflow, c.workflowParams)

			if !env.IsWorkflowCompleted() {
				t.Errorf("Workflow failed to complete. Error (%s)", env.GetWorkflowError())
			}
			var executionError *temporal.WorkflowExecutionError
			erval := env.GetWorkflowError()
			var errmsg string
			v := errors.As(erval, &executionError)
			if v { // Check if it's a WorkflowExecutionError
				errmsg = executionError.Unwrap().Error() // Extract the root cause
			}
			t.Log(errmsg, v, erval)
			assert.Equal(t, c.expectedWorkflowErr.Error(), errmsg)
		})
	}
}

// This takes all the task requests and makes an equivalent task response for our mock response
func createMockTaskResponseForReq(reqs []*dataforseo.GoogleMapsSERPTaskPostRequest) []*dataforseo.GoogleMapsTask {
	var out []*dataforseo.GoogleMapsTask
	for i, req := range reqs {
		out = append(out, &dataforseo.GoogleMapsTask{
			Data: &dataforseo.Data{
				Tag: dataforseo.MakeTag(req.BusinessID, req.Keyword, req.Vicinity),
			},
			ID: fmt.Sprintf("%d", i),
		})
	}
	return out
}

// This takes all the task requests and makes an equivalent task response for our mock response
func createMockOrganicTaskResponseForReq(reqs []*dataforseo.GoogleOrganicSERPTaskPostRequest) []*dataforseo.GoogleOrganicTask {
	var out []*dataforseo.GoogleOrganicTask
	for i, req := range reqs {
		out = append(out, &dataforseo.GoogleOrganicTask{
			Data: &dataforseo.Data{
				Tag: dataforseo.MakeTag(req.BusinessID, req.Keyword, req.Vicinity),
			},
			ID: fmt.Sprintf("%d", i),
		})
	}
	return out
}

// This takes all of our task requests and makes an equivalent GET response containing items for each task
func createMockTaskGetResponseForReq(reqs []*dataforseo.GoogleMapsSERPTaskPostRequest, items []*dataforseo.GoogleMapsItem) []*dataforseo.GoogleMapsSERPResponse {
	var out []*dataforseo.GoogleMapsSERPResponse
	for i, req := range reqs {
		resp := &dataforseo.GoogleMapsSERPResponse{
			Tasks: []*dataforseo.GoogleMapsTask{
				{
					ID:            fmt.Sprintf("%d", i),
					StatusCode:    20100,
					StatusMessage: "",
					Data: &dataforseo.Data{
						Tag: dataforseo.MakeTag(req.BusinessID, req.Keyword, req.Vicinity),
					},
					Result: []*dataforseo.GoogleMapsResult{
						{
							Items: items,
						},
					},
				},
			},
		}
		out = append(out, resp)
	}
	return out
}

// This takes all of our task requests and makes an equivalent GET response containing items for each task
func createMockOrganicTaskGetResponseForReq(reqs []*dataforseo.GoogleOrganicSERPTaskPostRequest, items []*dataforseo.GoogleOrganicItem) []*dataforseo.GoogleOrganicSERPResponse {
	var out []*dataforseo.GoogleOrganicSERPResponse
	for i, req := range reqs {
		resp := &dataforseo.GoogleOrganicSERPResponse{
			Tasks: []*dataforseo.GoogleOrganicTask{
				{
					ID:            fmt.Sprintf("%d", i),
					StatusCode:    20100,
					StatusMessage: "",
					Data: &dataforseo.Data{
						Tag: dataforseo.MakeTag(req.BusinessID, req.Keyword, req.Vicinity),
					},
					Result: []*dataforseo.GoogleOrganicResult{
						{
							Items: items,
						},
					},
				},
			},
		}
		out = append(out, resp)
	}
	return out
}

func Test_StoreMapsResults_Activities(t *testing.T) {
	type testCase struct {
		name           string
		activityName   string
		expectV0Called bool
		expectV1Called bool
		searchRadius   float64
	}

	cases := []testCase{
		{
			name:           "storeMapsResults should call UpsertLocalSEOData without searchRadius",
			activityName:   "storeMapsResults",
			expectV0Called: true,
			expectV1Called: false,
			searchRadius:   0, // Not used in V0
		},
		{
			name:           "storeMapsResultsV1 should call UpsertLocalSEOData with searchRadius",
			activityName:   "storeMapsResultsV1",
			expectV0Called: false,
			expectV1Called: true,
			searchRadius:   15.5,
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			ctx := context.Background()
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			// Create mocks
			mockSeoDataService := seodataservice.NewMockService(ctrl)

			// Set up expectations based on which version should be called
			if c.expectV0Called {
				mockSeoDataService.EXPECT().
					UpsertLocalSEOData(gomock.Any(), "AG-1", "test", "2023-06-01", gomock.Any(), "http://test.com").
					Return(nil).
					Times(1)
			}

			if c.expectV1Called {
				mockSeoDataService.EXPECT().
					UpsertLocalSEOData(gomock.Any(), "AG-1", "test", "2023-06-01", gomock.Any(), "http://test.com", gomock.Any()).
					Return(nil).
					Times(1)
			}

			// Create activity context with mocked service using the proper NewActivityContext function
			ctx = NewActivityContext(ctx, nil, nil, nil, mockSeoDataService, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil)

			// Test data
			params := &SERPWorkflowParams{
				BusinessID: "AG-1",
				Date:       "2023-06-01",
			}
			entries := []*seodata.LocalSearchData{
				{
					Keyword:  "test",
					Vicinity: "VICINITY_C3",
					Results: []*seodata.LocalSearchResult{
						{BusinessName: "Test", Rank: 1, IsMainBusiness: true},
					},
				},
			}

			storeMapsParams := &StoreMapsParams{
				Keyword:      "test",
				Entries:      entries,
				WorkflowURL:  "http://test.com",
				SearchRadius: c.searchRadius,
				SERPParams:   params,
			}

			// Execute the appropriate activity
			var err error
			if c.activityName == "storeMapsResults" {
				// Use original signature for V0
				err = storeMapsResults(ctx, params, "test", entries, "http://test.com")
			} else {
				// Use new struct-based signature for V1
				err = storeMapsResultsV1(ctx, storeMapsParams)
			}

			assert.NoError(t, err)
		})
	}
}
