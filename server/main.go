package main

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/vendasta/listing-products/internal/seo/aioauditresult"
	aioauditresultrepository "github.com/vendasta/listing-products/internal/seo/aioauditresult/repository"
	aioauditresultservice "github.com/vendasta/listing-products/internal/seo/aioauditresult/service"
	"github.com/vendasta/listing-products/internal/seo/workflow/seofailedworkflowinfo"

	"github.com/vendasta/IAM/sdks/go/iaminterceptor"

	"github.com/vendasta/listing-products/internal/syncdata/workflow"

	group "github.com/vendasta/group/sdks/go/v2"
	listingproductsresources "github.com/vendasta/iam-resources/applications/listing-products"
	listingscore "github.com/vendasta/listing-score/sdks/go/v1"
	listingsyndication "github.com/vendasta/listing-syndication/sdks/go"
	marketplaceapps "github.com/vendasta/marketplace-apps/sdks/go/v2"
	platformintegrations "github.com/vendasta/platform-integrations/sdks/go"
	snapshot "github.com/vendasta/snapshot/sdks/go"
	vanalytics "github.com/vendasta/vanalytics/sdks/go"

	"github.com/vendasta/listing-products/internal/google/googleupdatedworkflow"
	"github.com/vendasta/listing-products/internal/syncdata"
	"github.com/vendasta/listing-products/internal/syndication/gmb/datamodels/googleupdatedlocation"
	googleupdatedlocationrepository "github.com/vendasta/listing-products/internal/syndication/gmb/datamodels/googleupdatedlocation/repository"
	googleupdatedlocationservice "github.com/vendasta/listing-products/internal/syndication/gmb/datamodels/googleupdatedlocation/service"

	"github.com/vendasta/listing-products/internal/businessprofilefieldstatus"
	businessprofilefieldstatusrepository "github.com/vendasta/listing-products/internal/businessprofilefieldstatus/repository"
	businessprofilefieldstatusservice "github.com/vendasta/listing-products/internal/businessprofilefieldstatus/service"
	"github.com/vendasta/listing-products/internal/seo/keywordinfo"
	keywordinforepository "github.com/vendasta/listing-products/internal/seo/keywordinfo/repository"
	keywordinfoservice "github.com/vendasta/listing-products/internal/seo/keywordinfo/service"
	"github.com/vendasta/listing-products/internal/seo/seogooglekeywordinfo"
	seogooglekeywordinforepository "github.com/vendasta/listing-products/internal/seo/seogooglekeywordinfo/repository"
	seogooglekeywordinfoservice "github.com/vendasta/listing-products/internal/seo/seogooglekeywordinfo/service"
	seofailedworkflowinforepository "github.com/vendasta/listing-products/internal/seo/workflow/seofailedworkflowinfo/repository"
	seofailedworkflowinfoservice "github.com/vendasta/listing-products/internal/seo/workflow/seofailedworkflowinfo/service"
	"github.com/vendasta/listing-products/internal/suggestions"
	suggestionsrepository "github.com/vendasta/listing-products/internal/suggestions/repository"
	suggestionsservice "github.com/vendasta/listing-products/internal/suggestions/service"

	"github.com/vendasta/listing-products/internal/businessdatascore"
	"github.com/vendasta/listing-products/internal/cslistingsubmit"
	"github.com/vendasta/listing-products/internal/dataforseocategories"
	dataforseocategoriesrepository "github.com/vendasta/listing-products/internal/dataforseocategories/repository"
	dataforseocategoriesservice "github.com/vendasta/listing-products/internal/dataforseocategories/service"
	listingprofileevents "github.com/vendasta/listing-products/internal/listingprofile/events"
	uberallhttpclient "github.com/vendasta/listing-products/internal/syndication/uberall/httpclient"

	dataforseotask "github.com/vendasta/listing-products/internal/dataforseo/task"
	dataforseotaskrepository "github.com/vendasta/listing-products/internal/dataforseo/task/repository"
	dataforseotaskservice "github.com/vendasta/listing-products/internal/dataforseo/task/service"
	"github.com/vendasta/listing-products/internal/seosuggestedkeywords"
	seosuggestedkeywordsrepository "github.com/vendasta/listing-products/internal/seosuggestedkeywords/repository"
	seosuggestedkeywordsservice "github.com/vendasta/listing-products/internal/seosuggestedkeywords/service"

	smartyvalidationrecord "github.com/vendasta/listing-products/internal/smartystreets/validationrecords"

	"github.com/vendasta/listing-products/internal/accountswrapper"
	"github.com/vendasta/listing-products/internal/partnersettings"

	"cloud.google.com/go/datastore"
	secretmanager "cloud.google.com/go/secretmanager/apiv1"

	bingplacesmodel "github.com/vendasta/listing-products/internal/bingplaces/model"

	partnersettingsrepository "github.com/vendasta/listing-products/internal/partnersettings/repository"
	partnersettingsservice "github.com/vendasta/listing-products/internal/partnersettings/service"

	address "github.com/vendasta/address/sdks/go"
	"github.com/vendasta/gosdks/openai"
	vendastatemporal "github.com/vendasta/gosdks/temporal"

	applebusinessconnect "github.com/vendasta/listing-products/internal/apple/businessconnect"
	abchttpclient "github.com/vendasta/listing-products/internal/apple/businessconnect/httpsender"
	binginsightsService "github.com/vendasta/listing-products/internal/binginsights"
	bingclicksourceinsightModel "github.com/vendasta/listing-products/internal/binginsights/bingclicksourceinsights"
	bingclicksourceinsightRepository "github.com/vendasta/listing-products/internal/binginsights/bingclicksourceinsights/repository"
	bingclicksourceinsightService "github.com/vendasta/listing-products/internal/binginsights/bingclicksourceinsights/service"
	bingdeviceplatforminsightModel "github.com/vendasta/listing-products/internal/binginsights/bingdeviceplatforminsights"
	bingdeviceplatforminsightRepository "github.com/vendasta/listing-products/internal/binginsights/bingdeviceplatforminsights/repository"
	bingdeviceplatforminsightService "github.com/vendasta/listing-products/internal/binginsights/bingdeviceplatforminsights/service"
	binginsightsMetricsService "github.com/vendasta/listing-products/internal/binginsights/binginsightsworkflow/service"
	binginsightsworkflow "github.com/vendasta/listing-products/internal/binginsights/binginsightsworkflow/workflow"
	bingsimilarbusinessinsightModel "github.com/vendasta/listing-products/internal/binginsights/bingsimilarbusinessinsights"
	bingsimilarbusinessinsightRepository "github.com/vendasta/listing-products/internal/binginsights/bingsimilarbusinessinsights/repository"
	bingsimilarbusinessinsightService "github.com/vendasta/listing-products/internal/binginsights/bingsimilarbusinessinsights/service"

	bingplaceshttpclient "github.com/vendasta/listing-products/internal/bingplaces/httpclient"

	"github.com/vendasta/listing-products/internal/citations/citationdata"
	citationdatarepository "github.com/vendasta/listing-products/internal/citations/citationdata/repository"
	citationdataservice "github.com/vendasta/listing-products/internal/citations/citationdata/service"
	"github.com/vendasta/listing-products/internal/citations/rejectedcitations"
	rejectedcitationsrepository "github.com/vendasta/listing-products/internal/citations/rejectedcitations/repository"
	rejectedcitationsservice "github.com/vendasta/listing-products/internal/citations/rejectedcitations/service"
	citationworkflow "github.com/vendasta/listing-products/internal/citations/workflow"
	dataforseo "github.com/vendasta/listing-products/internal/dataforseo"
	"github.com/vendasta/listing-products/internal/directsyncsources"
	snapshotevents "github.com/vendasta/listing-products/internal/events/snapshot"
	"github.com/vendasta/listing-products/internal/gcs"
	"github.com/vendasta/listing-products/internal/msclient"
	seodata "github.com/vendasta/listing-products/internal/seo/model"
	seodatarepository "github.com/vendasta/listing-products/internal/seo/repository"
	seodataservice "github.com/vendasta/listing-products/internal/seo/service"
	seoworkflow "github.com/vendasta/listing-products/internal/seo/workflow"
	seosettings "github.com/vendasta/listing-products/internal/seosettings/model"
	seosettingsrepository "github.com/vendasta/listing-products/internal/seosettings/repository"
	seosettingsservice "github.com/vendasta/listing-products/internal/seosettings/service"
	aisuggestionservice "github.com/vendasta/listing-products/internal/suggestionservice"
	applebusinessconnect_syndication "github.com/vendasta/listing-products/internal/syndication/applebusinessconnect"
	"github.com/vendasta/listing-products/internal/syndication/applebusinessconnect/applebusinessconnectlocation"
	foursquaresftp "github.com/vendasta/listing-products/internal/syndication/foursquare"
	"github.com/vendasta/listing-products/internal/syndication/uberall/migrations"

	"github.com/vendasta/gosdks/taskqueue"
	local_seo_data_updated "github.com/vendasta/vendastaevents/snapshot/go/local-seo-data-updated"

	"github.com/vendasta/gosdks/cache"

	"github.com/vendasta/listing-products/internal/coreserviceswrapper/gmbclientwrapper"

	gmbHTTPClient "github.com/vendasta/listing-products/internal/google/businessprofile/httpclient"

	"github.com/vendasta/listing-products/internal/authservice"
	"github.com/vendasta/listing-products/internal/listingprofile"
	listingprofilemodel "github.com/vendasta/listing-products/internal/listingprofile/model"
	listingprofilepublish "github.com/vendasta/listing-products/internal/listingprofile/publish"
	listingprofilerepository "github.com/vendasta/listing-products/internal/listingprofile/repository"
	listingprofileservice "github.com/vendasta/listing-products/internal/listingprofile/service"
	"github.com/vendasta/listing-products/internal/syndication/gmb/bulkconnect"

	"github.com/vendasta/listing-products/internal/syndication/neustar/claimworkflow"

	advertising "github.com/vendasta/advertising/sdks/go/v2"
	"github.com/vendasta/gosdks/bifrost"

	"github.com/vendasta/listing-products/internal/activation/ldactivations"

	partner_v1 "github.com/vendasta/partner/sdks/go/v1"

	gbp "github.com/vendasta/listing-products/internal/google/businessprofile"
	"github.com/vendasta/listing-products/internal/pubsubhandlers"
	"github.com/vendasta/listing-products/internal/syndication/dataaxle"
	dataaxleverification "github.com/vendasta/listing-products/internal/syndication/dataaxle/verificationworkflow"
	neustarverification "github.com/vendasta/listing-products/internal/syndication/neustar/verificationworkflow"

	dataaxlelocationrepository "github.com/vendasta/listing-products/internal/syndication/dataaxle/dataaxlelocation/repository"
	dataaxlelocationservice "github.com/vendasta/listing-products/internal/syndication/dataaxle/dataaxlelocation/service"

	"github.com/vendasta/listing-products/internal/addonattributes"
	addonattributesrepository "github.com/vendasta/listing-products/internal/addonattributes/repository"
	addonattributesservice "github.com/vendasta/listing-products/internal/addonattributes/service"
	"github.com/vendasta/listing-products/internal/syndication/yext"

	"cloud.google.com/go/bigquery"
	cssdk "github.com/vendasta/CS/sdks/go/v1"
	iam "github.com/vendasta/IAM/sdks/go/v1"
	iam2 "github.com/vendasta/IAM/sdks/go/v2"
	accountgroupmedia "github.com/vendasta/account-group-media/sdks/go/v1"
	accountgroupsdk "github.com/vendasta/account-group/sdks/go/v1"
	accounts "github.com/vendasta/accounts/sdks/go/v1"
	accountsv2 "github.com/vendasta/accounts/sdks/go/v2"
	eventbroker "github.com/vendasta/event-broker/sdks/go"
	listing_products_v1 "github.com/vendasta/generated-protos-go/listing_products/v1"
	"github.com/vendasta/gosdks/catalogue"
	"github.com/vendasta/gosdks/config"
	"github.com/vendasta/gosdks/logging"
	vpubsub "github.com/vendasta/gosdks/pubsub"
	"github.com/vendasta/gosdks/statsd"
	"github.com/vendasta/gosdks/vax"
	"github.com/vendasta/gosdks/verrors"
	marketplaceWebhooks "github.com/vendasta/marketplace/sdks/go/v1"
	media "github.com/vendasta/media/sdks/go"
	multilocationanalytics "github.com/vendasta/multi-location-analytics/sdks/go/v1"
	orderfulfillment "github.com/vendasta/order-fulfillment/sdks/go"
	vstore "github.com/vendasta/vstore/vstore/sdks/go/v1"
	"google.golang.org/grpc"

	vbcsdk "github.com/vendasta/VBC/sdks/go/v1"
	"github.com/vendasta/gosdks/serverconfig/v2"

	categorysdk "github.com/vendasta/category/sdks/go/v1"
	lspSDK "github.com/vendasta/listing-sync-pro/sdks/go"

	yextSDK "github.com/vendasta/yext/sdks/go"

	vetl "github.com/vendasta/vetl/sdks/go/v1"

	temporalbigquery "github.com/vendasta/gosdks/temporal/shared/bigquery"

	"github.com/vendasta/listing-products/internal/api"
	"github.com/vendasta/listing-products/internal/bingplaces"
	"github.com/vendasta/listing-products/internal/common/accountgroupwrapper"
	lpbq "github.com/vendasta/listing-products/internal/common/bigquery"
	"github.com/vendasta/listing-products/internal/common/environment"
	lpslack "github.com/vendasta/listing-products/internal/common/gchat"
	"github.com/vendasta/listing-products/internal/common/libpostal"
	lpVstore "github.com/vendasta/listing-products/internal/common/vstore"
	"github.com/vendasta/listing-products/internal/constants"
	"github.com/vendasta/listing-products/internal/datahealth"
	"github.com/vendasta/listing-products/internal/events"
	"github.com/vendasta/listing-products/internal/insights"
	"github.com/vendasta/listing-products/internal/listingsource"
	listingsourceservice "github.com/vendasta/listing-products/internal/listingsource/service"

	"github.com/vendasta/listing-products/internal/syndication/dataaxle/dataaxlelocation"
	"github.com/vendasta/listing-products/internal/syndication/facebook"
	fblocation "github.com/vendasta/listing-products/internal/syndication/facebook/facebooklocation"
	fblocationrepo "github.com/vendasta/listing-products/internal/syndication/facebook/facebooklocation/repository"
	fblocationservice "github.com/vendasta/listing-products/internal/syndication/facebook/facebooklocation/service"
	gmblocation "github.com/vendasta/listing-products/internal/syndication/gmb/datamodels/location"
	"github.com/vendasta/listing-products/internal/syndication/neustar"
	neustarlocation "github.com/vendasta/listing-products/internal/syndication/neustar/datamodels/location"
	neustarlocationrepository "github.com/vendasta/listing-products/internal/syndication/neustar/datamodels/location/repository"
	neustarlocationservice "github.com/vendasta/listing-products/internal/syndication/neustar/datamodels/location/service"
	syndicationSubmissionAPIWorkflow "github.com/vendasta/listing-products/internal/syndication/submissions/apiworkflow"
	"github.com/vendasta/listing-products/internal/syndication/submissions/sftpworkflow"
	"github.com/vendasta/listing-products/internal/syndication/uberall"
	uberalllocation "github.com/vendasta/listing-products/internal/syndication/uberall/datamodels/location"
	uberalllocationrepository "github.com/vendasta/listing-products/internal/syndication/uberall/datamodels/location/repository"
	uberalllocationservice "github.com/vendasta/listing-products/internal/syndication/uberall/datamodels/location/service"
)

const (
	httpPort = 11001
	grpcPort = 11000
	msHost   = "https://microsite-%s.appspot.com"
)

var (
	// AuthorizedServiceAccounts are bingplacesservice accounts with access to this microservice.
	AuthorizedServiceAccounts = map[config.Env]iam.ServiceAccountToScopes{
		config.Prod: {
			// Fill these out if you need to authorize specific Google bingplacesservice accounts.
			"<EMAIL>": {iam.READ, iam.WRITE, iam.DELETE},
			// "<EMAIL>": {iam.READ},
		},
		config.Demo: {
			"<EMAIL>": {iam.READ, iam.WRITE, iam.DELETE},
			"<EMAIL>":            {iam.READ, iam.WRITE, iam.DELETE},
			// "<EMAIL>": {iam.READ},
		},
		config.Local: {
			"<EMAIL>": {iam.READ, iam.WRITE, iam.DELETE},
			// "<EMAIL>": {iam.READ},
		},
	}
)

// Setup Application logging and switch the logger
func setupLogger(ctx context.Context) error {
	var nameSpace, podName string
	var err error

	if !config.IsLocal() {
		nameSpace = config.GetGkeNamespace()
		podName = config.GetGkePodName()
	} else {
		nameSpace = "LOCAL_NAMESPACE"
		podName = "LOCAL_POD"
	}

	if err = logging.Initialize(nameSpace, podName, constants.AppID); err != nil {
		logging.Criticalf(ctx, "Error initializing logger: %s", err.Error())
	}
	return err
}

func setupStatsDClient(ctx context.Context) error {
	var err error

	if err = statsd.Initialize(constants.AppID, nil); err != nil {
		logging.Criticalf(ctx, "Error initializing statsd client: %s", err.Error())
	}

	return err
}

func createAndRegisterGrpcServer(ctx context.Context, iamInterceptors *iam.IAMInterceptor) *grpc.Server {
	authInterceptor := iamInterceptors.ServerInterceptor()
	loggingInterceptor := logging.Interceptor()
	timeoutInterceptor := vax.TimeoutInterceptor(20 * time.Second)
	errorsInterceptor := verrors.ErrorConverterServerInterceptor(verrors.DefaultErrorMask)

	//Create a GRPC Server
	logging.Infof(ctx, "Creating GRPC server...")
	grpcServer := serverconfig.CreateGrpcServer(constants.AppID, serverconfig.WithUnaryInterceptors(errorsInterceptor, loggingInterceptor, authInterceptor, timeoutInterceptor))

	//REGISTER_GRPC_SERVERS_HERE
	catalogue.RegisterServer(grpcServer)

	return grpcServer
}

func setupAccountsClient(
	ctx context.Context,
	env config.Env,
) (accounts.Interface, error) {
	accountsClient, err := accounts.NewClient(ctx, env, grpc.WithUnaryInterceptor(logging.ClientInterceptor()))
	if err != nil {
		logging.Criticalf(ctx, "Error creating accounts client: %s", err.Error())
		return nil, err
	}

	return accountsClient, nil
}

func createBigQueryClient(ctx context.Context) (*bigquery.Client, *lpbq.BQWrapper, error) {
	bigQueryClient, err := bigquery.NewClient(ctx, "repcore-prod")
	if err != nil {
		logging.Criticalf(ctx, "Error creating BQ client: %s", err.Error())
		return nil, nil, err
	}

	lpBqClient := lpbq.NewVClient(bigQueryClient)

	return bigQueryClient, lpBqClient, nil
}

func setupBingService(
	ctx context.Context,
	envConfig environment.EnvironmentConfig,
) (*bingplaces.Service, error) {
	client, err := bingplaceshttpclient.NewHttpClientWrapper(ctx, envConfig, nil)
	if err != nil {
		return nil, err
	}

	return bingplaces.NewService(client), nil
}

func createCSgmbClient(
	env config.Env,
	envConfig environment.EnvironmentConfig,
) *gmbclientwrapper.Service {
	client := cssdk.BuildGoogleMyBusinessClient(envConfig.CSapiUser, envConfig.CSapiKey, env)
	return gmbclientwrapper.NewService(client)
}

func setupAccountGroupService(
	ctx context.Context,
	env config.Env,
) accountgroupsdk.Interface {
	agEnv := env
	if env == config.Local {
		agEnv = config.Demo
	}

	accountGroupClient, err := accountgroupsdk.NewClient(ctx, agEnv)
	if err != nil {
		logging.Errorf(ctx, "Error initializing AccountGroup client: %s", err.Error())
		os.Exit(newAccountGroupClientErr)
	}

	return accountGroupClient
}

func setupAccountGroupMediaService(
	ctx context.Context,
	env config.Env,
) *accountgroupmedia.MediaServiceClient {
	agEnv := env
	if env == config.Local {
		agEnv = config.Demo
	}

	agMediaClient, err := accountgroupmedia.NewMediaServiceClient(ctx, agEnv, true)
	if err != nil {
		logging.Errorf(ctx, "Error initializing AccountGroup Media client: %s", err.Error())
		os.Exit(newAccountGroupMediaClientErr)
	}

	return agMediaClient
}

func setupMediaService(
	ctx context.Context,
	env config.Env,
) *media.MediaServiceClient {
	mediaService, err := media.NewMediaServiceClientWithOptions(ctx, env, true)
	if err != nil {
		logging.Errorf(ctx, "Error initializing Media client: %s", err.Error())
		os.Exit(newMediaServiceClientErr)
	}

	return mediaService
}

func intializeTesseractSyncs(ctx context.Context, vetlClient vetl.Interface) error {
	return seosettings.InitializeTessaractSync(ctx, vetlClient)
}

func initializeBingInsightTessaractSync(ctx context.Context, vetlClient vetl.Interface) error {
	return bingdeviceplatforminsightModel.InitializeBingInsightTessaractSync(ctx, vetlClient)
}

func initializeBingClickSourceInsightTessaractSync(ctx context.Context, vetlClient vetl.Interface) error {
	return bingclicksourceinsightModel.InitializeBingClickSourceInsightTessaractSync(ctx, vetlClient)
}

func initializeBingSimilarBusinessInsightTessaractSync(ctx context.Context, vetlClient vetl.Interface) error {
	return bingsimilarbusinessinsightModel.InitializeBingSimilarBusinessInsightTessaractSync(ctx, vetlClient)
}

func initializeSeoDataTessaractSync(ctx context.Context, vetlClient vetl.Interface) error {
	return seodata.InitializeTessaractSync(ctx, vetlClient)
}

// TODO: we need to fix our deferred function handling
func main() {
	var err error
	ctx := context.Background()
	env := config.CurEnv()

	envConfig, err := environment.GetEnvironmentConfig()
	if err != nil {
		logging.Criticalf(ctx, "failed to get env config: %s", err)
		os.Exit(getEnvironmentConfigErr)
	}

	err = setupLogger(ctx)
	if err != nil {
		logging.Criticalf(ctx, "failed to setup logger: %s", err)
		os.Exit(loggerInitErr)
	}

	err = setupStatsDClient(ctx)
	if err != nil {
		logging.Criticalf(ctx, "failed to setup statsd client: %s", err)
		os.Exit(statsDInitErr)
	}

	// setup iam clients and initialize resources
	iamClient, err := iam.NewClient(ctx, env)
	if err != nil {
		logging.Criticalf(ctx, "Error initializing iam client %s", err.Error())
		os.Exit(newIAMClientErr)
	}

	listingProductsResourceOwner := listingproductsresources.ListingProducts{}
	iamV2Client, closers, err := iam2.NewClientV2(ctx, config.CurEnv(), listingProductsResourceOwner.AppID(), listingProductsResourceOwner.AppName())
	if err != nil {
		logging.Criticalf(ctx, "Error initializing iam v2 client: %s", err.Error())
		os.Exit(newIAMClientErr)
	}

	defer func() {
		for _, closer := range closers {
			cErr := closer()
			if cErr != nil {
				logging.Criticalf(ctx, "Error closing IAM client: %s", cErr.Error())
			}
		}
	}()

	//Create IAM Auth
	iamAuthService := iam.NewAuthService(iamClient, AuthorizedServiceAccounts[env])

	// PUBLIC_ROUTES env var comes from publicRoutes in microservice.yaml
	iamInterceptors := iam.NewIAMInterceptor(env, strings.Split(os.Getenv("PUBLIC_ROUTES"), ",")...)

	iamInterceptorv2, err := iaminterceptor.NewIAMInterceptor(env)
	if err != nil {
		logging.Criticalf(ctx, "Error initializing IAM interceptor: %s", err.Error())
		os.Exit(newIAMClientErr)
	}

	grpcServer := createAndRegisterGrpcServer(ctx, iamInterceptors)

	vstoreClient, vstoreCloser, err := lpVstore.CreateVStoreClient(ctx)
	if err != nil {
		logging.Criticalf(ctx, "failed to create vstore client: %s", err)
		os.Exit(newVStoreClientErr)
	}
	defer vstoreCloser()

	vetlClient, err := vetl.NewClient(ctx, env)
	if err != nil {
		logging.Criticalf(ctx, "failed to create vstore client: %s", err)
		os.Exit(newVStoreClientErr)
	}

	logging.Infof(ctx, "VETL Client Handler Account: %s", vetl.GetServiceAccount(env))

	err = vstoreClient.RegisterNamespace(ctx, []string{config.GetGoogleServiceAccount(), vetl.GetServiceAccount(env)})
	if err != nil {
		logging.Errorf(ctx, "Vstore Namespace registration failed: %s", err.Error())
	}

	accountGroupService := setupAccountGroupService(ctx, env)
	accountGroupWrapperService := accountgroupwrapper.NewService(accountGroupService)
	accountGroupMediaClient := setupAccountGroupMediaService(ctx, env)
	mediaService := setupMediaService(ctx, env)

	mlaService, err := multilocationanalytics.NewAnalyticsServiceClient(ctx, env, true)
	if err != nil {
		logging.Criticalf(ctx, "failed to create multilocation analytics client: %s", err)
		os.Exit(newMultiLocationAnalyticsServiceErr)
	}

	snapshotSEOClient, err := snapshot.NewSeoSectionServiceClient(ctx, env, true)
	if err != nil {
		logging.Criticalf(ctx, "failed to create multilocation analytics client: %s", err)
		os.Exit(snapshotSEOClientError)
	}

	orderFulfillmentClient, err := orderfulfillment.NewOrderFulfillmentServiceClient(ctx, env, true)
	if err != nil {
		logging.Criticalf(ctx, "failed to create order fulfullment bingplacesservice: %s", err)
		os.Exit(newOrderFulfillmentClientErr)
	}
	csGMBClient := createCSgmbClient(env, envConfig)
	micrositeClient := msclient.NewClient(fmt.Sprintf(msHost, env.Name()),
		envConfig.MSapiKey,
		envConfig.MSapiUser)
	socialServicesClient := cssdk.BuildSocialServicesClient(envConfig.CSapiUser, envConfig.CSapiKey, env)
	csListingsClient := cssdk.BuildListingsClient(envConfig.CSapiUser, envConfig.CSapiKey, env)
	csListingSourceClient := cssdk.BuildListingSourcesClient(envConfig.CSapiUser, envConfig.CSapiKey, env)
	//socialServiceWrapperService := socialservicewrapper.NewService(socialServicesClient)
	insightsService := insights.NewService(mlaService, accountGroupService)

	marketplaceWebhooksAccountClient := marketplaceWebhooks.NewAccountClient(
		ctx, envConfig.MarketplaceAppIDs[0], envConfig.MarketplacePrivateKey, env, "")

	accountsV3Client, accountsV2Closer, err := accountsv2.NewAccountsServiceClientV3(ctx, env, true)
	if err != nil {
		logging.Criticalf(ctx, "failed to setup account v2 client: %s", err)
		os.Exit(accountsV2sdkErr)
	}
	defer accountsV2Closer()

	// Make sure you shouldn't be using the webhooks account client instead.
	accountsClient, err := setupAccountsClient(ctx, env)
	if err != nil {
		logging.Criticalf(ctx, "failed to setup account client: %s", err)
		os.Exit(accountsSDKErr)
	}

	_, lpBQClient, err := createBigQueryClient(ctx)
	if err != nil {
		logging.Criticalf(ctx, "failed to create bigquery client: %s", err)
		os.Exit(newBigQueryClientErr)
	}
	pubsubClient, err := vpubsub.NewGooglePubsubClient(ctx)
	if err != nil {
		logging.Criticalf(ctx, "failed to create pubsub client: %s", err)
		os.Exit(newPubSubClientErr)
	}

	featureFlagClient, err := partner_v1.NewClient(ctx, env)
	if err != nil {
		logging.Criticalf(ctx, "failed to create feature flags client", err.Error())
		os.Exit(featureFlagClientErr)
	}

	msDatastoreClient, err := datastore.NewClient(ctx, envConfig.MSDatastoreProjectID)
	fmt.Printf("Connecting to Datastore: %s\n", envConfig.MSDatastoreProjectID)
	if err != nil {
		logging.Criticalf(ctx, "Failed to create client: %v\n", err.Error())
		os.Exit(datastoreClientErr)
	}

	var closer func() error
	categoryClient, closer, err := categorysdk.NewCategoriesClientV3(ctx, env, true)
	if err != nil {
		logging.Criticalf(ctx, "failed to create categories client", err.Error())
		os.Exit(categorySDKClientErr)
	}
	defer closer()

	addonAttributesRepo := addonattributesrepository.New(vstoreClient)
	addonAttributesService := addonattributesservice.New(addonAttributesRepo)
	ldActivations := ldactivations.New(addonAttributesService, accountsClient)

	googleUpdatedLocationService := googleupdatedlocationservice.New(googleupdatedlocationrepository.New(vstoreClient))

	slackAlertService := lpslack.NewGChatService(&http.Client{})

	libpostalClient, err := libpostal.NewClient(ctx, env)
	if err != nil {
		logging.Criticalf(ctx, "failed to create libpostal client: %s", err.Error())
		os.Exit(libpostalClientSetupErr)
	}

	cacheClient, err := cache.NewRedisClient(os.Getenv("REDIS_HOST"), os.Getenv("REDIS_PORT"))
	if err != nil {
		logging.Criticalf(ctx, "Could not register one or more events with Event Broker: %s", err.Error())
		os.Exit(redisCacheErr)
	}

	gmbHTTPClient := gmbHTTPClient.NewHttpClientWrapper(&http.Client{Timeout: time.Second * 30})
	googleBusinessProfileService := gbp.NewService(envConfig.GMBConsumerKey, envConfig.GMBConsumerSecret, gmbHTTPClient, csGMBClient, accountGroupWrapperService, categoryClient, cacheClient)

	foursquareBucketName := fmt.Sprintf("listing_products_foursquare_%s", config.CurEnv().Name())
	gcsService, err := gcs.NewGCSBucketService(foursquareBucketName)
	if err != nil {
		logging.Criticalf(ctx, "Error initializing GCS bucket bingplacesservice", err)
		os.Exit(foursquareBucketErr)
	}

	seoBucketName := fmt.Sprintf("listing_products_seo_%s", config.CurEnv().Name())
	seoGoogleCloudService, err := gcs.NewGCSBucketService(seoBucketName)
	if err != nil {
		logging.Criticalf(ctx, "Error initializing GCS bucket bingplacesservice", err)
		os.Exit(foursquareBucketErr)
	}

	// this must be setup prior to `syndicationSubmissionAPIWorkflow.SetupActivityServices`
	foursquareSFTPService := foursquaresftp.New(lpBQClient, categoryClient, featureFlagClient, accountGroupWrapperService, gcsService)

	csFacebookClient := cssdk.BuildFacebookClient(envConfig.CSapiUser, envConfig.CSapiKey, env)
	uberallLocationRepo := uberalllocationrepository.New(vstoreClient)
	uberallLocationService := uberalllocationservice.New(uberallLocationRepo)

	businessDataFieldStatusRepo := businessprofilefieldstatusrepository.New(vstoreClient)
	businessDataFieldStatusService := businessprofilefieldstatusservice.New(businessDataFieldStatusRepo)

	lspClient, err := lspSDK.NewListingSyncProServiceClient(ctx, env, true)
	if err != nil {
		logging.Criticalf(ctx, "Could init LSP client: %s", err.Error())
		os.Exit(lspClientErr)
	}
	vbcLspClient := vbcsdk.BuildListingClient(envConfig.VBCapiUser, envConfig.VBCapiKey, env)

	yextSDKClient, err := yextSDK.NewYextClient(ctx, env, true)
	if err != nil {
		logging.Criticalf(ctx, "Could init Yext client: %s", err.Error())
		os.Exit(yextClientErr)
	}
	temporalClient, temporalCloser, err := vendastatemporal.NewService(ctx, "listings-products", env) // Extra `s` in the server name, hardcode for now
	if err != nil {
		logging.Criticalf(ctx, "temporal setup failed: %s", err)
		os.Exit(temporalSetupErr)
	}
	defer temporalCloser()
	listingScoreClient, err := listingscore.NewClient(ctx, env)
	if err != nil {
		logging.Criticalf(ctx, "Could init Listing Score client: %s", err.Error())
		os.Exit(listingScoreClientErr)
	}

	// apis for listing profile validations
	napAPI, err := listingprofile.NAPClient(grpc.WithUnaryInterceptor(logging.ClientInterceptor()))
	if err != nil {
		logging.Criticalf(ctx, "Error initializing NAP client %s", err.Error())
		os.Exit(napClientInitializationErr)
	}
	napClient, napCloser, err := address.NewNapDataServiceClientV3(ctx, env, true)
	if err != nil {
		logging.Criticalf(ctx, "Error initializing NAP client %s", err.Error())
		os.Exit(napClientInitializationErr)
	}
	defer napCloser()

	partnerServiceAPI, err := listingprofile.PartnerServiceClient()
	if err != nil {
		logging.Criticalf(ctx, "Error initializing Partner bingplacesservice client %s", err.Error())
		os.Exit(partnerServiceClientInitializationErr)
	}
	aasdkPartnerAPI := listingprofile.PartnerClient()
	taxonomyAPI := listingprofile.TaxonomyClient()
	geoAPI, err := listingprofile.GeoClient(envConfig.GooglePlacesApiKey, cacheClient)
	if err != nil {
		logging.Criticalf(ctx, "Error initializing Geo client %s", err.Error())
		os.Exit(geoClientInitializationError)
	}

	addressClient, addressCloser, err := address.NewNapDataServiceClientV3(ctx, env, true)
	if err != nil {
		logging.Criticalf(ctx, "Error initializing Address client %s", err.Error())
		os.Exit(addressClientInitializationError)
	}

	defer addressCloser()
	advertisingClient, err := advertising.NewAdwordsClient(ctx, env, true)
	if err != nil {
		logging.Criticalf(ctx, "Error initializing Adwords client %s", err.Error())
		os.Exit(advertisingClientError)
	}
	listingSyndicationClient, listingSyndicationCloser, err := listingsyndication.NewSyndicationServiceClientV3(ctx, env, true)
	if err != nil {
		logging.Criticalf(ctx, "Error initializing Listing Syndication client %s", err.Error())
		os.Exit(listingSyndicationClientError)
	}

	defer listingSyndicationCloser()

	// Set up API services
	tlSubmissionService := syndicationSubmissionAPIWorkflow.NewSubmissionWorkFlow(temporalClient)
	profileGroupClient := cssdk.BuildProfileGroupClient(envConfig.CSapiUser, envConfig.CSapiKey, env)
	listingSourceService := listingsourceservice.New(vstoreClient, profileGroupClient, cacheClient)
	addonAttributesRepo = addonattributesrepository.New(vstoreClient)
	addonAttributesService = addonattributesservice.New(addonAttributesRepo)
	// listing profile repository and bingplacesservice
	listingProfileRepo := listingprofilerepository.New(vstoreClient)
	listingProfileService := listingprofileservice.New(listingProfileRepo, googleBusinessProfileService, accountGroupWrapperService,
		tlSubmissionService, lspClient, addonAttributesService, yextSDKClient, listingSyndicationClient, temporalClient, listingSourceService)

	err = listingProfileService.SetupTemporalAndStartWorker(ctx)
	if err != nil {
		logging.Criticalf(ctx, "failed to setup temporal and start worker: %s", err)
		os.Exit(temporalSetupErr)
	}

	taskClient, err := taskqueue.NewClient(ctx, env)
	if err != nil {
		logging.Criticalf(ctx, "Error initializing task queue client: %s", err.Error())
		os.Exit(newTaskQueueClientErr)
	}

	csListingSubmit := cslistingsubmit.NewService(listingSourceService, accountGroupService, csListingsClient, taskClient, envConfig.CSapiUser, envConfig.CSapiKey)
	bingPlacesService, err := setupBingService(ctx, envConfig)
	if err != nil {
		logging.Criticalf(ctx, "failed to create bing bingplacesservice: %s", err)
		os.Exit(bingInitErr)
	}

	vanalyticsClient, err := vanalytics.NewVAnalyticsClient(ctx, env, true)
	if err != nil {
		logging.Criticalf(ctx, "failed to create VAnalyticsClient: %s", err)
		os.Exit(vanalyticsClientErr)
	}

	uberallHttpClient := uberallhttpclient.NewHttpClientWrapper(env, &http.Client{Timeout: time.Second * 30}, envConfig.UberallPrivateKey)
	uberallService := uberall.NewService(accountGroupService, accountGroupMediaClient, mediaService,
		uberallHttpClient, uberallLocationService, lspClient, addonAttributesService, categoryClient,
		temporalClient.Client, featureFlagClient, listingProfileService)

	dataAxleLocationRepo := dataaxlelocationrepository.New(vstoreClient)
	dataAxleLocationService := dataaxlelocationservice.New(dataAxleLocationRepo)
	dataAxleService := dataaxle.New(accountGroupWrapperService, dataAxleLocationService,
		dataaxle.NewHttpClientWrapper(env, &http.Client{Timeout: time.Second * 30}, envConfig.DataAxleKey),
		libpostalClient, categoryClient, accountGroupMediaClient, mediaService, accountsClient, addonAttributesService,
		featureFlagClient, ldActivations, listingProfileService)

	neustarHTTP := neustar.NewHTTPClientWrapper(env, envConfig.NeustarUsername, envConfig.NeustarPassword)
	neustarLocationRepo := neustarlocationrepository.New(vstoreClient)
	neustarLocationService := neustarlocationservice.New(neustarLocationRepo)
	neustarService := neustar.NewService(envConfig.NeustarAccountID, accountGroupService,
		accountGroupMediaClient, mediaService, categoryClient, neustarHTTP,
		neustarLocationService, featureFlagClient, ldActivations, listingProfileService)

	seoRepo := seodatarepository.New(vstoreClient)
	seoService := seodataservice.New(seoRepo, accountsV3Client)

	partnerServiceRepo := partnersettingsrepository.New(vstoreClient, msDatastoreClient)
	partnerSettingsService := partnersettingsservice.New(partnerServiceRepo, accountsClient)

	citationRepo := citationdatarepository.New(vstoreClient)
	citationService := citationdataservice.New(citationRepo, vanalyticsClient)

	rejectedCitationRepo := rejectedcitationsrepository.New(vstoreClient)
	rejectedCitationService := rejectedcitationsservice.New(rejectedCitationRepo)

	dataForSEOTaskRepo := dataforseotaskrepository.New(vstoreClient)
	dataForSEOTaskService := dataforseotaskservice.New(dataForSEOTaskRepo)

	dataForSEOClient := dataforseo.NewClient(envConfig.DataForSEOKey, dataForSEOTaskService)

	dataforseoCategoryRepo := dataforseocategoriesrepository.New(vstoreClient)
	dataforseoCategoryService := dataforseocategoriesservice.New(dataforseoCategoryRepo)

	seoSettingsRepo := seosettingsrepository.New(vstoreClient)
	seoSettingsService := seosettingsservice.New(seoSettingsRepo)

	bingClickSourceInsightsRepo := bingclicksourceinsightRepository.New(vstoreClient)
	bingDevicePlatformInsightsRepo := bingdeviceplatforminsightRepository.New(vstoreClient)
	bingSimilarBusinessInsightRepo := bingsimilarbusinessinsightRepository.New(vstoreClient)
	//As of now we are not using these bingclicksourceinsightService, bingDevicePlatformInsightsService, bingSimilarBusinessInsightService
	bingclicksourceinsightService.New(bingClickSourceInsightsRepo)
	bingdeviceplatforminsightService.New(bingDevicePlatformInsightsRepo)
	bingsimilarbusinessinsightService.New(bingSimilarBusinessInsightRepo)

	seoSuggestedKeywordsRepo := seosuggestedkeywordsrepository.New(vstoreClient)
	seoSuggestedKeywordsService := seosuggestedkeywordsservice.New(seoSuggestedKeywordsRepo)

	keywordInfoRepo := keywordinforepository.New(vstoreClient)
	keywordInfoService := keywordinfoservice.New(keywordInfoRepo)

	seoGoogleKeywordInfoRepo := seogooglekeywordinforepository.New(vstoreClient)
	seogooglekeywordInfoService := seogooglekeywordinfoservice.New(seoGoogleKeywordInfoRepo)

	seoFailedWorkflowInfoRepo := seofailedworkflowinforepository.New(vstoreClient)
	seoFailedWorkflowInfoService := seofailedworkflowinfoservice.New(seoFailedWorkflowInfoRepo)

	aioAuditResultRepo := aioauditresultrepository.New(vstoreClient)
	aioAuditResultService := aioauditresultservice.New(aioAuditResultRepo)

	suggestionsRepo := suggestionsrepository.New(vstoreClient)
	suggestionService := suggestionsservice.New(suggestionsRepo)

	fbLocationRepo := fblocationrepo.New(vstoreClient)
	fbLocationService := fblocationservice.New(fbLocationRepo)
	fbService := facebook.New(
		facebook.NewHttpClientWrapper(&http.Client{Timeout: time.Second * 30}), socialServicesClient, csFacebookClient,
		accountGroupWrapperService, accountGroupMediaClient, mediaService, fbLocationService, listingProfileService, csListingSubmit)

	accountsWrapperService := accountswrapper.NewService(accountsV3Client, addonAttributesService)

	abcHTTPClient := abchttpclient.NewHttpClientWrapper(&http.Client{Timeout: time.Second * 30}, envConfig, cacheClient)
	abcService := applebusinessconnect.NewService(abcHTTPClient, listingProfileService, libpostalClient, categoryClient, accountGroupMediaClient, mediaService)
	abcVendorService := applebusinessconnect_syndication.NewService(abcService, listingSyndicationClient)
	activityServices := syndicationSubmissionAPIWorkflow.NewActivityServices(accountGroupWrapperService, uberallService, dataAxleService, neustarService)
	tlSubmissionService.ActivityServices = activityServices

	ctx = context.WithValue(ctx, syndicationSubmissionAPIWorkflow.ActivityServicesKey{}, activityServices)
	err = temporalClient.NewWorker(ctx,
		constants.TurboListerTaskList,
		vendastatemporal.WithTaskQueueActivitiesPerSecond(1),
		vendastatemporal.WithSessionWorker(),
	)
	if err != nil {
		logging.Criticalf(ctx, "unable to create temporal worker for syndication submission api: %s", err.Error())
	}

	// Create OpenAI client first so it can be used by the SEO server
	secretManagerClient, err := secretmanager.NewClient(ctx)
	if err != nil {
		logging.Criticalf(ctx, "Could not create secretManager client: %s", err.Error())
		os.Exit(-1)
	}
	defer secretManagerClient.Close()

	// create open ai service
	openAiClient, err := openai.NewClient(ctx, secretManagerClient, openai.WithSecretKey(os.Getenv("OPENAI_SECRET_KEY")))
	if err != nil {
		logging.Criticalf(ctx, "Unable to create open ai service %s", err.Error())
		os.Exit(-1)
	}

	temporalObject, err := setupTemporal(ctx, temporalClient, lpBQClient, slackAlertService, neustarService, neustarService, dataAxleService, csGMBClient, foursquareSFTPService, featureFlagClient, uberallService, listingProfileService, seoService, dataForSEOClient, seoGoogleCloudService, citationService, listingSourceService, rejectedCitationService, seoSettingsService, snapshotSEOClient, napClient, accountsV3Client, seoSuggestedKeywordsService, dataforseoCategoryService, keywordInfoService, categoryClient, suggestionService, advertisingClient, seogooglekeywordInfoService, seoFailedWorkflowInfoService, aioAuditResultService, openAiClient)
	if err != nil {
		logging.Criticalf(ctx, "temporal setup failed: %s", err)
		os.Exit(temporalSetupErr)
	}
	err = temporalClient.NewWorker(migrations.NewActivityContext(ctx, lpBQClient, uberallService),
		constants.UberallMigrationTaskList,
		vendastatemporal.WithTaskQueueActivitiesPerSecond(1000),
		vendastatemporal.WithSessionWorker(),
	)
	if err != nil {
		logging.Warningf(ctx, "Unable to create temporal worker for fb migration: %s", err.Error())
	}

	groupClient, err := group.NewClient(ctx, env)
	if err != nil {
		logging.Criticalf(ctx, "failed to create group client: %s", err.Error())
		os.Exit(newGroupClientErr)
	}

	seoWorkflowService := seoworkflow.NewWorkflowService(temporalObject.Client, lpBQClient, accountsV3Client, listingProfileService, dataForSEOClient, addressClient, seoFailedWorkflowInfoService)
	authService := authservice.NewAuthService(iamV2Client, partnerSettingsService, groupClient)
	externalValidatorServices := listingprofile.NewExternalValidatorServices(napAPI, partnerServiceAPI, aasdkPartnerAPI, taxonomyAPI, geoAPI)
	listingSourceAPIServer := api.NewListSourceServer(listingSourceService)
	listingProfileAdapterService := api.NewListingProfileAdapterServer(listingProfileService, googleBusinessProfileService, accountsV3Client, externalValidatorServices)
	listingProfileAPIServer := api.NewListingProfileServer(listingProfileService, authService, listingProfileAdapterService, uberallHttpClient, accountGroupService, businessDataFieldStatusService, accountsClient, seoService, seoSettingsService)

	err = bulkconnect.SetupTemporalAndStartWorker(
		ctx, temporalClient, googleBusinessProfileService, lpBQClient, csGMBClient,
		accountGroupWrapperService, listingProfileService)
	if err != nil {
		logging.Criticalf(ctx, "failed to start google bulk create temporal worker: %s", err)
	}

	_, err = bingplaceshttpclient.NewHttpClientWrapper(ctx, envConfig, cacheClient)
	if err != nil {
		logging.Criticalf(ctx, "unable to create bing insights create temporal worker: %s", err.Error())
		os.Exit(bingInitErr)
	}
	bingClient, ers := bingplaceshttpclient.NewHttpClientWrapper(ctx, envConfig, cacheClient)
	if ers != nil {
		logging.Criticalf(ctx, "Error initializing bingplaces http client: %s", err.Error())
		os.Exit(startPubSubHandlingErr)
	}
	bingInsightsMetricsService := binginsightsMetricsService.NewBingInsightsMetricsService(bingClient, bingClickSourceInsightsRepo, bingDevicePlatformInsightsRepo, bingSimilarBusinessInsightRepo)

	bingInsightsWorkflowService := binginsightsworkflow.New(lpBQClient, temporalClient, bingInsightsMetricsService)
	err = bingInsightsWorkflowService.SetupTemporalAndStartWorker(ctx)
	if err != nil {
		logging.Criticalf(ctx, "unable to register bing insights workflow: %s", err.Error())
		os.Exit(bingInitErr)
	}
	BingInsightsService := binginsightsService.New(bingInsightsWorkflowService)

	yextService := yext.NewService(ctx, yextSDKClient, addonAttributesService)
	NeustarClaimWorkflowService := claimworkflow.NewService(temporalObject, env)
	googleBulkConnectService := bulkconnect.NewService(temporalObject)
	uberallMigrationService := migrations.NewService(temporalObject)

	citationWorkflowService := citationworkflow.NewWorkflowService(temporalObject.Client)

	connectionsClient, connectionsClientCloser, err := platformintegrations.NewConnectionsServiceClientV3(ctx, env, true)
	if err != nil {
		logging.Criticalf(ctx, "failed to create Platform-Integrations Connections Client: %s", err)
		os.Exit(platformIntegrationsConnectionsClientErr)
	}
	defer connectionsClientCloser()
	directSyncSourcesService := directsyncsources.NewService(accountGroupService, socialServicesClient, micrositeClient,
		csGMBClient, csListingsClient, listingScoreClient, listingSyndicationClient, lspClient, csListingSourceClient,
		connectionsClient, featureFlagClient, envConfig)

	marketplaceApps, marketplaceCloser, err := marketplaceapps.NewPartnerClientV3(ctx, env, true)
	if err != nil {
		logging.Criticalf(ctx, "temporal setup failed: %s", err)
		os.Exit(marketplaceAppsClientErr)
	}
	defer marketplaceCloser()

	syncDataService := syncdata.NewService(accountGroupService, socialServicesClient, micrositeClient, csGMBClient,
		csListingsClient, csListingSourceClient, listingScoreClient, listingSyndicationClient,
		lspClient, accountsClient, accountsWrapperService, listingSourceService, marketplaceApps,
		addonAttributesService, connectionsClient, featureFlagClient, envConfig)

	// Create AIOAudit workflow service
	aioauditWorkflowService := seoworkflow.NewAIOAuditWorkflowService(temporalClient.Client)

	// Create SEO server first so it can be passed to the listing products service
	seoServer := api.NewSEOServer(seoService, seoWorkflowService, seoSettingsService, listingProfileService, keywordInfoService, dataforseoCategoryService, aioAuditResultService, aioauditWorkflowService, openAiClient)

	listingProductsAPIServer := api.New(iamAuthService, authService, insightsService, tlSubmissionService, uberallMigrationService, addonAttributesService, yextService, uberallService, googleBusinessProfileService, ldActivations, dataAxleService, NeustarClaimWorkflowService, googleBulkConnectService, directSyncSourcesService, fbService, neustarService, bingPlacesService, abcVendorService, uberallHttpClient, accountsWrapperService, *listingProfileService, syncDataService, BingInsightsService, seoServer)

	aiSuggestionService := aisuggestionservice.New(openAiClient, categoryClient, listingProfileService)

	suggetionsRepository := suggestionsrepository.New(vstoreClient)
	suggestionsSerivce := suggestionsservice.New(suggetionsRepository)

	suggestionAPIServer := api.NewSuggestionServer(aiSuggestionService, suggestionsSerivce, listingProfileService)
	seoSuggestedKeywordsServer := api.NewSEOSuggestedKeywordsServer(seoSuggestedKeywordsService, listingProfileService, dataForSEOClient, seoWorkflowService, categoryClient)
	citationServer := api.NewCitationServer(citationWorkflowService, citationService)
	partnerSettingsServer := api.NewPartnerSettingsServer(partnerSettingsService, authService)

	// make sure these are all the right initializers
	UnIgnoreSourceService := workflow.New(temporalObject, "unignore-source")
	bqClient, err := temporalbigquery.NewClient(ctx, "repcore-prod")
	if err != nil {
		logging.Criticalf(ctx, "Error initializing bigquery client: %s", err.Error())
		os.Exit(bigQueryClientErr)
	}
	rowProcessor := &workflow.RowProcessor{
		AccountGroup:         accountGroupService,
		ListingSourceService: listingSourceService,
		CoreServices:         csListingSourceClient,
	}
	bqActivity := temporalbigquery.NewBQActivity[workflow.AccountGroupRow](bqClient, rowProcessor)
	unIgnoreSourcesWorkflow := workflow.New(temporalObject, "unignore-sources")
	err = unIgnoreSourcesWorkflow.SetupTemporalAndStartWorker(ctx, bqActivity)
	if err != nil {
		logging.Criticalf(ctx, "Error setting up temporal and start worker: %s", err.Error())
		os.Exit(temporalSetupErr)
	}

	googleUpdatedWorkflow := googleupdatedworkflow.NewService(csGMBClient, accountGroupWrapperService, googleUpdatedLocationService, googleBusinessProfileService, temporalClient, categoryClient, suggestionService, csGMBClient, accountsWrapperService, listingSyndicationClient, listingProfileService)
	googleUpdatedWorkflowMuxHandler := googleupdatedworkflow.NewMuxHandler(googleUpdatedWorkflow)

	listing_products_v1.RegisterListingProductsServiceServer(grpcServer, listingProductsAPIServer)
	listing_products_v1.RegisterListingSourceServiceServer(grpcServer, listingSourceAPIServer)
	listing_products_v1.RegisterListingProfileServiceServer(grpcServer, listingProfileAPIServer)
	listing_products_v1.RegisterSuggestionServiceServer(grpcServer, suggestionAPIServer)
	listing_products_v1.RegisterSEOServiceServer(grpcServer, seoServer)
	listing_products_v1.RegisterCitationsServer(grpcServer, citationServer)
	listing_products_v1.RegisterPartnerSettingsServiceServer(grpcServer, partnerSettingsServer)
	listing_products_v1.RegisterSEOSuggestedKeywordsServiceServer(grpcServer, seoSuggestedKeywordsServer)

	googleUpdatedWorkflow.RegisterWorkflowAndStartWorker(ctx)

	mux := http.NewServeMux()
	mux.Handle("/", serverconfig.CORS(bifrost.Wrap(
		grpcServer,
		strconv.Itoa(grpcPort),
		bifrost.WithGRPCDialOptions(
			grpc.WithDefaultCallOptions(grpc.MaxCallRecvMsgSize(1024*1024*16)),
		),
	)))
	mux.HandleFunc("/start-citation-workflow", citationWorkflowService.HandleCitationSearchRequest)
	mux.HandleFunc("/handle-bulk-connect-workflow", listingProductsAPIServer.HandleBulkConnectRequest)
	mux.Handle("/unignore-sources-workflow", iamInterceptorv2.HTTPHandlerInterceptor(UnIgnoreSourceService.HandleUnIgnoreSourceRequest()))
	mux.HandleFunc("/start-google-updated-workflow", googleUpdatedWorkflowMuxHandler.HandleStartGoogleUpdatedWorkflowRequest)
	mux.HandleFunc("/start-aioaudit-workflow", func(w http.ResponseWriter, r *http.Request) {
		logging.Infof(ctx, "Received request to /start-aioaudit-workflow")
		if r.Method != http.MethodPost {
			logging.Errorf(ctx, "Invalid HTTP method: %s", r.Method)
			w.WriteHeader(http.StatusMethodNotAllowed)
			w.Write([]byte("Only POST method is allowed"))
			return
		}
		body, err := io.ReadAll(r.Body)
		if err != nil {
			logging.Errorf(ctx, "Error reading request body: %s", err.Error())
			w.WriteHeader(http.StatusBadRequest)
			w.Write([]byte("Error reading request body: " + err.Error()))
			return
		}
		defer r.Body.Close()
		logging.Infof(ctx, "Request body: %s", string(body))

		type AIOAuditRequest struct {
			BusinessID   string `json:"business_id"`
			Website      string `json:"website"`
			BusinessName string `json:"business_name"`
			AuditDate    string `json:"audit_date,omitempty"`
		}
		var request AIOAuditRequest
		err = json.Unmarshal(body, &request)
		if err != nil {
			logging.Errorf(ctx, "Error parsing JSON: %s", err.Error())
			w.WriteHeader(http.StatusBadRequest)
			w.Write([]byte("Error parsing JSON: " + err.Error()))
			return
		}

		if request.BusinessID == "" {
			logging.Errorf(ctx, "Business ID is required")
			w.WriteHeader(http.StatusBadRequest)
			w.Write([]byte("business_id is required"))
			return
		}

		logging.Infof(ctx, "Starting AIOAudit workflow for business: %s", request.BusinessID)

		// Use provided audit date or default to current date
		auditDate := request.AuditDate
		if auditDate == "" {
			auditDate = time.Now().Format("2006-01-02")
			logging.Infof(ctx, "No audit date provided, using current date: %s", auditDate)
		} else {
			logging.Infof(ctx, "Using provided audit date: %s", auditDate)
		}

		err = aioauditWorkflowService.StartAIOAuditWorkflow(ctx, request.BusinessID, request.Website, request.BusinessName, auditDate)
		if err != nil {
			logging.Errorf(ctx, "Error starting AIOAudit workflow: %s", err.Error())
			w.WriteHeader(http.StatusInternalServerError)
			w.Write([]byte("Error starting AIOAudit workflow: " + err.Error()))
			return
		}

		logging.Infof(ctx, "AIOAudit workflow started successfully for business: %s", request.BusinessID)
		response := struct {
			Success    bool   `json:"success"`
			Message    string `json:"message"`
			BusinessID string `json:"business_id"`
			AuditDate  string `json:"audit_date"`
		}{
			Success:    true,
			Message:    "AIOAudit workflow started successfully",
			BusinessID: request.BusinessID,
			AuditDate:  auditDate,
		}
		responseJSON, err := json.Marshal(response)
		if err != nil {
			logging.Errorf(ctx, "Error creating response JSON: %s", err.Error())
			w.WriteHeader(http.StatusInternalServerError)
			w.Write([]byte("Error creating response: " + err.Error()))
			return
		}

		logging.Infof(ctx, "Response JSON: %s", string(responseJSON))
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		w.Write(responseJSON)
	})
	mux.Handle("/register-tesseract", iamInterceptors.HTTPInterceptor(func(ctx context.Context, w http.ResponseWriter, r *http.Request) {
		err = initializeBingInsightTessaractSync(ctx, vetlClient)
		if err != nil {
			logging.Criticalf(ctx, "Could not initialize tesseract sync: %s", err.Error())
		}

	}))

	mux.HandleFunc("/google-updated-test", func(w http.ResponseWriter, r *http.Request) {
		b, err := io.ReadAll(r.Body)
		if err != nil {
			w.WriteHeader(http.StatusInternalServerError)
			w.Write([]byte(err.Error()))
			return
		}
		type body struct {
			BusinessIDs []string `json:"business_ids"`
		}
		var bdy body
		err = json.Unmarshal(b, &bdy)
		if err != nil {
			w.WriteHeader(http.StatusInternalServerError)
			w.Write([]byte(err.Error()))
			return
		}
		var errors []string
		for _, id := range bdy.BusinessIDs {
			err = googleUpdatedWorkflow.GetAndStoreUpdatedFields(ctx, id)
			if err != nil {
				errors = append(errors, err.Error())
			}
		}
		if len(errors) > 0 {
			w.WriteHeader(http.StatusInternalServerError)
			w.Write([]byte(strings.Join(errors, ",")))
			return
		}
		w.WriteHeader(http.StatusOK)
		return
	})

	mux.HandleFunc("/bing-device-test", func(w http.ResponseWriter, r *http.Request) {

		in := &bingdeviceplatforminsightModel.BingDevicePlatformInsights{
			AccountGroupId:        "AG-2345",
			Date:                  "2023-10-23",
			DesktopMapClicks:      12,
			DesktopMapImpression:  20,
			DesktopSERPClicks:     22,
			DesktopSERPImpression: 30,
			MobileMapClicks:       14,
			MobileMapImpression:   17,
			MobileSERPClicks:      22,
			MobileSERPImpression:  20,
		}

		err = bingDevicePlatformInsightsRepo.Create(ctx, in)
		if err != nil {
			w.WriteHeader(http.StatusInternalServerError)
			w.Write([]byte(err.Error()))
			return
		}

		w.WriteHeader(http.StatusOK)
		return
	})
	mux.HandleFunc("/start-keyword-info-workflows", func(w http.ResponseWriter, r *http.Request) {
		logging.Infof(ctx, "Received request to /start-keyword-info-workflows")
		if r.Method != http.MethodPost {
			logging.Errorf(ctx, "Invalid HTTP method: %s", r.Method)
			w.WriteHeader(http.StatusMethodNotAllowed)
			w.Write([]byte("Only POST method is allowed"))
			return
		}
		body, err := io.ReadAll(r.Body)
		if err != nil {
			logging.Errorf(ctx, "Error reading request body: %s", err.Error())
			w.WriteHeader(http.StatusBadRequest)
			w.Write([]byte("Error reading request body: " + err.Error()))
			return
		}
		defer r.Body.Close()
		logging.Infof(ctx, "Request body: %s", string(body))
		type KeywordInput struct {
			BusinessID string   `json:"business_id"`
			Keywords   []string `json:"keywords"`
			Date       string   `json:"date,omitempty"`
		}
		var inputs []KeywordInput
		err = json.Unmarshal(body, &inputs)
		if err != nil {
			logging.Errorf(ctx, "Error parsing JSON: %s", err.Error())
			w.WriteHeader(http.StatusBadRequest)
			w.Write([]byte("Error parsing JSON: " + err.Error()))
			return
		}
		logging.Infof(ctx, "Parsed inputs: %+v", inputs)
		businesses := make([]*seoworkflow.KeywordSearchParams, 0, len(inputs))
		for _, input := range inputs {
			businesses = append(businesses, &seoworkflow.KeywordSearchParams{
				BusinessID: input.BusinessID,
				Keywords:   input.Keywords,
			})
		}
		logging.Infof(ctx, "Prepared businesses: %+v", businesses)
		var date time.Time
		if len(inputs) > 0 && inputs[0].Date != "" {
			date, err = time.Parse("2006-01-02", inputs[0].Date)
			if err != nil {
				logging.Errorf(ctx, "Invalid date format: %s", err.Error())
				w.WriteHeader(http.StatusBadRequest)
				w.Write([]byte("Invalid date format. Use YYYY-MM-DD: " + err.Error()))
				return
			}
		} else {
			date = time.Now()
		}
		logging.Infof(ctx, "Using date: %s", date.Format("2006-01-02"))
		errors, err := seoWorkflowService.StartKeywordInfoWorkflows(ctx, date, businesses)
		if err != nil {
			logging.Errorf(ctx, "Error starting keyword info workflows: %s", err.Error())
			w.WriteHeader(http.StatusInternalServerError)
			w.Write([]byte("Error starting keyword info workflows: " + err.Error()))
			return
		}
		logging.Infof(ctx, "Workflow started with errors: %+v", errors)
		response := struct {
			Success bool     `json:"success"`
			Errors  []string `json:"errors,omitempty"`
		}{
			Success: len(errors) == 0,
			Errors:  errors,
		}
		responseJSON, err := json.Marshal(response)
		if err != nil {
			logging.Errorf(ctx, "Error creating response JSON: %s", err.Error())
			w.WriteHeader(http.StatusInternalServerError)
			w.Write([]byte("Error creating response: " + err.Error()))
			return
		}
		logging.Infof(ctx, "Response JSON: %s", string(responseJSON))
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		w.Write(responseJSON)
	})
	mux.Handle("/register-bing-click-source-tesseract", iamInterceptors.HTTPInterceptor(func(ctx context.Context, w http.ResponseWriter, r *http.Request) {
		err = initializeBingClickSourceInsightTessaractSync(ctx, vetlClient)
		if err != nil {
			logging.Criticalf(ctx, "Could not initialize tesseract sync: %s", err.Error())
		}
	}))

	mux.Handle("/register-bing-similar-business-tesseract", iamInterceptors.HTTPInterceptor(func(ctx context.Context, w http.ResponseWriter, r *http.Request) {
		err = initializeBingSimilarBusinessInsightTessaractSync(ctx, vetlClient)
		if err != nil {
			logging.Criticalf(ctx, "Could not initialize tesseract sync: %s", err.Error())
		}
	}))

	mux.Handle("/register-seo-data-tesseract", iamInterceptors.HTTPInterceptor(func(ctx context.Context, w http.ResponseWriter, r *http.Request) {
		err = initializeSeoDataTessaractSync(ctx, vetlClient)
		if err != nil {
			logging.Criticalf(ctx, "Could not initialize tesseract sync: %s", err.Error())
		}
	}))

	// Set up event broker client and register events.
	eventBrokerClient, err := eventbroker.New(ctx, constants.AppID, env)
	if err != nil {
		logging.Criticalf(ctx, "Could not create the eventBroker handler: %s", err.Error())
		os.Exit(eventBrokerClientErr)
	}
	eventService := events.NewService(eventBrokerClient)
	err = eventService.RegisterEvents(ctx)
	if err != nil {
		logging.Criticalf(ctx, "Could not register one or more events with Event Broker: %s", err.Error())
		os.Exit(eventRegistrationErr)
	}

	snapshotEventHandler := snapshotevents.NewService(listingProfileService, snapshotSEOClient, accountsClient, seoWorkflowService, seoSettingsService)

	err = eventBrokerClient.Subscribe(ctx, mux, snapshotevents.EventSubscriptionID, local_seo_data_updated.SnapshotLocalSEODataUpdatedEventDefn.ID, snapshotEventHandler.Process)
	if err != nil {
		logging.Criticalf(ctx, "Error subscribing to event %s: %s", local_seo_data_updated.SnapshotLocalSEODataUpdatedEventDefn.ID, err.Error())
		os.Exit(snapshotSubscriptionError)
	}

	agDeleteHandler := listingprofileevents.NewAccountGroupUpdateEventHandler(eventBrokerClient, listingProfileService)
	err = agDeleteHandler.HandleAccountGroupUpdates(ctx, mux)
	if err != nil {
		logging.Criticalf(ctx, "Unable to register account group update delete handler: %s", err.Error())
		os.Exit(eventRegistrationErr)
	}

	agBusinessProfileUpdateHandler := listingprofileevents.NewHandler(eventBrokerClient, businessDataFieldStatusService, listingProfileService, suggestionService)
	err = agBusinessProfileUpdateHandler.HandleFieldStatusUpdate(ctx, mux)
	if err != nil {
		logging.Criticalf(ctx, "Unable to register field status handler for account group update event: %s", err.Error())
		os.Exit(eventRegistrationErr)
	}

	err = agBusinessProfileUpdateHandler.HandleAccountGroupUpdate(ctx, mux)
	if err != nil {
		logging.Criticalf(ctx, "Unable to register listing profile handler for account group update event: %s", err.Error())
		os.Exit(eventRegistrationErr)
	}

	// Note: this handler is still used by other services sending messages to the GCP pubsub. Let's keep this for now
	// so it won't throw any unexpected errors.
	mux.Handle("/listing-profile-update", iamInterceptors.HTTPInterceptor(func(ctx context.Context, w http.ResponseWriter, r *http.Request) {
		// Just return 200 for the profile update mux endpoint. This way we don't have to process any changes from the account group to the listing profile
		// TODO: Delete subscription in GCP PubSub once we've fully removed two-way sync
		w.Header().Add("Content-Type", "application/json")
		w.WriteHeader(200)
	}))

	// Create a handler to emit Event Broker Listing profile update events
	listingProfileUpdateHandler := listingprofilepublish.NewListingProfileUpdateHandler(eventService, partnerSettingsService, seoSettingsService, seoWorkflowService)
	err = vstoreClient.RegisterSubscriptionCallback(ctx, listingprofilemodel.Kind, "pubsub",
		"publish-updates", listingProfileUpdateHandler.EmitEventHandler, listingProfileUpdateHandler.Cancel)
	if err != nil {
		logging.Criticalf(ctx, "Error initializing listingProfileUpdate pubsub: %s", err.Error())
		os.Exit(listingProfilePubSubUpdateHandlerErr)
	}

	// Create a handler to run SEO Workflows when keywords are added
	err = vstoreClient.RegisterSubscriptionCallback(ctx, listingprofilemodel.Kind, "pubsub",
		"seo-workflows", listingProfileUpdateHandler.RunWorkflowForNewKeywordsHandler, listingProfileUpdateHandler.Cancel)
	if err != nil {
		logging.Criticalf(ctx, "Error initializing listingProfileUpdate pubsub: %s", err.Error())
		os.Exit(listingProfilePubSubUpdateHandlerErr)
	}

	// Create a handler to update favorite keywords when keywords change
	err = vstoreClient.RegisterSubscriptionCallback(ctx, listingprofilemodel.Kind, "pubsub",
		"favorite-keywords", listingProfileUpdateHandler.UpdateFavoriteKeywordsHandler, listingProfileUpdateHandler.Cancel)
	if err != nil {
		logging.Criticalf(ctx, "Error initializing listingProfileUpdate pubsub: %s", err.Error())
		os.Exit(listingProfilePubSubUpdateHandlerErr)
	}

	// Create a handler to sync Listing profile to sources
	listingProfileSyncHandler := listingprofilepublish.NewListingProfileSyncHandler(*listingProfileService, featureFlagClient, accountGroupService)
	err = vstoreClient.RegisterSubscriptionCallback(ctx, listingprofilemodel.Kind, "pubsub", "sync-updates", listingProfileSyncHandler.Handler, listingProfileSyncHandler.Cancel)
	if err != nil {
		logging.Criticalf(ctx, "Error initializing listingProfileSync pubsub: %s", err.Error())
		os.Exit(listingProfilePubSubSyncHandlerErr)
	}

	// Set this to prod for now, we can change it later once we verified
	// that we're getting the right notifications
	project := fmt.Sprintf("repcore-%s", config.Prod.Name())
	// Create a handler to get the google updated fields
	gmbNofiticationHanlder := googleupdatedworkflow.NewNotificationHandler(env, googleUpdatedWorkflow)
	err = vpubsub.StartHandlingForDifferentProject(ctx, pubsubClient, gmbNofiticationHanlder, project, vpubsub.WithNackOnError(true))
	if err != nil {
		logging.Criticalf(ctx, "Error initializing GMB notification pubsub: %s", err.Error())
		os.Exit(gmbNotificationHandlerErr)
	}

	// add apple syndication here
	setupPurchaseWebhookHandler(ctx, env, temporalObject, accountsClient, accountsV3Client, accountGroupWrapperService, marketplaceWebhooksAccountClient, csGMBClient, orderFulfillmentClient, mux, vbcLspClient, addonAttributesService, uberallService, yextSDKClient, dataAxleService, neustarService, seoWorkflowService, listingSyndicationClient, accountsWrapperService, seoSuggestedKeywordsServer, listingProfileService, listingSourceService, featureFlagClient, BingInsightsService)

	err = pubsubhandlers.StartHandlingPubsubs(ctx, env, pubsubClient,
		datahealth.NewService(temporalObject, slackAlertService),
		neustarverification.NewService(temporalObject, env),
		dataaxleverification.NewService(temporalObject, env),
		sftpworkflow.NewService(temporalObject.Client),
		seoWorkflowService,
		bingInsightsWorkflowService,
	)
	if err != nil {
		logging.Criticalf(ctx, "Could not start handling pubsub topics: %s", err.Error())
		os.Exit(startPubSubHandlingErr)
	}

	err = vstoreClient.SetupRegistrationEndpoints(mux, []*vstore.Schema{
		dataaxlelocation.Schema(),
		gmblocation.Schema(),
		fblocation.Schema(),
		listingsource.Schema(),
		uberalllocation.Schema(),
		addonattributes.Schema(),
		neustarlocation.Schema(),
		listingprofilemodel.Schema(),
		applebusinessconnectlocation.Schema(),
		seodata.Schema(),
		citationdata.Schema(),
		rejectedcitations.Schema(),
		seosettings.Schema(),
		bingplacesmodel.Schema(),
		partnersettings.Schema(),
		smartyvalidationrecord.Schema(),
		seosuggestedkeywords.Schema(),
		dataforseotask.Schema(),
		dataforseocategories.Schema(),
		businessdatascore.Schema(),
		businessprofilefieldstatus.Schema(),
		suggestions.Schema(),
		keywordinfo.Schema(),
		googleupdatedlocation.Schema(),
		bingdeviceplatforminsightModel.Schema(),
		bingclicksourceinsightModel.Schema(),
		bingsimilarbusinessinsightModel.Schema(),
		seogooglekeywordinfo.Schema(),
		seofailedworkflowinfo.Schema(),
		aioauditresult.Schema(),
	})
	if err != nil {
		logging.Criticalf(ctx, "Could not register VStore Schemas: %s", err.Error())
		os.Exit(vstoreRegistrationErr)
	}

	if err := serverconfig.StartAndListenServer(ctx, grpcServer, mux,
		serverconfig.WithHTTPPort(httpPort),
		serverconfig.WithGRPCPort(grpcPort),
		serverconfig.WithHealthz(func() error {
			return nil
		}),
		serverconfig.EnableHTTPLogging(true),
	); err != nil {
		logging.Criticalf(ctx, "error running gRPC and HTTP server: %s", err.Error())
		os.Exit(1)
	}

}
