// Code generated by MockGen. DO NOT EDIT.
// Source: interface.go
//
// Generated by this command:
//
//	mockgen -destination mock.go -source=interface.go -package=aioauditresultservice
//

// Package aioauditresultservice is a generated GoMock package.
package aioauditresultservice

import (
	context "context"
	reflect "reflect"

	aioauditresult "github.com/vendasta/listing-products/internal/seo/aioauditresult"
	gomock "go.uber.org/mock/gomock"
)

// MockService is a mock of Service interface.
type MockService struct {
	ctrl     *gomock.Controller
	recorder *MockServiceMockRecorder
	isgomock struct{}
}

// MockServiceMockRecorder is the mock recorder for MockService.
type MockServiceMockRecorder struct {
	mock *MockService
}

// NewMockService creates a new mock instance.
func NewMockService(ctrl *gomock.Controller) *MockService {
	mock := &MockService{ctrl: ctrl}
	mock.recorder = &MockServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockService) EXPECT() *MockServiceMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockService) Create(ctx context.Context, businessID, websiteURL, auditDate string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, businessID, websiteURL, auditDate)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockServiceMockRecorder) Create(ctx, businessID, websiteURL, auditDate any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockService)(nil).Create), ctx, businessID, websiteURL, auditDate)
}

// Delete mocks base method.
func (m *MockService) Delete(ctx context.Context, businessID, websiteURL, auditDate string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Delete", ctx, businessID, websiteURL, auditDate)
	ret0, _ := ret[0].(error)
	return ret0
}

// Delete indicates an expected call of Delete.
func (mr *MockServiceMockRecorder) Delete(ctx, businessID, websiteURL, auditDate any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockService)(nil).Delete), ctx, businessID, websiteURL, auditDate)
}

// Get mocks base method.
func (m *MockService) Get(ctx context.Context, businessID, websiteURL, auditDate string) (*aioauditresult.AIOAuditResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Get", ctx, businessID, websiteURL, auditDate)
	ret0, _ := ret[0].(*aioauditresult.AIOAuditResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Get indicates an expected call of Get.
func (mr *MockServiceMockRecorder) Get(ctx, businessID, websiteURL, auditDate any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockService)(nil).Get), ctx, businessID, websiteURL, auditDate)
}

// GetAIOAuditResult mocks base method.
func (m *MockService) GetAIOAuditResult(ctx context.Context, businessID, websiteURL, auditDate string) (*aioauditresult.AIOAuditResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAIOAuditResult", ctx, businessID, websiteURL, auditDate)
	ret0, _ := ret[0].(*aioauditresult.AIOAuditResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAIOAuditResult indicates an expected call of GetAIOAuditResult.
func (mr *MockServiceMockRecorder) GetAIOAuditResult(ctx, businessID, websiteURL, auditDate any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAIOAuditResult", reflect.TypeOf((*MockService)(nil).GetAIOAuditResult), ctx, businessID, websiteURL, auditDate)
}

// GetALLAIOAuditResult mocks base method.
func (m *MockService) GetALLAIOAuditResult(ctx context.Context, businessID string) ([]*aioauditresult.AIOAuditResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetALLAIOAuditResult", ctx, businessID)
	ret0, _ := ret[0].([]*aioauditresult.AIOAuditResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetALLAIOAuditResult indicates an expected call of GetALLAIOAuditResult.
func (mr *MockServiceMockRecorder) GetALLAIOAuditResult(ctx, businessID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetALLAIOAuditResult", reflect.TypeOf((*MockService)(nil).GetALLAIOAuditResult), ctx, businessID)
}

// GetAuditPageResult mocks base method.
func (m *MockService) GetAuditPageResult(ctx context.Context, businessID, websiteURL, auditDate, pageURL string) ([]*aioauditresult.AuditPageData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAuditPageResult", ctx, businessID, websiteURL, auditDate, pageURL)
	ret0, _ := ret[0].([]*aioauditresult.AuditPageData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAuditPageResult indicates an expected call of GetAuditPageResult.
func (mr *MockServiceMockRecorder) GetAuditPageResult(ctx, businessID, websiteURL, auditDate, pageURL any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAuditPageResult", reflect.TypeOf((*MockService)(nil).GetAuditPageResult), ctx, businessID, websiteURL, auditDate, pageURL)
}

// GetAuditScoreResult mocks base method.
func (m *MockService) GetAuditScoreResult(ctx context.Context, businessID, websiteURL, auditDate string) ([]*aioauditresult.AuditScoreResults, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAuditScoreResult", ctx, businessID, websiteURL, auditDate)
	ret0, _ := ret[0].([]*aioauditresult.AuditScoreResults)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAuditScoreResult indicates an expected call of GetAuditScoreResult.
func (mr *MockServiceMockRecorder) GetAuditScoreResult(ctx, businessID, websiteURL, auditDate any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAuditScoreResult", reflect.TypeOf((*MockService)(nil).GetAuditScoreResult), ctx, businessID, websiteURL, auditDate)
}

// GetAuditStatus mocks base method.
func (m *MockService) GetAuditStatus(ctx context.Context, businessID, websiteURL, auditDate string) (aioauditresult.AuditStatus, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAuditStatus", ctx, businessID, websiteURL, auditDate)
	ret0, _ := ret[0].(aioauditresult.AuditStatus)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAuditStatus indicates an expected call of GetAuditStatus.
func (mr *MockServiceMockRecorder) GetAuditStatus(ctx, businessID, websiteURL, auditDate any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAuditStatus", reflect.TypeOf((*MockService)(nil).GetAuditStatus), ctx, businessID, websiteURL, auditDate)
}

// GetAuditSummary mocks base method.
func (m *MockService) GetAuditSummary(ctx context.Context, businessID, websiteURL, auditDate string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAuditSummary", ctx, businessID, websiteURL, auditDate)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAuditSummary indicates an expected call of GetAuditSummary.
func (mr *MockServiceMockRecorder) GetAuditSummary(ctx, businessID, websiteURL, auditDate any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAuditSummary", reflect.TypeOf((*MockService)(nil).GetAuditSummary), ctx, businessID, websiteURL, auditDate)
}

// List mocks base method.
func (m *MockService) List(ctx context.Context, cursorIn string, pageSize int64) ([]*aioauditresult.AIOAuditResult, string, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "List", ctx, cursorIn, pageSize)
	ret0, _ := ret[0].([]*aioauditresult.AIOAuditResult)
	ret1, _ := ret[1].(string)
	ret2, _ := ret[2].(bool)
	ret3, _ := ret[3].(error)
	return ret0, ret1, ret2, ret3
}

// List indicates an expected call of List.
func (mr *MockServiceMockRecorder) List(ctx, cursorIn, pageSize any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "List", reflect.TypeOf((*MockService)(nil).List), ctx, cursorIn, pageSize)
}

// UpdateAIOAuditResult mocks base method.
func (m *MockService) UpdateAIOAuditResult(ctx context.Context, businessID, websiteURL, auditDate string, update aioauditresult.AIOAuditResult) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateAIOAuditResult", ctx, businessID, websiteURL, auditDate, update)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateAIOAuditResult indicates an expected call of UpdateAIOAuditResult.
func (mr *MockServiceMockRecorder) UpdateAIOAuditResult(ctx, businessID, websiteURL, auditDate, update any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAIOAuditResult", reflect.TypeOf((*MockService)(nil).UpdateAIOAuditResult), ctx, businessID, websiteURL, auditDate, update)
}

// UpdateAuditAllPages mocks base method.
func (m *MockService) UpdateAuditAllPages(ctx context.Context, businessID, websiteURL, auditDate string, update []*aioauditresult.AuditPageData) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateAuditAllPages", ctx, businessID, websiteURL, auditDate, update)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateAuditAllPages indicates an expected call of UpdateAuditAllPages.
func (mr *MockServiceMockRecorder) UpdateAuditAllPages(ctx, businessID, websiteURL, auditDate, update any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAuditAllPages", reflect.TypeOf((*MockService)(nil).UpdateAuditAllPages), ctx, businessID, websiteURL, auditDate, update)
}

// UpdateAuditScoreResult mocks base method.
func (m *MockService) UpdateAuditScoreResult(ctx context.Context, businessID, websiteURL, auditDate string, update []aioauditresult.AuditScoreResults) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateAuditScoreResult", ctx, businessID, websiteURL, auditDate, update)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateAuditScoreResult indicates an expected call of UpdateAuditScoreResult.
func (mr *MockServiceMockRecorder) UpdateAuditScoreResult(ctx, businessID, websiteURL, auditDate, update any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAuditScoreResult", reflect.TypeOf((*MockService)(nil).UpdateAuditScoreResult), ctx, businessID, websiteURL, auditDate, update)
}

// UpdateAuditStatus mocks base method.
func (m *MockService) UpdateAuditStatus(ctx context.Context, businessID, websiteURL, auditDate string, status aioauditresult.AuditStatus) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateAuditStatus", ctx, businessID, websiteURL, auditDate, status)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateAuditStatus indicates an expected call of UpdateAuditStatus.
func (mr *MockServiceMockRecorder) UpdateAuditStatus(ctx, businessID, websiteURL, auditDate, status any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAuditStatus", reflect.TypeOf((*MockService)(nil).UpdateAuditStatus), ctx, businessID, websiteURL, auditDate, status)
}

// UpsertAIOAuditResult mocks base method.
func (m *MockService) UpsertAIOAuditResult(ctx context.Context, businessID, websiteURL, auditDate string, update aioauditresult.AIOAuditResult) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertAIOAuditResult", ctx, businessID, websiteURL, auditDate, update)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpsertAIOAuditResult indicates an expected call of UpsertAIOAuditResult.
func (mr *MockServiceMockRecorder) UpsertAIOAuditResult(ctx, businessID, websiteURL, auditDate, update any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertAIOAuditResult", reflect.TypeOf((*MockService)(nil).UpsertAIOAuditResult), ctx, businessID, websiteURL, auditDate, update)
}

// UpsertAuditPageResult mocks base method.
func (m *MockService) UpsertAuditPageResult(ctx context.Context, businessID, websiteURL, auditDate, pageURL string, update aioauditresult.AuditPageData) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertAuditPageResult", ctx, businessID, websiteURL, auditDate, pageURL, update)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpsertAuditPageResult indicates an expected call of UpsertAuditPageResult.
func (mr *MockServiceMockRecorder) UpsertAuditPageResult(ctx, businessID, websiteURL, auditDate, pageURL, update any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertAuditPageResult", reflect.TypeOf((*MockService)(nil).UpsertAuditPageResult), ctx, businessID, websiteURL, auditDate, pageURL, update)
}
