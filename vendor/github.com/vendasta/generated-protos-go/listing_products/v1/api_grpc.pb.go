// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v6.30.0
// source: listing_products/v1/api.proto

package listing_products_v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	ListingProductsService_GetGoogleMyBusinessInsightsData_FullMethodName         = "/listing_products.v1.ListingProductsService/GetGoogleMyBusinessInsightsData"
	ListingProductsService_GetGoogleMyBusinessInsightsDataBucketed_FullMethodName = "/listing_products.v1.ListingProductsService/GetGoogleMyBusinessInsightsDataBucketed"
	ListingProductsService_UpdateVendorSyncFlag_FullMethodName                    = "/listing_products.v1.ListingProductsService/UpdateVendorSyncFlag"
	ListingProductsService_SubmitTurboLister_FullMethodName                       = "/listing_products.v1.ListingProductsService/SubmitTurboLister"
	ListingProductsService_GetAddonAttributes_FullMethodName                      = "/listing_products.v1.ListingProductsService/GetAddonAttributes"
	ListingProductsService_CreateAddonAttributes_FullMethodName                   = "/listing_products.v1.ListingProductsService/CreateAddonAttributes"
	ListingProductsService_UpdateAddonAttributes_FullMethodName                   = "/listing_products.v1.ListingProductsService/UpdateAddonAttributes"
	ListingProductsService_DeleteAddonAttributes_FullMethodName                   = "/listing_products.v1.ListingProductsService/DeleteAddonAttributes"
	ListingProductsService_GetMultiAddonAttributes_FullMethodName                 = "/listing_products.v1.ListingProductsService/GetMultiAddonAttributes"
	ListingProductsService_BulkConnectLocationsForGoogleUser_FullMethodName       = "/listing_products.v1.ListingProductsService/BulkConnectLocationsForGoogleUser"
	ListingProductsService_ManuallyFixYextProvisioning_FullMethodName             = "/listing_products.v1.ListingProductsService/ManuallyFixYextProvisioning"
	ListingProductsService_ManuallyProvisionUberall_FullMethodName                = "/listing_products.v1.ListingProductsService/ManuallyProvisionUberall"
	ListingProductsService_ManuallyDeactivateUberallProvisioning_FullMethodName   = "/listing_products.v1.ListingProductsService/ManuallyDeactivateUberallProvisioning"
	ListingProductsService_GetGoogleBusinessInfo_FullMethodName                   = "/listing_products.v1.ListingProductsService/GetGoogleBusinessInfo"
	ListingProductsService_GetFacebookPageInfo_FullMethodName                     = "/listing_products.v1.ListingProductsService/GetFacebookPageInfo"
	ListingProductsService_GetAppleBusinessConnectInfo_FullMethodName             = "/listing_products.v1.ListingProductsService/GetAppleBusinessConnectInfo"
	ListingProductsService_GetBingPlacesInfo_FullMethodName                       = "/listing_products.v1.ListingProductsService/GetBingPlacesInfo"
	ListingProductsService_TriggerBingInsightsBackFill_FullMethodName             = "/listing_products.v1.ListingProductsService/TriggerBingInsightsBackFill"
	ListingProductsService_CheckSubmissionStatus_FullMethodName                   = "/listing_products.v1.ListingProductsService/CheckSubmissionStatus"
	ListingProductsService_GetInfoGroupId_FullMethodName                          = "/listing_products.v1.ListingProductsService/GetInfoGroupId"
	ListingProductsService_GetListingDistributionActivationStatus_FullMethodName  = "/listing_products.v1.ListingProductsService/GetListingDistributionActivationStatus"
	ListingProductsService_GetDirectSyncSourceInfo_FullMethodName                 = "/listing_products.v1.ListingProductsService/GetDirectSyncSourceInfo"
	ListingProductsService_GetSyncData_FullMethodName                             = "/listing_products.v1.ListingProductsService/GetSyncData"
	ListingProductsService_GetSyndicationInfo_FullMethodName                      = "/listing_products.v1.ListingProductsService/GetSyndicationInfo"
	ListingProductsService_GetProductSettings_FullMethodName                      = "/listing_products.v1.ListingProductsService/GetProductSettings"
	ListingProductsService_GetDoctorDotComCategories_FullMethodName               = "/listing_products.v1.ListingProductsService/GetDoctorDotComCategories"
	ListingProductsService_FetchAndSubmitSourceURLsToCS_FullMethodName            = "/listing_products.v1.ListingProductsService/FetchAndSubmitSourceURLsToCS"
	ListingProductsService_StartBusinessDataScoreWorkflow_FullMethodName          = "/listing_products.v1.ListingProductsService/StartBusinessDataScoreWorkflow"
	ListingProductsService_IsPartnerUser_FullMethodName                           = "/listing_products.v1.ListingProductsService/IsPartnerUser"
	ListingProductsService_IsLocalSEOProActiveForAccount_FullMethodName           = "/listing_products.v1.ListingProductsService/IsLocalSEOProActiveForAccount"
	ListingProductsService_GetLDData_FullMethodName                               = "/listing_products.v1.ListingProductsService/GetLDData"
	ListingProductsService_SyncToSources_FullMethodName                           = "/listing_products.v1.ListingProductsService/SyncToSources"
	ListingProductsService_GetNeustarPublications_FullMethodName                  = "/listing_products.v1.ListingProductsService/GetNeustarPublications"
)

// ListingProductsServiceClient is the client API for ListingProductsService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ListingProductsServiceClient interface {
	// GetGoogleMyBusinessInsightsData gets Google My Business insights for a business
	GetGoogleMyBusinessInsightsData(ctx context.Context, in *GetGoogleMyBusinessInsightsDataRequest, opts ...grpc.CallOption) (*GetGoogleMyBusinessInsightsDataResponse, error)
	// GetGoogleMyBusinessInsightsDataBucketed gets bucketed Google My Business insights for a business
	GetGoogleMyBusinessInsightsDataBucketed(ctx context.Context, in *GetGoogleMyBusinessInsightsDataBucketedRequest, opts ...grpc.CallOption) (*GetGoogleMyBusinessInsightsDataBucketedResponse, error)
	// UpdateVendorSyncFlag sets the syncing property for a Vendor such as Bing or Apple.
	UpdateVendorSyncFlag(ctx context.Context, in *UpdateVendorSyncFlagRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// SubmitTurboLister initiates a workflow to submit listing data for a specified vendor
	// This endpoint is async and returns success when the task has been added to the Cadence workflow list.
	SubmitTurboLister(ctx context.Context, in *SubmitTurboListerRequest, opts ...grpc.CallOption) (*SubmitTurboListerResponse, error)
	// GetAddonAttributes fetches Addon attributes using the AddonID in request
	GetAddonAttributes(ctx context.Context, in *GetAddonAttributesRequest, opts ...grpc.CallOption) (*GetAddonAttributesResponse, error)
	// CreateAddonAttributes creates new record for Addon attributes
	CreateAddonAttributes(ctx context.Context, in *CreateAddonAttributesRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// UpdateAddonAttributes updates existing record for Addon attributes
	UpdateAddonAttributes(ctx context.Context, in *UpdateAddonAttributesRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// DeleteAddonAttributes deletes existing record for Addon attributes
	DeleteAddonAttributes(ctx context.Context, in *DeleteAddonAttributesRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// GetMultiAddonAttributes gets a list of Addon attributes based on included filters
	GetMultiAddonAttributes(ctx context.Context, in *GetMultiAddonAttributesRequest, opts ...grpc.CallOption) (*GetMultiAddonAttributesResponse, error)
	// BulkConnectLocationsForGoogleUser will take all the Google Business Profile locations for the given Google user and attempt
	// to connect them to account groups in the given partner. It will return a list of the connections made and the list of
	// GBP locations that could not be connected.
	BulkConnectLocationsForGoogleUser(ctx context.Context, in *BulkConnectLocationsForGoogleUserRequest, opts ...grpc.CallOption) (*BulkConnectLocationsForGoogleUserResponse, error)
	// Manual* IF YOU USE THESE ENDPOINTS IN CODE YOU WILL BREAK EVERYTHING AND LIKELY DIE
	// ManuallyFixYextProvisioning bypasses all of our Marketplace duplicate activation checks and can make things much much worse.
	// ManuallyFixYextProvisioning is a one off to fix a broken system state. Interactions with Yext should be done in the
	//
	//	  Yext microservice.
	//	This will look up the plan ID for the activation and pass the account to the Yext microservice to try to provision it
	//	  again since the automated process is broken. Please try to fix the bug that resulted in the account getting into this
	//	  state in addition to running this endpoint.
	ManuallyFixYextProvisioning(ctx context.Context, in *ManuallyFixYextProvisioningRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// ManuallyProvisionUberall bypasses all of our Marketplace duplicate activation checks and can make things much much worse.
	//
	//	This will try to provision the account again since the automated process is broken. Please try to fix the bug that
	//	resulted in the account getting into this state in addition to running this endpoint.
	ManuallyProvisionUberall(ctx context.Context, in *ManuallyProvisionUberallRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// ManuallyDeactivateUberallProvisioning bypasses all standard operations regarding Uberall location cancelling and deactivation.
	//
	//	It bypasses all Marketplace checks and immediately ends all service on an Uberall location.
	ManuallyDeactivateUberallProvisioning(ctx context.Context, in *ManuallyDeactivateUberallProvisioningRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// GetGoogleBusinessInfo is used for human Support on Demand interaction only. Google is very strict about how their data is used
	//
	//	and it should only be pulled when a user or agent working on behalf of the user has taken action to request it.
	//	This endpoint pulls the business information directly from Google's API using the tokens stored in Core Services.
	GetGoogleBusinessInfo(ctx context.Context, in *GetGoogleBusinessInfoRequest, opts ...grpc.CallOption) (*GetGoogleBusinessInfoResponse, error)
	GetFacebookPageInfo(ctx context.Context, in *GetFacebookPageInfoRequest, opts ...grpc.CallOption) (*GetFacebookPageInfoResponse, error)
	GetAppleBusinessConnectInfo(ctx context.Context, in *GetAppleBusinessConnectInfoRequest, opts ...grpc.CallOption) (*GetAppleBusinessConnectInfoResponse, error)
	GetBingPlacesInfo(ctx context.Context, in *GetBingPlacesInfoRequest, opts ...grpc.CallOption) (*GetBingPlacesInfoResponse, error)
	TriggerBingInsightsBackFill(ctx context.Context, in *TriggerBingInsightsBackFillRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// CheckSubmissionStatus takes in business_id in request and using this business_id we fetch the latest data-axle submission_id for that business(account) from vtsore.
	// The submission_id obtained is then used to fetch the latest submission status from data-axle.
	CheckSubmissionStatus(ctx context.Context, in *CheckSubmissionStatusRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// GetInfoGroupId uses business_id and returns InfoGroupID from DataAxleLocation model if available
	GetInfoGroupId(ctx context.Context, in *GetInfoGroupIdRequest, opts ...grpc.CallOption) (*GetInfoGroupIdResponse, error)
	// GetListingDistributionActivationStatus gets the status of Listing Distribution for the account provided in the request
	GetListingDistributionActivationStatus(ctx context.Context, in *GetListingDistributionActivationStatusRequest, opts ...grpc.CallOption) (*GetListingDistributionActivationStatusResponse, error)
	// GetDirectSyncSourceInfo returns information about the directly submitted sources for a given business
	GetDirectSyncSourceInfo(ctx context.Context, in *GetDirectSyncSourceInfoRequest, opts ...grpc.CallOption) (*GetDirectSyncSourceInfoResponse, error)
	// GetDirectSyncSourceInfo returns information about the directly submitted sources for a given business
	GetSyncData(ctx context.Context, in *GetSyncDataRequest, opts ...grpc.CallOption) (*GetSyncDataResponse, error)
	// GetSyndicationInfo returns information about the last syndication workflows performed for a given business
	GetSyndicationInfo(ctx context.Context, in *GetSyndicationInfoRequest, opts ...grpc.CallOption) (*GetSyndicationInfoResponse, error)
	// GetProductSettings returns information about the partner settings and checks permissions for a given partnerID / marketID
	GetProductSettings(ctx context.Context, in *GetProductSettingsRequest, opts ...grpc.CallOption) (*GetProductSettingsResponse, error)
	// GetDoctorDotComCategories returns the list of categories for Doctor.com
	GetDoctorDotComCategories(ctx context.Context, in *GetDoctorDotComCategoriesRequest, opts ...grpc.CallOption) (*GetDoctorDotComCategoriesResponse, error)
	// Deprecated: no more usage of this.
	FetchAndSubmitSourceURLsToCS(ctx context.Context, in *FetchAndSubmitSourceURLsToCSRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// StartBusinessDataScoreWorkflow starts a workflow to calculate the business data score for one or more businesses
	StartBusinessDataScoreWorkflow(ctx context.Context, in *StartBusinessDataScoreWorkflowRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// IsPartnerUser checks if the user making the request is a partner
	IsPartnerUser(ctx context.Context, in *IsPartnerUserRequest, opts ...grpc.CallOption) (*IsPartnerUserResponse, error)
	// IsLocalSEOProActiveForAccount checks if the account has LSEO Pro active
	IsLocalSEOProActiveForAccount(ctx context.Context, in *IsLocalSEOProActiveForAccountRequest, opts ...grpc.CallOption) (*IsLocalSEOProActiveForAccountResponse, error)
	// GetLDData returns the DirectSyncSource information for Neustar, Data Axle, and Foursquare
	GetLDData(ctx context.Context, in *GetLDDataRequest, opts ...grpc.CallOption) (*GetLDDataResponse, error)
	SyncToSources(ctx context.Context, in *SyncToSourcesRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	GetNeustarPublications(ctx context.Context, in *GetNeustarPublicationsRequest, opts ...grpc.CallOption) (*GetNeustarPublicationsResponse, error)
}

type listingProductsServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewListingProductsServiceClient(cc grpc.ClientConnInterface) ListingProductsServiceClient {
	return &listingProductsServiceClient{cc}
}

func (c *listingProductsServiceClient) GetGoogleMyBusinessInsightsData(ctx context.Context, in *GetGoogleMyBusinessInsightsDataRequest, opts ...grpc.CallOption) (*GetGoogleMyBusinessInsightsDataResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetGoogleMyBusinessInsightsDataResponse)
	err := c.cc.Invoke(ctx, ListingProductsService_GetGoogleMyBusinessInsightsData_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *listingProductsServiceClient) GetGoogleMyBusinessInsightsDataBucketed(ctx context.Context, in *GetGoogleMyBusinessInsightsDataBucketedRequest, opts ...grpc.CallOption) (*GetGoogleMyBusinessInsightsDataBucketedResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetGoogleMyBusinessInsightsDataBucketedResponse)
	err := c.cc.Invoke(ctx, ListingProductsService_GetGoogleMyBusinessInsightsDataBucketed_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *listingProductsServiceClient) UpdateVendorSyncFlag(ctx context.Context, in *UpdateVendorSyncFlagRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, ListingProductsService_UpdateVendorSyncFlag_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *listingProductsServiceClient) SubmitTurboLister(ctx context.Context, in *SubmitTurboListerRequest, opts ...grpc.CallOption) (*SubmitTurboListerResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SubmitTurboListerResponse)
	err := c.cc.Invoke(ctx, ListingProductsService_SubmitTurboLister_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *listingProductsServiceClient) GetAddonAttributes(ctx context.Context, in *GetAddonAttributesRequest, opts ...grpc.CallOption) (*GetAddonAttributesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAddonAttributesResponse)
	err := c.cc.Invoke(ctx, ListingProductsService_GetAddonAttributes_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *listingProductsServiceClient) CreateAddonAttributes(ctx context.Context, in *CreateAddonAttributesRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, ListingProductsService_CreateAddonAttributes_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *listingProductsServiceClient) UpdateAddonAttributes(ctx context.Context, in *UpdateAddonAttributesRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, ListingProductsService_UpdateAddonAttributes_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *listingProductsServiceClient) DeleteAddonAttributes(ctx context.Context, in *DeleteAddonAttributesRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, ListingProductsService_DeleteAddonAttributes_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *listingProductsServiceClient) GetMultiAddonAttributes(ctx context.Context, in *GetMultiAddonAttributesRequest, opts ...grpc.CallOption) (*GetMultiAddonAttributesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetMultiAddonAttributesResponse)
	err := c.cc.Invoke(ctx, ListingProductsService_GetMultiAddonAttributes_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *listingProductsServiceClient) BulkConnectLocationsForGoogleUser(ctx context.Context, in *BulkConnectLocationsForGoogleUserRequest, opts ...grpc.CallOption) (*BulkConnectLocationsForGoogleUserResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BulkConnectLocationsForGoogleUserResponse)
	err := c.cc.Invoke(ctx, ListingProductsService_BulkConnectLocationsForGoogleUser_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *listingProductsServiceClient) ManuallyFixYextProvisioning(ctx context.Context, in *ManuallyFixYextProvisioningRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, ListingProductsService_ManuallyFixYextProvisioning_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *listingProductsServiceClient) ManuallyProvisionUberall(ctx context.Context, in *ManuallyProvisionUberallRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, ListingProductsService_ManuallyProvisionUberall_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *listingProductsServiceClient) ManuallyDeactivateUberallProvisioning(ctx context.Context, in *ManuallyDeactivateUberallProvisioningRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, ListingProductsService_ManuallyDeactivateUberallProvisioning_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *listingProductsServiceClient) GetGoogleBusinessInfo(ctx context.Context, in *GetGoogleBusinessInfoRequest, opts ...grpc.CallOption) (*GetGoogleBusinessInfoResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetGoogleBusinessInfoResponse)
	err := c.cc.Invoke(ctx, ListingProductsService_GetGoogleBusinessInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *listingProductsServiceClient) GetFacebookPageInfo(ctx context.Context, in *GetFacebookPageInfoRequest, opts ...grpc.CallOption) (*GetFacebookPageInfoResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetFacebookPageInfoResponse)
	err := c.cc.Invoke(ctx, ListingProductsService_GetFacebookPageInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *listingProductsServiceClient) GetAppleBusinessConnectInfo(ctx context.Context, in *GetAppleBusinessConnectInfoRequest, opts ...grpc.CallOption) (*GetAppleBusinessConnectInfoResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAppleBusinessConnectInfoResponse)
	err := c.cc.Invoke(ctx, ListingProductsService_GetAppleBusinessConnectInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *listingProductsServiceClient) GetBingPlacesInfo(ctx context.Context, in *GetBingPlacesInfoRequest, opts ...grpc.CallOption) (*GetBingPlacesInfoResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetBingPlacesInfoResponse)
	err := c.cc.Invoke(ctx, ListingProductsService_GetBingPlacesInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *listingProductsServiceClient) TriggerBingInsightsBackFill(ctx context.Context, in *TriggerBingInsightsBackFillRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, ListingProductsService_TriggerBingInsightsBackFill_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *listingProductsServiceClient) CheckSubmissionStatus(ctx context.Context, in *CheckSubmissionStatusRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, ListingProductsService_CheckSubmissionStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *listingProductsServiceClient) GetInfoGroupId(ctx context.Context, in *GetInfoGroupIdRequest, opts ...grpc.CallOption) (*GetInfoGroupIdResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetInfoGroupIdResponse)
	err := c.cc.Invoke(ctx, ListingProductsService_GetInfoGroupId_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *listingProductsServiceClient) GetListingDistributionActivationStatus(ctx context.Context, in *GetListingDistributionActivationStatusRequest, opts ...grpc.CallOption) (*GetListingDistributionActivationStatusResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetListingDistributionActivationStatusResponse)
	err := c.cc.Invoke(ctx, ListingProductsService_GetListingDistributionActivationStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *listingProductsServiceClient) GetDirectSyncSourceInfo(ctx context.Context, in *GetDirectSyncSourceInfoRequest, opts ...grpc.CallOption) (*GetDirectSyncSourceInfoResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetDirectSyncSourceInfoResponse)
	err := c.cc.Invoke(ctx, ListingProductsService_GetDirectSyncSourceInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *listingProductsServiceClient) GetSyncData(ctx context.Context, in *GetSyncDataRequest, opts ...grpc.CallOption) (*GetSyncDataResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetSyncDataResponse)
	err := c.cc.Invoke(ctx, ListingProductsService_GetSyncData_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *listingProductsServiceClient) GetSyndicationInfo(ctx context.Context, in *GetSyndicationInfoRequest, opts ...grpc.CallOption) (*GetSyndicationInfoResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetSyndicationInfoResponse)
	err := c.cc.Invoke(ctx, ListingProductsService_GetSyndicationInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *listingProductsServiceClient) GetProductSettings(ctx context.Context, in *GetProductSettingsRequest, opts ...grpc.CallOption) (*GetProductSettingsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetProductSettingsResponse)
	err := c.cc.Invoke(ctx, ListingProductsService_GetProductSettings_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *listingProductsServiceClient) GetDoctorDotComCategories(ctx context.Context, in *GetDoctorDotComCategoriesRequest, opts ...grpc.CallOption) (*GetDoctorDotComCategoriesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetDoctorDotComCategoriesResponse)
	err := c.cc.Invoke(ctx, ListingProductsService_GetDoctorDotComCategories_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *listingProductsServiceClient) FetchAndSubmitSourceURLsToCS(ctx context.Context, in *FetchAndSubmitSourceURLsToCSRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, ListingProductsService_FetchAndSubmitSourceURLsToCS_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *listingProductsServiceClient) StartBusinessDataScoreWorkflow(ctx context.Context, in *StartBusinessDataScoreWorkflowRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, ListingProductsService_StartBusinessDataScoreWorkflow_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *listingProductsServiceClient) IsPartnerUser(ctx context.Context, in *IsPartnerUserRequest, opts ...grpc.CallOption) (*IsPartnerUserResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(IsPartnerUserResponse)
	err := c.cc.Invoke(ctx, ListingProductsService_IsPartnerUser_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *listingProductsServiceClient) IsLocalSEOProActiveForAccount(ctx context.Context, in *IsLocalSEOProActiveForAccountRequest, opts ...grpc.CallOption) (*IsLocalSEOProActiveForAccountResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(IsLocalSEOProActiveForAccountResponse)
	err := c.cc.Invoke(ctx, ListingProductsService_IsLocalSEOProActiveForAccount_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *listingProductsServiceClient) GetLDData(ctx context.Context, in *GetLDDataRequest, opts ...grpc.CallOption) (*GetLDDataResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetLDDataResponse)
	err := c.cc.Invoke(ctx, ListingProductsService_GetLDData_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *listingProductsServiceClient) SyncToSources(ctx context.Context, in *SyncToSourcesRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, ListingProductsService_SyncToSources_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *listingProductsServiceClient) GetNeustarPublications(ctx context.Context, in *GetNeustarPublicationsRequest, opts ...grpc.CallOption) (*GetNeustarPublicationsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetNeustarPublicationsResponse)
	err := c.cc.Invoke(ctx, ListingProductsService_GetNeustarPublications_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ListingProductsServiceServer is the server API for ListingProductsService service.
// All implementations should embed UnimplementedListingProductsServiceServer
// for forward compatibility.
type ListingProductsServiceServer interface {
	// GetGoogleMyBusinessInsightsData gets Google My Business insights for a business
	GetGoogleMyBusinessInsightsData(context.Context, *GetGoogleMyBusinessInsightsDataRequest) (*GetGoogleMyBusinessInsightsDataResponse, error)
	// GetGoogleMyBusinessInsightsDataBucketed gets bucketed Google My Business insights for a business
	GetGoogleMyBusinessInsightsDataBucketed(context.Context, *GetGoogleMyBusinessInsightsDataBucketedRequest) (*GetGoogleMyBusinessInsightsDataBucketedResponse, error)
	// UpdateVendorSyncFlag sets the syncing property for a Vendor such as Bing or Apple.
	UpdateVendorSyncFlag(context.Context, *UpdateVendorSyncFlagRequest) (*emptypb.Empty, error)
	// SubmitTurboLister initiates a workflow to submit listing data for a specified vendor
	// This endpoint is async and returns success when the task has been added to the Cadence workflow list.
	SubmitTurboLister(context.Context, *SubmitTurboListerRequest) (*SubmitTurboListerResponse, error)
	// GetAddonAttributes fetches Addon attributes using the AddonID in request
	GetAddonAttributes(context.Context, *GetAddonAttributesRequest) (*GetAddonAttributesResponse, error)
	// CreateAddonAttributes creates new record for Addon attributes
	CreateAddonAttributes(context.Context, *CreateAddonAttributesRequest) (*emptypb.Empty, error)
	// UpdateAddonAttributes updates existing record for Addon attributes
	UpdateAddonAttributes(context.Context, *UpdateAddonAttributesRequest) (*emptypb.Empty, error)
	// DeleteAddonAttributes deletes existing record for Addon attributes
	DeleteAddonAttributes(context.Context, *DeleteAddonAttributesRequest) (*emptypb.Empty, error)
	// GetMultiAddonAttributes gets a list of Addon attributes based on included filters
	GetMultiAddonAttributes(context.Context, *GetMultiAddonAttributesRequest) (*GetMultiAddonAttributesResponse, error)
	// BulkConnectLocationsForGoogleUser will take all the Google Business Profile locations for the given Google user and attempt
	// to connect them to account groups in the given partner. It will return a list of the connections made and the list of
	// GBP locations that could not be connected.
	BulkConnectLocationsForGoogleUser(context.Context, *BulkConnectLocationsForGoogleUserRequest) (*BulkConnectLocationsForGoogleUserResponse, error)
	// Manual* IF YOU USE THESE ENDPOINTS IN CODE YOU WILL BREAK EVERYTHING AND LIKELY DIE
	// ManuallyFixYextProvisioning bypasses all of our Marketplace duplicate activation checks and can make things much much worse.
	// ManuallyFixYextProvisioning is a one off to fix a broken system state. Interactions with Yext should be done in the
	//
	//	  Yext microservice.
	//	This will look up the plan ID for the activation and pass the account to the Yext microservice to try to provision it
	//	  again since the automated process is broken. Please try to fix the bug that resulted in the account getting into this
	//	  state in addition to running this endpoint.
	ManuallyFixYextProvisioning(context.Context, *ManuallyFixYextProvisioningRequest) (*emptypb.Empty, error)
	// ManuallyProvisionUberall bypasses all of our Marketplace duplicate activation checks and can make things much much worse.
	//
	//	This will try to provision the account again since the automated process is broken. Please try to fix the bug that
	//	resulted in the account getting into this state in addition to running this endpoint.
	ManuallyProvisionUberall(context.Context, *ManuallyProvisionUberallRequest) (*emptypb.Empty, error)
	// ManuallyDeactivateUberallProvisioning bypasses all standard operations regarding Uberall location cancelling and deactivation.
	//
	//	It bypasses all Marketplace checks and immediately ends all service on an Uberall location.
	ManuallyDeactivateUberallProvisioning(context.Context, *ManuallyDeactivateUberallProvisioningRequest) (*emptypb.Empty, error)
	// GetGoogleBusinessInfo is used for human Support on Demand interaction only. Google is very strict about how their data is used
	//
	//	and it should only be pulled when a user or agent working on behalf of the user has taken action to request it.
	//	This endpoint pulls the business information directly from Google's API using the tokens stored in Core Services.
	GetGoogleBusinessInfo(context.Context, *GetGoogleBusinessInfoRequest) (*GetGoogleBusinessInfoResponse, error)
	GetFacebookPageInfo(context.Context, *GetFacebookPageInfoRequest) (*GetFacebookPageInfoResponse, error)
	GetAppleBusinessConnectInfo(context.Context, *GetAppleBusinessConnectInfoRequest) (*GetAppleBusinessConnectInfoResponse, error)
	GetBingPlacesInfo(context.Context, *GetBingPlacesInfoRequest) (*GetBingPlacesInfoResponse, error)
	TriggerBingInsightsBackFill(context.Context, *TriggerBingInsightsBackFillRequest) (*emptypb.Empty, error)
	// CheckSubmissionStatus takes in business_id in request and using this business_id we fetch the latest data-axle submission_id for that business(account) from vtsore.
	// The submission_id obtained is then used to fetch the latest submission status from data-axle.
	CheckSubmissionStatus(context.Context, *CheckSubmissionStatusRequest) (*emptypb.Empty, error)
	// GetInfoGroupId uses business_id and returns InfoGroupID from DataAxleLocation model if available
	GetInfoGroupId(context.Context, *GetInfoGroupIdRequest) (*GetInfoGroupIdResponse, error)
	// GetListingDistributionActivationStatus gets the status of Listing Distribution for the account provided in the request
	GetListingDistributionActivationStatus(context.Context, *GetListingDistributionActivationStatusRequest) (*GetListingDistributionActivationStatusResponse, error)
	// GetDirectSyncSourceInfo returns information about the directly submitted sources for a given business
	GetDirectSyncSourceInfo(context.Context, *GetDirectSyncSourceInfoRequest) (*GetDirectSyncSourceInfoResponse, error)
	// GetDirectSyncSourceInfo returns information about the directly submitted sources for a given business
	GetSyncData(context.Context, *GetSyncDataRequest) (*GetSyncDataResponse, error)
	// GetSyndicationInfo returns information about the last syndication workflows performed for a given business
	GetSyndicationInfo(context.Context, *GetSyndicationInfoRequest) (*GetSyndicationInfoResponse, error)
	// GetProductSettings returns information about the partner settings and checks permissions for a given partnerID / marketID
	GetProductSettings(context.Context, *GetProductSettingsRequest) (*GetProductSettingsResponse, error)
	// GetDoctorDotComCategories returns the list of categories for Doctor.com
	GetDoctorDotComCategories(context.Context, *GetDoctorDotComCategoriesRequest) (*GetDoctorDotComCategoriesResponse, error)
	// Deprecated: no more usage of this.
	FetchAndSubmitSourceURLsToCS(context.Context, *FetchAndSubmitSourceURLsToCSRequest) (*emptypb.Empty, error)
	// StartBusinessDataScoreWorkflow starts a workflow to calculate the business data score for one or more businesses
	StartBusinessDataScoreWorkflow(context.Context, *StartBusinessDataScoreWorkflowRequest) (*emptypb.Empty, error)
	// IsPartnerUser checks if the user making the request is a partner
	IsPartnerUser(context.Context, *IsPartnerUserRequest) (*IsPartnerUserResponse, error)
	// IsLocalSEOProActiveForAccount checks if the account has LSEO Pro active
	IsLocalSEOProActiveForAccount(context.Context, *IsLocalSEOProActiveForAccountRequest) (*IsLocalSEOProActiveForAccountResponse, error)
	// GetLDData returns the DirectSyncSource information for Neustar, Data Axle, and Foursquare
	GetLDData(context.Context, *GetLDDataRequest) (*GetLDDataResponse, error)
	SyncToSources(context.Context, *SyncToSourcesRequest) (*emptypb.Empty, error)
	GetNeustarPublications(context.Context, *GetNeustarPublicationsRequest) (*GetNeustarPublicationsResponse, error)
}

// UnimplementedListingProductsServiceServer should be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedListingProductsServiceServer struct{}

func (UnimplementedListingProductsServiceServer) GetGoogleMyBusinessInsightsData(context.Context, *GetGoogleMyBusinessInsightsDataRequest) (*GetGoogleMyBusinessInsightsDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGoogleMyBusinessInsightsData not implemented")
}
func (UnimplementedListingProductsServiceServer) GetGoogleMyBusinessInsightsDataBucketed(context.Context, *GetGoogleMyBusinessInsightsDataBucketedRequest) (*GetGoogleMyBusinessInsightsDataBucketedResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGoogleMyBusinessInsightsDataBucketed not implemented")
}
func (UnimplementedListingProductsServiceServer) UpdateVendorSyncFlag(context.Context, *UpdateVendorSyncFlagRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateVendorSyncFlag not implemented")
}
func (UnimplementedListingProductsServiceServer) SubmitTurboLister(context.Context, *SubmitTurboListerRequest) (*SubmitTurboListerResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SubmitTurboLister not implemented")
}
func (UnimplementedListingProductsServiceServer) GetAddonAttributes(context.Context, *GetAddonAttributesRequest) (*GetAddonAttributesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAddonAttributes not implemented")
}
func (UnimplementedListingProductsServiceServer) CreateAddonAttributes(context.Context, *CreateAddonAttributesRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateAddonAttributes not implemented")
}
func (UnimplementedListingProductsServiceServer) UpdateAddonAttributes(context.Context, *UpdateAddonAttributesRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAddonAttributes not implemented")
}
func (UnimplementedListingProductsServiceServer) DeleteAddonAttributes(context.Context, *DeleteAddonAttributesRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteAddonAttributes not implemented")
}
func (UnimplementedListingProductsServiceServer) GetMultiAddonAttributes(context.Context, *GetMultiAddonAttributesRequest) (*GetMultiAddonAttributesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMultiAddonAttributes not implemented")
}
func (UnimplementedListingProductsServiceServer) BulkConnectLocationsForGoogleUser(context.Context, *BulkConnectLocationsForGoogleUserRequest) (*BulkConnectLocationsForGoogleUserResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BulkConnectLocationsForGoogleUser not implemented")
}
func (UnimplementedListingProductsServiceServer) ManuallyFixYextProvisioning(context.Context, *ManuallyFixYextProvisioningRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ManuallyFixYextProvisioning not implemented")
}
func (UnimplementedListingProductsServiceServer) ManuallyProvisionUberall(context.Context, *ManuallyProvisionUberallRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ManuallyProvisionUberall not implemented")
}
func (UnimplementedListingProductsServiceServer) ManuallyDeactivateUberallProvisioning(context.Context, *ManuallyDeactivateUberallProvisioningRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ManuallyDeactivateUberallProvisioning not implemented")
}
func (UnimplementedListingProductsServiceServer) GetGoogleBusinessInfo(context.Context, *GetGoogleBusinessInfoRequest) (*GetGoogleBusinessInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGoogleBusinessInfo not implemented")
}
func (UnimplementedListingProductsServiceServer) GetFacebookPageInfo(context.Context, *GetFacebookPageInfoRequest) (*GetFacebookPageInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFacebookPageInfo not implemented")
}
func (UnimplementedListingProductsServiceServer) GetAppleBusinessConnectInfo(context.Context, *GetAppleBusinessConnectInfoRequest) (*GetAppleBusinessConnectInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAppleBusinessConnectInfo not implemented")
}
func (UnimplementedListingProductsServiceServer) GetBingPlacesInfo(context.Context, *GetBingPlacesInfoRequest) (*GetBingPlacesInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBingPlacesInfo not implemented")
}
func (UnimplementedListingProductsServiceServer) TriggerBingInsightsBackFill(context.Context, *TriggerBingInsightsBackFillRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TriggerBingInsightsBackFill not implemented")
}
func (UnimplementedListingProductsServiceServer) CheckSubmissionStatus(context.Context, *CheckSubmissionStatusRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckSubmissionStatus not implemented")
}
func (UnimplementedListingProductsServiceServer) GetInfoGroupId(context.Context, *GetInfoGroupIdRequest) (*GetInfoGroupIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetInfoGroupId not implemented")
}
func (UnimplementedListingProductsServiceServer) GetListingDistributionActivationStatus(context.Context, *GetListingDistributionActivationStatusRequest) (*GetListingDistributionActivationStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetListingDistributionActivationStatus not implemented")
}
func (UnimplementedListingProductsServiceServer) GetDirectSyncSourceInfo(context.Context, *GetDirectSyncSourceInfoRequest) (*GetDirectSyncSourceInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDirectSyncSourceInfo not implemented")
}
func (UnimplementedListingProductsServiceServer) GetSyncData(context.Context, *GetSyncDataRequest) (*GetSyncDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSyncData not implemented")
}
func (UnimplementedListingProductsServiceServer) GetSyndicationInfo(context.Context, *GetSyndicationInfoRequest) (*GetSyndicationInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSyndicationInfo not implemented")
}
func (UnimplementedListingProductsServiceServer) GetProductSettings(context.Context, *GetProductSettingsRequest) (*GetProductSettingsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetProductSettings not implemented")
}
func (UnimplementedListingProductsServiceServer) GetDoctorDotComCategories(context.Context, *GetDoctorDotComCategoriesRequest) (*GetDoctorDotComCategoriesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDoctorDotComCategories not implemented")
}
func (UnimplementedListingProductsServiceServer) FetchAndSubmitSourceURLsToCS(context.Context, *FetchAndSubmitSourceURLsToCSRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FetchAndSubmitSourceURLsToCS not implemented")
}
func (UnimplementedListingProductsServiceServer) StartBusinessDataScoreWorkflow(context.Context, *StartBusinessDataScoreWorkflowRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartBusinessDataScoreWorkflow not implemented")
}
func (UnimplementedListingProductsServiceServer) IsPartnerUser(context.Context, *IsPartnerUserRequest) (*IsPartnerUserResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IsPartnerUser not implemented")
}
func (UnimplementedListingProductsServiceServer) IsLocalSEOProActiveForAccount(context.Context, *IsLocalSEOProActiveForAccountRequest) (*IsLocalSEOProActiveForAccountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IsLocalSEOProActiveForAccount not implemented")
}
func (UnimplementedListingProductsServiceServer) GetLDData(context.Context, *GetLDDataRequest) (*GetLDDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLDData not implemented")
}
func (UnimplementedListingProductsServiceServer) SyncToSources(context.Context, *SyncToSourcesRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SyncToSources not implemented")
}
func (UnimplementedListingProductsServiceServer) GetNeustarPublications(context.Context, *GetNeustarPublicationsRequest) (*GetNeustarPublicationsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNeustarPublications not implemented")
}
func (UnimplementedListingProductsServiceServer) testEmbeddedByValue() {}

// UnsafeListingProductsServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ListingProductsServiceServer will
// result in compilation errors.
type UnsafeListingProductsServiceServer interface {
	mustEmbedUnimplementedListingProductsServiceServer()
}

func RegisterListingProductsServiceServer(s grpc.ServiceRegistrar, srv ListingProductsServiceServer) {
	// If the following call pancis, it indicates UnimplementedListingProductsServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&ListingProductsService_ServiceDesc, srv)
}

func _ListingProductsService_GetGoogleMyBusinessInsightsData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGoogleMyBusinessInsightsDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ListingProductsServiceServer).GetGoogleMyBusinessInsightsData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ListingProductsService_GetGoogleMyBusinessInsightsData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ListingProductsServiceServer).GetGoogleMyBusinessInsightsData(ctx, req.(*GetGoogleMyBusinessInsightsDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ListingProductsService_GetGoogleMyBusinessInsightsDataBucketed_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGoogleMyBusinessInsightsDataBucketedRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ListingProductsServiceServer).GetGoogleMyBusinessInsightsDataBucketed(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ListingProductsService_GetGoogleMyBusinessInsightsDataBucketed_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ListingProductsServiceServer).GetGoogleMyBusinessInsightsDataBucketed(ctx, req.(*GetGoogleMyBusinessInsightsDataBucketedRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ListingProductsService_UpdateVendorSyncFlag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateVendorSyncFlagRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ListingProductsServiceServer).UpdateVendorSyncFlag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ListingProductsService_UpdateVendorSyncFlag_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ListingProductsServiceServer).UpdateVendorSyncFlag(ctx, req.(*UpdateVendorSyncFlagRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ListingProductsService_SubmitTurboLister_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SubmitTurboListerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ListingProductsServiceServer).SubmitTurboLister(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ListingProductsService_SubmitTurboLister_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ListingProductsServiceServer).SubmitTurboLister(ctx, req.(*SubmitTurboListerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ListingProductsService_GetAddonAttributes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAddonAttributesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ListingProductsServiceServer).GetAddonAttributes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ListingProductsService_GetAddonAttributes_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ListingProductsServiceServer).GetAddonAttributes(ctx, req.(*GetAddonAttributesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ListingProductsService_CreateAddonAttributes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateAddonAttributesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ListingProductsServiceServer).CreateAddonAttributes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ListingProductsService_CreateAddonAttributes_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ListingProductsServiceServer).CreateAddonAttributes(ctx, req.(*CreateAddonAttributesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ListingProductsService_UpdateAddonAttributes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAddonAttributesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ListingProductsServiceServer).UpdateAddonAttributes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ListingProductsService_UpdateAddonAttributes_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ListingProductsServiceServer).UpdateAddonAttributes(ctx, req.(*UpdateAddonAttributesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ListingProductsService_DeleteAddonAttributes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteAddonAttributesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ListingProductsServiceServer).DeleteAddonAttributes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ListingProductsService_DeleteAddonAttributes_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ListingProductsServiceServer).DeleteAddonAttributes(ctx, req.(*DeleteAddonAttributesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ListingProductsService_GetMultiAddonAttributes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMultiAddonAttributesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ListingProductsServiceServer).GetMultiAddonAttributes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ListingProductsService_GetMultiAddonAttributes_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ListingProductsServiceServer).GetMultiAddonAttributes(ctx, req.(*GetMultiAddonAttributesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ListingProductsService_BulkConnectLocationsForGoogleUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BulkConnectLocationsForGoogleUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ListingProductsServiceServer).BulkConnectLocationsForGoogleUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ListingProductsService_BulkConnectLocationsForGoogleUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ListingProductsServiceServer).BulkConnectLocationsForGoogleUser(ctx, req.(*BulkConnectLocationsForGoogleUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ListingProductsService_ManuallyFixYextProvisioning_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ManuallyFixYextProvisioningRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ListingProductsServiceServer).ManuallyFixYextProvisioning(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ListingProductsService_ManuallyFixYextProvisioning_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ListingProductsServiceServer).ManuallyFixYextProvisioning(ctx, req.(*ManuallyFixYextProvisioningRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ListingProductsService_ManuallyProvisionUberall_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ManuallyProvisionUberallRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ListingProductsServiceServer).ManuallyProvisionUberall(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ListingProductsService_ManuallyProvisionUberall_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ListingProductsServiceServer).ManuallyProvisionUberall(ctx, req.(*ManuallyProvisionUberallRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ListingProductsService_ManuallyDeactivateUberallProvisioning_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ManuallyDeactivateUberallProvisioningRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ListingProductsServiceServer).ManuallyDeactivateUberallProvisioning(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ListingProductsService_ManuallyDeactivateUberallProvisioning_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ListingProductsServiceServer).ManuallyDeactivateUberallProvisioning(ctx, req.(*ManuallyDeactivateUberallProvisioningRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ListingProductsService_GetGoogleBusinessInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGoogleBusinessInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ListingProductsServiceServer).GetGoogleBusinessInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ListingProductsService_GetGoogleBusinessInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ListingProductsServiceServer).GetGoogleBusinessInfo(ctx, req.(*GetGoogleBusinessInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ListingProductsService_GetFacebookPageInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFacebookPageInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ListingProductsServiceServer).GetFacebookPageInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ListingProductsService_GetFacebookPageInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ListingProductsServiceServer).GetFacebookPageInfo(ctx, req.(*GetFacebookPageInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ListingProductsService_GetAppleBusinessConnectInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAppleBusinessConnectInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ListingProductsServiceServer).GetAppleBusinessConnectInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ListingProductsService_GetAppleBusinessConnectInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ListingProductsServiceServer).GetAppleBusinessConnectInfo(ctx, req.(*GetAppleBusinessConnectInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ListingProductsService_GetBingPlacesInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBingPlacesInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ListingProductsServiceServer).GetBingPlacesInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ListingProductsService_GetBingPlacesInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ListingProductsServiceServer).GetBingPlacesInfo(ctx, req.(*GetBingPlacesInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ListingProductsService_TriggerBingInsightsBackFill_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TriggerBingInsightsBackFillRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ListingProductsServiceServer).TriggerBingInsightsBackFill(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ListingProductsService_TriggerBingInsightsBackFill_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ListingProductsServiceServer).TriggerBingInsightsBackFill(ctx, req.(*TriggerBingInsightsBackFillRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ListingProductsService_CheckSubmissionStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckSubmissionStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ListingProductsServiceServer).CheckSubmissionStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ListingProductsService_CheckSubmissionStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ListingProductsServiceServer).CheckSubmissionStatus(ctx, req.(*CheckSubmissionStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ListingProductsService_GetInfoGroupId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetInfoGroupIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ListingProductsServiceServer).GetInfoGroupId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ListingProductsService_GetInfoGroupId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ListingProductsServiceServer).GetInfoGroupId(ctx, req.(*GetInfoGroupIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ListingProductsService_GetListingDistributionActivationStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetListingDistributionActivationStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ListingProductsServiceServer).GetListingDistributionActivationStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ListingProductsService_GetListingDistributionActivationStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ListingProductsServiceServer).GetListingDistributionActivationStatus(ctx, req.(*GetListingDistributionActivationStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ListingProductsService_GetDirectSyncSourceInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDirectSyncSourceInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ListingProductsServiceServer).GetDirectSyncSourceInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ListingProductsService_GetDirectSyncSourceInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ListingProductsServiceServer).GetDirectSyncSourceInfo(ctx, req.(*GetDirectSyncSourceInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ListingProductsService_GetSyncData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSyncDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ListingProductsServiceServer).GetSyncData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ListingProductsService_GetSyncData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ListingProductsServiceServer).GetSyncData(ctx, req.(*GetSyncDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ListingProductsService_GetSyndicationInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSyndicationInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ListingProductsServiceServer).GetSyndicationInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ListingProductsService_GetSyndicationInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ListingProductsServiceServer).GetSyndicationInfo(ctx, req.(*GetSyndicationInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ListingProductsService_GetProductSettings_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetProductSettingsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ListingProductsServiceServer).GetProductSettings(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ListingProductsService_GetProductSettings_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ListingProductsServiceServer).GetProductSettings(ctx, req.(*GetProductSettingsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ListingProductsService_GetDoctorDotComCategories_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDoctorDotComCategoriesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ListingProductsServiceServer).GetDoctorDotComCategories(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ListingProductsService_GetDoctorDotComCategories_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ListingProductsServiceServer).GetDoctorDotComCategories(ctx, req.(*GetDoctorDotComCategoriesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ListingProductsService_FetchAndSubmitSourceURLsToCS_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FetchAndSubmitSourceURLsToCSRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ListingProductsServiceServer).FetchAndSubmitSourceURLsToCS(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ListingProductsService_FetchAndSubmitSourceURLsToCS_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ListingProductsServiceServer).FetchAndSubmitSourceURLsToCS(ctx, req.(*FetchAndSubmitSourceURLsToCSRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ListingProductsService_StartBusinessDataScoreWorkflow_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StartBusinessDataScoreWorkflowRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ListingProductsServiceServer).StartBusinessDataScoreWorkflow(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ListingProductsService_StartBusinessDataScoreWorkflow_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ListingProductsServiceServer).StartBusinessDataScoreWorkflow(ctx, req.(*StartBusinessDataScoreWorkflowRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ListingProductsService_IsPartnerUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IsPartnerUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ListingProductsServiceServer).IsPartnerUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ListingProductsService_IsPartnerUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ListingProductsServiceServer).IsPartnerUser(ctx, req.(*IsPartnerUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ListingProductsService_IsLocalSEOProActiveForAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IsLocalSEOProActiveForAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ListingProductsServiceServer).IsLocalSEOProActiveForAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ListingProductsService_IsLocalSEOProActiveForAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ListingProductsServiceServer).IsLocalSEOProActiveForAccount(ctx, req.(*IsLocalSEOProActiveForAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ListingProductsService_GetLDData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLDDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ListingProductsServiceServer).GetLDData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ListingProductsService_GetLDData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ListingProductsServiceServer).GetLDData(ctx, req.(*GetLDDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ListingProductsService_SyncToSources_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SyncToSourcesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ListingProductsServiceServer).SyncToSources(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ListingProductsService_SyncToSources_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ListingProductsServiceServer).SyncToSources(ctx, req.(*SyncToSourcesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ListingProductsService_GetNeustarPublications_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNeustarPublicationsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ListingProductsServiceServer).GetNeustarPublications(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ListingProductsService_GetNeustarPublications_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ListingProductsServiceServer).GetNeustarPublications(ctx, req.(*GetNeustarPublicationsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ListingProductsService_ServiceDesc is the grpc.ServiceDesc for ListingProductsService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ListingProductsService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "listing_products.v1.ListingProductsService",
	HandlerType: (*ListingProductsServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetGoogleMyBusinessInsightsData",
			Handler:    _ListingProductsService_GetGoogleMyBusinessInsightsData_Handler,
		},
		{
			MethodName: "GetGoogleMyBusinessInsightsDataBucketed",
			Handler:    _ListingProductsService_GetGoogleMyBusinessInsightsDataBucketed_Handler,
		},
		{
			MethodName: "UpdateVendorSyncFlag",
			Handler:    _ListingProductsService_UpdateVendorSyncFlag_Handler,
		},
		{
			MethodName: "SubmitTurboLister",
			Handler:    _ListingProductsService_SubmitTurboLister_Handler,
		},
		{
			MethodName: "GetAddonAttributes",
			Handler:    _ListingProductsService_GetAddonAttributes_Handler,
		},
		{
			MethodName: "CreateAddonAttributes",
			Handler:    _ListingProductsService_CreateAddonAttributes_Handler,
		},
		{
			MethodName: "UpdateAddonAttributes",
			Handler:    _ListingProductsService_UpdateAddonAttributes_Handler,
		},
		{
			MethodName: "DeleteAddonAttributes",
			Handler:    _ListingProductsService_DeleteAddonAttributes_Handler,
		},
		{
			MethodName: "GetMultiAddonAttributes",
			Handler:    _ListingProductsService_GetMultiAddonAttributes_Handler,
		},
		{
			MethodName: "BulkConnectLocationsForGoogleUser",
			Handler:    _ListingProductsService_BulkConnectLocationsForGoogleUser_Handler,
		},
		{
			MethodName: "ManuallyFixYextProvisioning",
			Handler:    _ListingProductsService_ManuallyFixYextProvisioning_Handler,
		},
		{
			MethodName: "ManuallyProvisionUberall",
			Handler:    _ListingProductsService_ManuallyProvisionUberall_Handler,
		},
		{
			MethodName: "ManuallyDeactivateUberallProvisioning",
			Handler:    _ListingProductsService_ManuallyDeactivateUberallProvisioning_Handler,
		},
		{
			MethodName: "GetGoogleBusinessInfo",
			Handler:    _ListingProductsService_GetGoogleBusinessInfo_Handler,
		},
		{
			MethodName: "GetFacebookPageInfo",
			Handler:    _ListingProductsService_GetFacebookPageInfo_Handler,
		},
		{
			MethodName: "GetAppleBusinessConnectInfo",
			Handler:    _ListingProductsService_GetAppleBusinessConnectInfo_Handler,
		},
		{
			MethodName: "GetBingPlacesInfo",
			Handler:    _ListingProductsService_GetBingPlacesInfo_Handler,
		},
		{
			MethodName: "TriggerBingInsightsBackFill",
			Handler:    _ListingProductsService_TriggerBingInsightsBackFill_Handler,
		},
		{
			MethodName: "CheckSubmissionStatus",
			Handler:    _ListingProductsService_CheckSubmissionStatus_Handler,
		},
		{
			MethodName: "GetInfoGroupId",
			Handler:    _ListingProductsService_GetInfoGroupId_Handler,
		},
		{
			MethodName: "GetListingDistributionActivationStatus",
			Handler:    _ListingProductsService_GetListingDistributionActivationStatus_Handler,
		},
		{
			MethodName: "GetDirectSyncSourceInfo",
			Handler:    _ListingProductsService_GetDirectSyncSourceInfo_Handler,
		},
		{
			MethodName: "GetSyncData",
			Handler:    _ListingProductsService_GetSyncData_Handler,
		},
		{
			MethodName: "GetSyndicationInfo",
			Handler:    _ListingProductsService_GetSyndicationInfo_Handler,
		},
		{
			MethodName: "GetProductSettings",
			Handler:    _ListingProductsService_GetProductSettings_Handler,
		},
		{
			MethodName: "GetDoctorDotComCategories",
			Handler:    _ListingProductsService_GetDoctorDotComCategories_Handler,
		},
		{
			MethodName: "FetchAndSubmitSourceURLsToCS",
			Handler:    _ListingProductsService_FetchAndSubmitSourceURLsToCS_Handler,
		},
		{
			MethodName: "StartBusinessDataScoreWorkflow",
			Handler:    _ListingProductsService_StartBusinessDataScoreWorkflow_Handler,
		},
		{
			MethodName: "IsPartnerUser",
			Handler:    _ListingProductsService_IsPartnerUser_Handler,
		},
		{
			MethodName: "IsLocalSEOProActiveForAccount",
			Handler:    _ListingProductsService_IsLocalSEOProActiveForAccount_Handler,
		},
		{
			MethodName: "GetLDData",
			Handler:    _ListingProductsService_GetLDData_Handler,
		},
		{
			MethodName: "SyncToSources",
			Handler:    _ListingProductsService_SyncToSources_Handler,
		},
		{
			MethodName: "GetNeustarPublications",
			Handler:    _ListingProductsService_GetNeustarPublications_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "listing_products/v1/api.proto",
}

const (
	ListingSourceService_GetListingSources_FullMethodName            = "/listing_products.v1.ListingSourceService/GetListingSources"
	ListingSourceService_GetListingSourceById_FullMethodName         = "/listing_products.v1.ListingSourceService/GetListingSourceById"
	ListingSourceService_GetListingSourceByProviderId_FullMethodName = "/listing_products.v1.ListingSourceService/GetListingSourceByProviderId"
	ListingSourceService_GetPartnerSources_FullMethodName            = "/listing_products.v1.ListingSourceService/GetPartnerSources"
	ListingSourceService_CreateListingSource_FullMethodName          = "/listing_products.v1.ListingSourceService/CreateListingSource"
	ListingSourceService_DeleteListingSource_FullMethodName          = "/listing_products.v1.ListingSourceService/DeleteListingSource"
	ListingSourceService_UndeleteListingSource_FullMethodName        = "/listing_products.v1.ListingSourceService/UndeleteListingSource"
	ListingSourceService_UpdateListingSource_FullMethodName          = "/listing_products.v1.ListingSourceService/UpdateListingSource"
)

// ListingSourceServiceClient is the client API for ListingSourceService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ListingSourceServiceClient interface {
	// GetListingSources fetches all listing sources, optionally filtered by provider
	GetListingSources(ctx context.Context, in *GetListingSourcesRequest, opts ...grpc.CallOption) (*GetListingSourcesResponse, error)
	// GetListingSourceById fetches details for a source based on its numeric ID
	GetListingSourceById(ctx context.Context, in *GetListingSourceByIdRequest, opts ...grpc.CallOption) (*GetListingSourceByIdResponse, error)
	// GetListingSourceByProviderId fetches details for a source based on its provider ID
	GetListingSourceByProviderId(ctx context.Context, in *GetListingSourceByProviderIdRequest, opts ...grpc.CallOption) (*GetListingSourceByProviderIdResponse, error)
	// GetPartnerSources gets all sources that have not been disabled by the partner
	GetPartnerSources(ctx context.Context, in *GetPartnerSourcesRequest, opts ...grpc.CallOption) (*GetPartnerSourcesResponse, error)
	// CreateListingSource creates a new source
	CreateListingSource(ctx context.Context, in *CreateListingSourceRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// DeleteListingSource sets a source to deleted
	DeleteListingSource(ctx context.Context, in *DeleteListingSourceRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// UndeleteListingSource restores a deleted source
	UndeleteListingSource(ctx context.Context, in *UndeleteListingSourceRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// UpdateListingSource updates fields of a source
	UpdateListingSource(ctx context.Context, in *UpdateListingSourceRequest, opts ...grpc.CallOption) (*UpdateListingSourceResponse, error)
}

type listingSourceServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewListingSourceServiceClient(cc grpc.ClientConnInterface) ListingSourceServiceClient {
	return &listingSourceServiceClient{cc}
}

func (c *listingSourceServiceClient) GetListingSources(ctx context.Context, in *GetListingSourcesRequest, opts ...grpc.CallOption) (*GetListingSourcesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetListingSourcesResponse)
	err := c.cc.Invoke(ctx, ListingSourceService_GetListingSources_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *listingSourceServiceClient) GetListingSourceById(ctx context.Context, in *GetListingSourceByIdRequest, opts ...grpc.CallOption) (*GetListingSourceByIdResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetListingSourceByIdResponse)
	err := c.cc.Invoke(ctx, ListingSourceService_GetListingSourceById_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *listingSourceServiceClient) GetListingSourceByProviderId(ctx context.Context, in *GetListingSourceByProviderIdRequest, opts ...grpc.CallOption) (*GetListingSourceByProviderIdResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetListingSourceByProviderIdResponse)
	err := c.cc.Invoke(ctx, ListingSourceService_GetListingSourceByProviderId_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *listingSourceServiceClient) GetPartnerSources(ctx context.Context, in *GetPartnerSourcesRequest, opts ...grpc.CallOption) (*GetPartnerSourcesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetPartnerSourcesResponse)
	err := c.cc.Invoke(ctx, ListingSourceService_GetPartnerSources_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *listingSourceServiceClient) CreateListingSource(ctx context.Context, in *CreateListingSourceRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, ListingSourceService_CreateListingSource_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *listingSourceServiceClient) DeleteListingSource(ctx context.Context, in *DeleteListingSourceRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, ListingSourceService_DeleteListingSource_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *listingSourceServiceClient) UndeleteListingSource(ctx context.Context, in *UndeleteListingSourceRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, ListingSourceService_UndeleteListingSource_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *listingSourceServiceClient) UpdateListingSource(ctx context.Context, in *UpdateListingSourceRequest, opts ...grpc.CallOption) (*UpdateListingSourceResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateListingSourceResponse)
	err := c.cc.Invoke(ctx, ListingSourceService_UpdateListingSource_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ListingSourceServiceServer is the server API for ListingSourceService service.
// All implementations should embed UnimplementedListingSourceServiceServer
// for forward compatibility.
type ListingSourceServiceServer interface {
	// GetListingSources fetches all listing sources, optionally filtered by provider
	GetListingSources(context.Context, *GetListingSourcesRequest) (*GetListingSourcesResponse, error)
	// GetListingSourceById fetches details for a source based on its numeric ID
	GetListingSourceById(context.Context, *GetListingSourceByIdRequest) (*GetListingSourceByIdResponse, error)
	// GetListingSourceByProviderId fetches details for a source based on its provider ID
	GetListingSourceByProviderId(context.Context, *GetListingSourceByProviderIdRequest) (*GetListingSourceByProviderIdResponse, error)
	// GetPartnerSources gets all sources that have not been disabled by the partner
	GetPartnerSources(context.Context, *GetPartnerSourcesRequest) (*GetPartnerSourcesResponse, error)
	// CreateListingSource creates a new source
	CreateListingSource(context.Context, *CreateListingSourceRequest) (*emptypb.Empty, error)
	// DeleteListingSource sets a source to deleted
	DeleteListingSource(context.Context, *DeleteListingSourceRequest) (*emptypb.Empty, error)
	// UndeleteListingSource restores a deleted source
	UndeleteListingSource(context.Context, *UndeleteListingSourceRequest) (*emptypb.Empty, error)
	// UpdateListingSource updates fields of a source
	UpdateListingSource(context.Context, *UpdateListingSourceRequest) (*UpdateListingSourceResponse, error)
}

// UnimplementedListingSourceServiceServer should be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedListingSourceServiceServer struct{}

func (UnimplementedListingSourceServiceServer) GetListingSources(context.Context, *GetListingSourcesRequest) (*GetListingSourcesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetListingSources not implemented")
}
func (UnimplementedListingSourceServiceServer) GetListingSourceById(context.Context, *GetListingSourceByIdRequest) (*GetListingSourceByIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetListingSourceById not implemented")
}
func (UnimplementedListingSourceServiceServer) GetListingSourceByProviderId(context.Context, *GetListingSourceByProviderIdRequest) (*GetListingSourceByProviderIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetListingSourceByProviderId not implemented")
}
func (UnimplementedListingSourceServiceServer) GetPartnerSources(context.Context, *GetPartnerSourcesRequest) (*GetPartnerSourcesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPartnerSources not implemented")
}
func (UnimplementedListingSourceServiceServer) CreateListingSource(context.Context, *CreateListingSourceRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateListingSource not implemented")
}
func (UnimplementedListingSourceServiceServer) DeleteListingSource(context.Context, *DeleteListingSourceRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteListingSource not implemented")
}
func (UnimplementedListingSourceServiceServer) UndeleteListingSource(context.Context, *UndeleteListingSourceRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UndeleteListingSource not implemented")
}
func (UnimplementedListingSourceServiceServer) UpdateListingSource(context.Context, *UpdateListingSourceRequest) (*UpdateListingSourceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateListingSource not implemented")
}
func (UnimplementedListingSourceServiceServer) testEmbeddedByValue() {}

// UnsafeListingSourceServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ListingSourceServiceServer will
// result in compilation errors.
type UnsafeListingSourceServiceServer interface {
	mustEmbedUnimplementedListingSourceServiceServer()
}

func RegisterListingSourceServiceServer(s grpc.ServiceRegistrar, srv ListingSourceServiceServer) {
	// If the following call pancis, it indicates UnimplementedListingSourceServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&ListingSourceService_ServiceDesc, srv)
}

func _ListingSourceService_GetListingSources_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetListingSourcesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ListingSourceServiceServer).GetListingSources(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ListingSourceService_GetListingSources_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ListingSourceServiceServer).GetListingSources(ctx, req.(*GetListingSourcesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ListingSourceService_GetListingSourceById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetListingSourceByIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ListingSourceServiceServer).GetListingSourceById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ListingSourceService_GetListingSourceById_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ListingSourceServiceServer).GetListingSourceById(ctx, req.(*GetListingSourceByIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ListingSourceService_GetListingSourceByProviderId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetListingSourceByProviderIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ListingSourceServiceServer).GetListingSourceByProviderId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ListingSourceService_GetListingSourceByProviderId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ListingSourceServiceServer).GetListingSourceByProviderId(ctx, req.(*GetListingSourceByProviderIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ListingSourceService_GetPartnerSources_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPartnerSourcesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ListingSourceServiceServer).GetPartnerSources(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ListingSourceService_GetPartnerSources_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ListingSourceServiceServer).GetPartnerSources(ctx, req.(*GetPartnerSourcesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ListingSourceService_CreateListingSource_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateListingSourceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ListingSourceServiceServer).CreateListingSource(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ListingSourceService_CreateListingSource_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ListingSourceServiceServer).CreateListingSource(ctx, req.(*CreateListingSourceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ListingSourceService_DeleteListingSource_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteListingSourceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ListingSourceServiceServer).DeleteListingSource(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ListingSourceService_DeleteListingSource_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ListingSourceServiceServer).DeleteListingSource(ctx, req.(*DeleteListingSourceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ListingSourceService_UndeleteListingSource_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UndeleteListingSourceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ListingSourceServiceServer).UndeleteListingSource(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ListingSourceService_UndeleteListingSource_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ListingSourceServiceServer).UndeleteListingSource(ctx, req.(*UndeleteListingSourceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ListingSourceService_UpdateListingSource_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateListingSourceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ListingSourceServiceServer).UpdateListingSource(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ListingSourceService_UpdateListingSource_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ListingSourceServiceServer).UpdateListingSource(ctx, req.(*UpdateListingSourceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ListingSourceService_ServiceDesc is the grpc.ServiceDesc for ListingSourceService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ListingSourceService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "listing_products.v1.ListingSourceService",
	HandlerType: (*ListingSourceServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetListingSources",
			Handler:    _ListingSourceService_GetListingSources_Handler,
		},
		{
			MethodName: "GetListingSourceById",
			Handler:    _ListingSourceService_GetListingSourceById_Handler,
		},
		{
			MethodName: "GetListingSourceByProviderId",
			Handler:    _ListingSourceService_GetListingSourceByProviderId_Handler,
		},
		{
			MethodName: "GetPartnerSources",
			Handler:    _ListingSourceService_GetPartnerSources_Handler,
		},
		{
			MethodName: "CreateListingSource",
			Handler:    _ListingSourceService_CreateListingSource_Handler,
		},
		{
			MethodName: "DeleteListingSource",
			Handler:    _ListingSourceService_DeleteListingSource_Handler,
		},
		{
			MethodName: "UndeleteListingSource",
			Handler:    _ListingSourceService_UndeleteListingSource_Handler,
		},
		{
			MethodName: "UpdateListingSource",
			Handler:    _ListingSourceService_UpdateListingSource_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "listing_products/v1/api.proto",
}

const (
	ListingProfileService_Create_FullMethodName                        = "/listing_products.v1.ListingProfileService/Create"
	ListingProfileService_GetMulti_FullMethodName                      = "/listing_products.v1.ListingProfileService/GetMulti"
	ListingProfileService_Update_FullMethodName                        = "/listing_products.v1.ListingProfileService/Update"
	ListingProfileService_Delete_FullMethodName                        = "/listing_products.v1.ListingProfileService/Delete"
	ListingProfileService_GetMoreHoursTypes_FullMethodName             = "/listing_products.v1.ListingProfileService/GetMoreHoursTypes"
	ListingProfileService_GetAttributeMetadata_FullMethodName          = "/listing_products.v1.ListingProfileService/GetAttributeMetadata"
	ListingProfileService_GetDoctorDotComCategories_FullMethodName     = "/listing_products.v1.ListingProfileService/GetDoctorDotComCategories"
	ListingProfileService_LegacyAPICreate_FullMethodName               = "/listing_products.v1.ListingProfileService/LegacyAPICreate"
	ListingProfileService_LegacyAPIUpdate_FullMethodName               = "/listing_products.v1.ListingProfileService/LegacyAPIUpdate"
	ListingProfileService_GetBusinessProfileFieldStatus_FullMethodName = "/listing_products.v1.ListingProfileService/GetBusinessProfileFieldStatus"
	ListingProfileService_GetMultiAccountGroup_FullMethodName          = "/listing_products.v1.ListingProfileService/GetMultiAccountGroup"
)

// ListingProfileServiceClient is the client API for ListingProfileService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// Listing Profile Service
type ListingProfileServiceClient interface {
	Create(ctx context.Context, in *CreateListingProfileRequest, opts ...grpc.CallOption) (*CreateListingProfileResponse, error)
	GetMulti(ctx context.Context, in *GetMultiListingProfileRequest, opts ...grpc.CallOption) (*GetMultiListingProfileResponse, error)
	Update(ctx context.Context, in *UpdateListingProfileRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	Delete(ctx context.Context, in *DeleteListingProfileRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	GetMoreHoursTypes(ctx context.Context, in *GetMoreHoursTypesRequest, opts ...grpc.CallOption) (*GetMoreHoursTypesResponse, error)
	GetAttributeMetadata(ctx context.Context, in *GetAttributeMetadataRequest, opts ...grpc.CallOption) (*GetAttributeMetadataResponse, error)
	GetDoctorDotComCategories(ctx context.Context, in *GetDoctorDotComCategoriesRequest, opts ...grpc.CallOption) (*GetDoctorDotComCategoriesResponse, error)
	LegacyAPICreate(ctx context.Context, in *LegacyAPICreateRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	LegacyAPIUpdate(ctx context.Context, in *LegacyAPIUpdateRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	GetBusinessProfileFieldStatus(ctx context.Context, in *GetBusinessProfileFieldStatusRequest, opts ...grpc.CallOption) (*GetBusinessProfileFieldStatusResponse, error)
	GetMultiAccountGroup(ctx context.Context, in *GetMultiAccountGroupRequest, opts ...grpc.CallOption) (*GetMultiAccountGroupResponse, error)
}

type listingProfileServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewListingProfileServiceClient(cc grpc.ClientConnInterface) ListingProfileServiceClient {
	return &listingProfileServiceClient{cc}
}

func (c *listingProfileServiceClient) Create(ctx context.Context, in *CreateListingProfileRequest, opts ...grpc.CallOption) (*CreateListingProfileResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateListingProfileResponse)
	err := c.cc.Invoke(ctx, ListingProfileService_Create_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *listingProfileServiceClient) GetMulti(ctx context.Context, in *GetMultiListingProfileRequest, opts ...grpc.CallOption) (*GetMultiListingProfileResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetMultiListingProfileResponse)
	err := c.cc.Invoke(ctx, ListingProfileService_GetMulti_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *listingProfileServiceClient) Update(ctx context.Context, in *UpdateListingProfileRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, ListingProfileService_Update_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *listingProfileServiceClient) Delete(ctx context.Context, in *DeleteListingProfileRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, ListingProfileService_Delete_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *listingProfileServiceClient) GetMoreHoursTypes(ctx context.Context, in *GetMoreHoursTypesRequest, opts ...grpc.CallOption) (*GetMoreHoursTypesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetMoreHoursTypesResponse)
	err := c.cc.Invoke(ctx, ListingProfileService_GetMoreHoursTypes_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *listingProfileServiceClient) GetAttributeMetadata(ctx context.Context, in *GetAttributeMetadataRequest, opts ...grpc.CallOption) (*GetAttributeMetadataResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAttributeMetadataResponse)
	err := c.cc.Invoke(ctx, ListingProfileService_GetAttributeMetadata_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *listingProfileServiceClient) GetDoctorDotComCategories(ctx context.Context, in *GetDoctorDotComCategoriesRequest, opts ...grpc.CallOption) (*GetDoctorDotComCategoriesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetDoctorDotComCategoriesResponse)
	err := c.cc.Invoke(ctx, ListingProfileService_GetDoctorDotComCategories_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *listingProfileServiceClient) LegacyAPICreate(ctx context.Context, in *LegacyAPICreateRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, ListingProfileService_LegacyAPICreate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *listingProfileServiceClient) LegacyAPIUpdate(ctx context.Context, in *LegacyAPIUpdateRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, ListingProfileService_LegacyAPIUpdate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *listingProfileServiceClient) GetBusinessProfileFieldStatus(ctx context.Context, in *GetBusinessProfileFieldStatusRequest, opts ...grpc.CallOption) (*GetBusinessProfileFieldStatusResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetBusinessProfileFieldStatusResponse)
	err := c.cc.Invoke(ctx, ListingProfileService_GetBusinessProfileFieldStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *listingProfileServiceClient) GetMultiAccountGroup(ctx context.Context, in *GetMultiAccountGroupRequest, opts ...grpc.CallOption) (*GetMultiAccountGroupResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetMultiAccountGroupResponse)
	err := c.cc.Invoke(ctx, ListingProfileService_GetMultiAccountGroup_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ListingProfileServiceServer is the server API for ListingProfileService service.
// All implementations should embed UnimplementedListingProfileServiceServer
// for forward compatibility.
//
// Listing Profile Service
type ListingProfileServiceServer interface {
	Create(context.Context, *CreateListingProfileRequest) (*CreateListingProfileResponse, error)
	GetMulti(context.Context, *GetMultiListingProfileRequest) (*GetMultiListingProfileResponse, error)
	Update(context.Context, *UpdateListingProfileRequest) (*emptypb.Empty, error)
	Delete(context.Context, *DeleteListingProfileRequest) (*emptypb.Empty, error)
	GetMoreHoursTypes(context.Context, *GetMoreHoursTypesRequest) (*GetMoreHoursTypesResponse, error)
	GetAttributeMetadata(context.Context, *GetAttributeMetadataRequest) (*GetAttributeMetadataResponse, error)
	GetDoctorDotComCategories(context.Context, *GetDoctorDotComCategoriesRequest) (*GetDoctorDotComCategoriesResponse, error)
	LegacyAPICreate(context.Context, *LegacyAPICreateRequest) (*emptypb.Empty, error)
	LegacyAPIUpdate(context.Context, *LegacyAPIUpdateRequest) (*emptypb.Empty, error)
	GetBusinessProfileFieldStatus(context.Context, *GetBusinessProfileFieldStatusRequest) (*GetBusinessProfileFieldStatusResponse, error)
	GetMultiAccountGroup(context.Context, *GetMultiAccountGroupRequest) (*GetMultiAccountGroupResponse, error)
}

// UnimplementedListingProfileServiceServer should be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedListingProfileServiceServer struct{}

func (UnimplementedListingProfileServiceServer) Create(context.Context, *CreateListingProfileRequest) (*CreateListingProfileResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Create not implemented")
}
func (UnimplementedListingProfileServiceServer) GetMulti(context.Context, *GetMultiListingProfileRequest) (*GetMultiListingProfileResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMulti not implemented")
}
func (UnimplementedListingProfileServiceServer) Update(context.Context, *UpdateListingProfileRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Update not implemented")
}
func (UnimplementedListingProfileServiceServer) Delete(context.Context, *DeleteListingProfileRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Delete not implemented")
}
func (UnimplementedListingProfileServiceServer) GetMoreHoursTypes(context.Context, *GetMoreHoursTypesRequest) (*GetMoreHoursTypesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMoreHoursTypes not implemented")
}
func (UnimplementedListingProfileServiceServer) GetAttributeMetadata(context.Context, *GetAttributeMetadataRequest) (*GetAttributeMetadataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAttributeMetadata not implemented")
}
func (UnimplementedListingProfileServiceServer) GetDoctorDotComCategories(context.Context, *GetDoctorDotComCategoriesRequest) (*GetDoctorDotComCategoriesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDoctorDotComCategories not implemented")
}
func (UnimplementedListingProfileServiceServer) LegacyAPICreate(context.Context, *LegacyAPICreateRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LegacyAPICreate not implemented")
}
func (UnimplementedListingProfileServiceServer) LegacyAPIUpdate(context.Context, *LegacyAPIUpdateRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LegacyAPIUpdate not implemented")
}
func (UnimplementedListingProfileServiceServer) GetBusinessProfileFieldStatus(context.Context, *GetBusinessProfileFieldStatusRequest) (*GetBusinessProfileFieldStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBusinessProfileFieldStatus not implemented")
}
func (UnimplementedListingProfileServiceServer) GetMultiAccountGroup(context.Context, *GetMultiAccountGroupRequest) (*GetMultiAccountGroupResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMultiAccountGroup not implemented")
}
func (UnimplementedListingProfileServiceServer) testEmbeddedByValue() {}

// UnsafeListingProfileServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ListingProfileServiceServer will
// result in compilation errors.
type UnsafeListingProfileServiceServer interface {
	mustEmbedUnimplementedListingProfileServiceServer()
}

func RegisterListingProfileServiceServer(s grpc.ServiceRegistrar, srv ListingProfileServiceServer) {
	// If the following call pancis, it indicates UnimplementedListingProfileServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&ListingProfileService_ServiceDesc, srv)
}

func _ListingProfileService_Create_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateListingProfileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ListingProfileServiceServer).Create(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ListingProfileService_Create_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ListingProfileServiceServer).Create(ctx, req.(*CreateListingProfileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ListingProfileService_GetMulti_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMultiListingProfileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ListingProfileServiceServer).GetMulti(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ListingProfileService_GetMulti_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ListingProfileServiceServer).GetMulti(ctx, req.(*GetMultiListingProfileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ListingProfileService_Update_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateListingProfileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ListingProfileServiceServer).Update(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ListingProfileService_Update_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ListingProfileServiceServer).Update(ctx, req.(*UpdateListingProfileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ListingProfileService_Delete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteListingProfileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ListingProfileServiceServer).Delete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ListingProfileService_Delete_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ListingProfileServiceServer).Delete(ctx, req.(*DeleteListingProfileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ListingProfileService_GetMoreHoursTypes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMoreHoursTypesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ListingProfileServiceServer).GetMoreHoursTypes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ListingProfileService_GetMoreHoursTypes_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ListingProfileServiceServer).GetMoreHoursTypes(ctx, req.(*GetMoreHoursTypesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ListingProfileService_GetAttributeMetadata_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAttributeMetadataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ListingProfileServiceServer).GetAttributeMetadata(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ListingProfileService_GetAttributeMetadata_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ListingProfileServiceServer).GetAttributeMetadata(ctx, req.(*GetAttributeMetadataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ListingProfileService_GetDoctorDotComCategories_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDoctorDotComCategoriesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ListingProfileServiceServer).GetDoctorDotComCategories(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ListingProfileService_GetDoctorDotComCategories_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ListingProfileServiceServer).GetDoctorDotComCategories(ctx, req.(*GetDoctorDotComCategoriesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ListingProfileService_LegacyAPICreate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LegacyAPICreateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ListingProfileServiceServer).LegacyAPICreate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ListingProfileService_LegacyAPICreate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ListingProfileServiceServer).LegacyAPICreate(ctx, req.(*LegacyAPICreateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ListingProfileService_LegacyAPIUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LegacyAPIUpdateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ListingProfileServiceServer).LegacyAPIUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ListingProfileService_LegacyAPIUpdate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ListingProfileServiceServer).LegacyAPIUpdate(ctx, req.(*LegacyAPIUpdateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ListingProfileService_GetBusinessProfileFieldStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBusinessProfileFieldStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ListingProfileServiceServer).GetBusinessProfileFieldStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ListingProfileService_GetBusinessProfileFieldStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ListingProfileServiceServer).GetBusinessProfileFieldStatus(ctx, req.(*GetBusinessProfileFieldStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ListingProfileService_GetMultiAccountGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMultiAccountGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ListingProfileServiceServer).GetMultiAccountGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ListingProfileService_GetMultiAccountGroup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ListingProfileServiceServer).GetMultiAccountGroup(ctx, req.(*GetMultiAccountGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ListingProfileService_ServiceDesc is the grpc.ServiceDesc for ListingProfileService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ListingProfileService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "listing_products.v1.ListingProfileService",
	HandlerType: (*ListingProfileServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Create",
			Handler:    _ListingProfileService_Create_Handler,
		},
		{
			MethodName: "GetMulti",
			Handler:    _ListingProfileService_GetMulti_Handler,
		},
		{
			MethodName: "Update",
			Handler:    _ListingProfileService_Update_Handler,
		},
		{
			MethodName: "Delete",
			Handler:    _ListingProfileService_Delete_Handler,
		},
		{
			MethodName: "GetMoreHoursTypes",
			Handler:    _ListingProfileService_GetMoreHoursTypes_Handler,
		},
		{
			MethodName: "GetAttributeMetadata",
			Handler:    _ListingProfileService_GetAttributeMetadata_Handler,
		},
		{
			MethodName: "GetDoctorDotComCategories",
			Handler:    _ListingProfileService_GetDoctorDotComCategories_Handler,
		},
		{
			MethodName: "LegacyAPICreate",
			Handler:    _ListingProfileService_LegacyAPICreate_Handler,
		},
		{
			MethodName: "LegacyAPIUpdate",
			Handler:    _ListingProfileService_LegacyAPIUpdate_Handler,
		},
		{
			MethodName: "GetBusinessProfileFieldStatus",
			Handler:    _ListingProfileService_GetBusinessProfileFieldStatus_Handler,
		},
		{
			MethodName: "GetMultiAccountGroup",
			Handler:    _ListingProfileService_GetMultiAccountGroup_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "listing_products/v1/api.proto",
}

const (
	SuggestionService_Upsert_FullMethodName             = "/listing_products.v1.SuggestionService/Upsert"
	SuggestionService_GetSuggestion_FullMethodName      = "/listing_products.v1.SuggestionService/GetSuggestion"
	SuggestionService_GetMulti_FullMethodName           = "/listing_products.v1.SuggestionService/GetMulti"
	SuggestionService_Delete_FullMethodName             = "/listing_products.v1.SuggestionService/Delete"
	SuggestionService_List_FullMethodName               = "/listing_products.v1.SuggestionService/List"
	SuggestionService_SuggestFieldUpdate_FullMethodName = "/listing_products.v1.SuggestionService/SuggestFieldUpdate"
)

// SuggestionServiceClient is the client API for SuggestionService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SuggestionServiceClient interface {
	Upsert(ctx context.Context, in *UpsertSuggestionRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	GetSuggestion(ctx context.Context, in *GetSuggestionRequest, opts ...grpc.CallOption) (*GetSuggestionResponse, error)
	GetMulti(ctx context.Context, in *GetMultiSuggestionRequest, opts ...grpc.CallOption) (*GetMultiSuggestionResponse, error)
	Delete(ctx context.Context, in *DeleteSuggestionRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	List(ctx context.Context, in *ListSuggestionRequest, opts ...grpc.CallOption) (*ListSuggestionResponse, error)
	SuggestFieldUpdate(ctx context.Context, in *SuggestFieldUpdateRequest, opts ...grpc.CallOption) (*SuggestFieldUpdateResponse, error)
}

type suggestionServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewSuggestionServiceClient(cc grpc.ClientConnInterface) SuggestionServiceClient {
	return &suggestionServiceClient{cc}
}

func (c *suggestionServiceClient) Upsert(ctx context.Context, in *UpsertSuggestionRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, SuggestionService_Upsert_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *suggestionServiceClient) GetSuggestion(ctx context.Context, in *GetSuggestionRequest, opts ...grpc.CallOption) (*GetSuggestionResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetSuggestionResponse)
	err := c.cc.Invoke(ctx, SuggestionService_GetSuggestion_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *suggestionServiceClient) GetMulti(ctx context.Context, in *GetMultiSuggestionRequest, opts ...grpc.CallOption) (*GetMultiSuggestionResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetMultiSuggestionResponse)
	err := c.cc.Invoke(ctx, SuggestionService_GetMulti_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *suggestionServiceClient) Delete(ctx context.Context, in *DeleteSuggestionRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, SuggestionService_Delete_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *suggestionServiceClient) List(ctx context.Context, in *ListSuggestionRequest, opts ...grpc.CallOption) (*ListSuggestionResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListSuggestionResponse)
	err := c.cc.Invoke(ctx, SuggestionService_List_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *suggestionServiceClient) SuggestFieldUpdate(ctx context.Context, in *SuggestFieldUpdateRequest, opts ...grpc.CallOption) (*SuggestFieldUpdateResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SuggestFieldUpdateResponse)
	err := c.cc.Invoke(ctx, SuggestionService_SuggestFieldUpdate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SuggestionServiceServer is the server API for SuggestionService service.
// All implementations should embed UnimplementedSuggestionServiceServer
// for forward compatibility.
type SuggestionServiceServer interface {
	Upsert(context.Context, *UpsertSuggestionRequest) (*emptypb.Empty, error)
	GetSuggestion(context.Context, *GetSuggestionRequest) (*GetSuggestionResponse, error)
	GetMulti(context.Context, *GetMultiSuggestionRequest) (*GetMultiSuggestionResponse, error)
	Delete(context.Context, *DeleteSuggestionRequest) (*emptypb.Empty, error)
	List(context.Context, *ListSuggestionRequest) (*ListSuggestionResponse, error)
	SuggestFieldUpdate(context.Context, *SuggestFieldUpdateRequest) (*SuggestFieldUpdateResponse, error)
}

// UnimplementedSuggestionServiceServer should be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedSuggestionServiceServer struct{}

func (UnimplementedSuggestionServiceServer) Upsert(context.Context, *UpsertSuggestionRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Upsert not implemented")
}
func (UnimplementedSuggestionServiceServer) GetSuggestion(context.Context, *GetSuggestionRequest) (*GetSuggestionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSuggestion not implemented")
}
func (UnimplementedSuggestionServiceServer) GetMulti(context.Context, *GetMultiSuggestionRequest) (*GetMultiSuggestionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMulti not implemented")
}
func (UnimplementedSuggestionServiceServer) Delete(context.Context, *DeleteSuggestionRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Delete not implemented")
}
func (UnimplementedSuggestionServiceServer) List(context.Context, *ListSuggestionRequest) (*ListSuggestionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method List not implemented")
}
func (UnimplementedSuggestionServiceServer) SuggestFieldUpdate(context.Context, *SuggestFieldUpdateRequest) (*SuggestFieldUpdateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SuggestFieldUpdate not implemented")
}
func (UnimplementedSuggestionServiceServer) testEmbeddedByValue() {}

// UnsafeSuggestionServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SuggestionServiceServer will
// result in compilation errors.
type UnsafeSuggestionServiceServer interface {
	mustEmbedUnimplementedSuggestionServiceServer()
}

func RegisterSuggestionServiceServer(s grpc.ServiceRegistrar, srv SuggestionServiceServer) {
	// If the following call pancis, it indicates UnimplementedSuggestionServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&SuggestionService_ServiceDesc, srv)
}

func _SuggestionService_Upsert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpsertSuggestionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SuggestionServiceServer).Upsert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SuggestionService_Upsert_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SuggestionServiceServer).Upsert(ctx, req.(*UpsertSuggestionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SuggestionService_GetSuggestion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSuggestionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SuggestionServiceServer).GetSuggestion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SuggestionService_GetSuggestion_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SuggestionServiceServer).GetSuggestion(ctx, req.(*GetSuggestionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SuggestionService_GetMulti_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMultiSuggestionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SuggestionServiceServer).GetMulti(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SuggestionService_GetMulti_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SuggestionServiceServer).GetMulti(ctx, req.(*GetMultiSuggestionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SuggestionService_Delete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteSuggestionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SuggestionServiceServer).Delete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SuggestionService_Delete_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SuggestionServiceServer).Delete(ctx, req.(*DeleteSuggestionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SuggestionService_List_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListSuggestionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SuggestionServiceServer).List(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SuggestionService_List_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SuggestionServiceServer).List(ctx, req.(*ListSuggestionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SuggestionService_SuggestFieldUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SuggestFieldUpdateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SuggestionServiceServer).SuggestFieldUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SuggestionService_SuggestFieldUpdate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SuggestionServiceServer).SuggestFieldUpdate(ctx, req.(*SuggestFieldUpdateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// SuggestionService_ServiceDesc is the grpc.ServiceDesc for SuggestionService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var SuggestionService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "listing_products.v1.SuggestionService",
	HandlerType: (*SuggestionServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Upsert",
			Handler:    _SuggestionService_Upsert_Handler,
		},
		{
			MethodName: "GetSuggestion",
			Handler:    _SuggestionService_GetSuggestion_Handler,
		},
		{
			MethodName: "GetMulti",
			Handler:    _SuggestionService_GetMulti_Handler,
		},
		{
			MethodName: "Delete",
			Handler:    _SuggestionService_Delete_Handler,
		},
		{
			MethodName: "List",
			Handler:    _SuggestionService_List_Handler,
		},
		{
			MethodName: "SuggestFieldUpdate",
			Handler:    _SuggestionService_SuggestFieldUpdate_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "listing_products/v1/api.proto",
}

const (
	SEOService_GetSEOData_FullMethodName                 = "/listing_products.v1.SEOService/GetSEOData"
	SEOService_GetAllAIOAudit_FullMethodName             = "/listing_products.v1.SEOService/GetAllAIOAudit"
	SEOService_GetAIOAudit_FullMethodName                = "/listing_products.v1.SEOService/GetAIOAudit"
	SEOService_GetAllAIOAuditScoreResults_FullMethodName = "/listing_products.v1.SEOService/GetAllAIOAuditScoreResults"
	SEOService_GetAIOAuditStatus_FullMethodName          = "/listing_products.v1.SEOService/GetAIOAuditStatus"
	SEOService_TriggerAIOAudit_FullMethodName            = "/listing_products.v1.SEOService/TriggerAIOAudit"
	SEOService_GetSEODataSummary_FullMethodName          = "/listing_products.v1.SEOService/GetSEODataSummary"
	SEOService_GetLocalSearchSEOData_FullMethodName      = "/listing_products.v1.SEOService/GetLocalSearchSEOData"
	SEOService_StartLocalSEODataWorkflow_FullMethodName  = "/listing_products.v1.SEOService/StartLocalSEODataWorkflow"
	SEOService_SaveSEOSettings_FullMethodName            = "/listing_products.v1.SEOService/SaveSEOSettings"
	SEOService_GetSEOSettings_FullMethodName             = "/listing_products.v1.SEOService/GetSEOSettings"
	SEOService_GetActiveSEOAddons_FullMethodName         = "/listing_products.v1.SEOService/GetActiveSEOAddons"
	SEOService_StartSEOCategoryWorkflow_FullMethodName   = "/listing_products.v1.SEOService/StartSEOCategoryWorkflow"
	SEOService_GetDataForSEOCategory_FullMethodName      = "/listing_products.v1.SEOService/GetDataForSEOCategory"
)

// SEOServiceClient is the client API for SEOService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// SEO service
type SEOServiceClient interface {
	// Deprecated: use SEODataSummary & LocalSearchData
	GetSEOData(ctx context.Context, in *GetSEODataRequest, opts ...grpc.CallOption) (*GetSEODataResponse, error)
	GetAllAIOAudit(ctx context.Context, in *GetAllAIOAuditRequest, opts ...grpc.CallOption) (*GetAllAIOAuditResponse, error)
	GetAIOAudit(ctx context.Context, in *GetAIOAuditRequest, opts ...grpc.CallOption) (*GetAIOAuditResponse, error)
	GetAllAIOAuditScoreResults(ctx context.Context, in *GetAllAIOAuditScoreResultsRequest, opts ...grpc.CallOption) (*GetAllAIOAuditScoreResultsResponse, error)
	GetAIOAuditStatus(ctx context.Context, in *GetAIOAuditStatusRequest, opts ...grpc.CallOption) (*GetAIOAuditStatusResponse, error)
	TriggerAIOAudit(ctx context.Context, in *TriggerAIOAuditRequest, opts ...grpc.CallOption) (*TriggerAIOAuditResponse, error)
	// Get SEO summary data except Vicinity Data for keywords
	GetSEODataSummary(ctx context.Context, in *GetSEODataSummaryRequest, opts ...grpc.CallOption) (*GetSEODataSummaryResponse, error)
	// Get Vicinity Data for keywords
	GetLocalSearchSEOData(ctx context.Context, in *GetLocalSearchSEODataRequest, opts ...grpc.CallOption) (*GetLocalSearchSEODataResponse, error)
	StartLocalSEODataWorkflow(ctx context.Context, in *StartLocalSEODataWorkflowRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	SaveSEOSettings(ctx context.Context, in *SaveSEOSettingsRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	GetSEOSettings(ctx context.Context, in *GetSEOSettingsRequest, opts ...grpc.CallOption) (*SEOSettingsResponse, error)
	GetActiveSEOAddons(ctx context.Context, in *GetActiveSEOAddonsRequest, opts ...grpc.CallOption) (*GetActiveSEOAddonsResponse, error)
	StartSEOCategoryWorkflow(ctx context.Context, in *StartSEOCategoryWorkflowRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	GetDataForSEOCategory(ctx context.Context, in *GetDataForSEOCategoryRequest, opts ...grpc.CallOption) (*GetDataForSEOCategoryResponse, error)
}

type sEOServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewSEOServiceClient(cc grpc.ClientConnInterface) SEOServiceClient {
	return &sEOServiceClient{cc}
}

func (c *sEOServiceClient) GetSEOData(ctx context.Context, in *GetSEODataRequest, opts ...grpc.CallOption) (*GetSEODataResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetSEODataResponse)
	err := c.cc.Invoke(ctx, SEOService_GetSEOData_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sEOServiceClient) GetAllAIOAudit(ctx context.Context, in *GetAllAIOAuditRequest, opts ...grpc.CallOption) (*GetAllAIOAuditResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAllAIOAuditResponse)
	err := c.cc.Invoke(ctx, SEOService_GetAllAIOAudit_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sEOServiceClient) GetAIOAudit(ctx context.Context, in *GetAIOAuditRequest, opts ...grpc.CallOption) (*GetAIOAuditResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAIOAuditResponse)
	err := c.cc.Invoke(ctx, SEOService_GetAIOAudit_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sEOServiceClient) GetAllAIOAuditScoreResults(ctx context.Context, in *GetAllAIOAuditScoreResultsRequest, opts ...grpc.CallOption) (*GetAllAIOAuditScoreResultsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAllAIOAuditScoreResultsResponse)
	err := c.cc.Invoke(ctx, SEOService_GetAllAIOAuditScoreResults_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sEOServiceClient) GetAIOAuditStatus(ctx context.Context, in *GetAIOAuditStatusRequest, opts ...grpc.CallOption) (*GetAIOAuditStatusResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAIOAuditStatusResponse)
	err := c.cc.Invoke(ctx, SEOService_GetAIOAuditStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sEOServiceClient) TriggerAIOAudit(ctx context.Context, in *TriggerAIOAuditRequest, opts ...grpc.CallOption) (*TriggerAIOAuditResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TriggerAIOAuditResponse)
	err := c.cc.Invoke(ctx, SEOService_TriggerAIOAudit_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sEOServiceClient) GetSEODataSummary(ctx context.Context, in *GetSEODataSummaryRequest, opts ...grpc.CallOption) (*GetSEODataSummaryResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetSEODataSummaryResponse)
	err := c.cc.Invoke(ctx, SEOService_GetSEODataSummary_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sEOServiceClient) GetLocalSearchSEOData(ctx context.Context, in *GetLocalSearchSEODataRequest, opts ...grpc.CallOption) (*GetLocalSearchSEODataResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetLocalSearchSEODataResponse)
	err := c.cc.Invoke(ctx, SEOService_GetLocalSearchSEOData_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sEOServiceClient) StartLocalSEODataWorkflow(ctx context.Context, in *StartLocalSEODataWorkflowRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, SEOService_StartLocalSEODataWorkflow_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sEOServiceClient) SaveSEOSettings(ctx context.Context, in *SaveSEOSettingsRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, SEOService_SaveSEOSettings_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sEOServiceClient) GetSEOSettings(ctx context.Context, in *GetSEOSettingsRequest, opts ...grpc.CallOption) (*SEOSettingsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SEOSettingsResponse)
	err := c.cc.Invoke(ctx, SEOService_GetSEOSettings_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sEOServiceClient) GetActiveSEOAddons(ctx context.Context, in *GetActiveSEOAddonsRequest, opts ...grpc.CallOption) (*GetActiveSEOAddonsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetActiveSEOAddonsResponse)
	err := c.cc.Invoke(ctx, SEOService_GetActiveSEOAddons_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sEOServiceClient) StartSEOCategoryWorkflow(ctx context.Context, in *StartSEOCategoryWorkflowRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, SEOService_StartSEOCategoryWorkflow_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sEOServiceClient) GetDataForSEOCategory(ctx context.Context, in *GetDataForSEOCategoryRequest, opts ...grpc.CallOption) (*GetDataForSEOCategoryResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetDataForSEOCategoryResponse)
	err := c.cc.Invoke(ctx, SEOService_GetDataForSEOCategory_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SEOServiceServer is the server API for SEOService service.
// All implementations should embed UnimplementedSEOServiceServer
// for forward compatibility.
//
// SEO service
type SEOServiceServer interface {
	// Deprecated: use SEODataSummary & LocalSearchData
	GetSEOData(context.Context, *GetSEODataRequest) (*GetSEODataResponse, error)
	GetAllAIOAudit(context.Context, *GetAllAIOAuditRequest) (*GetAllAIOAuditResponse, error)
	GetAIOAudit(context.Context, *GetAIOAuditRequest) (*GetAIOAuditResponse, error)
	GetAllAIOAuditScoreResults(context.Context, *GetAllAIOAuditScoreResultsRequest) (*GetAllAIOAuditScoreResultsResponse, error)
	GetAIOAuditStatus(context.Context, *GetAIOAuditStatusRequest) (*GetAIOAuditStatusResponse, error)
	TriggerAIOAudit(context.Context, *TriggerAIOAuditRequest) (*TriggerAIOAuditResponse, error)
	// Get SEO summary data except Vicinity Data for keywords
	GetSEODataSummary(context.Context, *GetSEODataSummaryRequest) (*GetSEODataSummaryResponse, error)
	// Get Vicinity Data for keywords
	GetLocalSearchSEOData(context.Context, *GetLocalSearchSEODataRequest) (*GetLocalSearchSEODataResponse, error)
	StartLocalSEODataWorkflow(context.Context, *StartLocalSEODataWorkflowRequest) (*emptypb.Empty, error)
	SaveSEOSettings(context.Context, *SaveSEOSettingsRequest) (*emptypb.Empty, error)
	GetSEOSettings(context.Context, *GetSEOSettingsRequest) (*SEOSettingsResponse, error)
	GetActiveSEOAddons(context.Context, *GetActiveSEOAddonsRequest) (*GetActiveSEOAddonsResponse, error)
	StartSEOCategoryWorkflow(context.Context, *StartSEOCategoryWorkflowRequest) (*emptypb.Empty, error)
	GetDataForSEOCategory(context.Context, *GetDataForSEOCategoryRequest) (*GetDataForSEOCategoryResponse, error)
}

// UnimplementedSEOServiceServer should be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedSEOServiceServer struct{}

func (UnimplementedSEOServiceServer) GetSEOData(context.Context, *GetSEODataRequest) (*GetSEODataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSEOData not implemented")
}
func (UnimplementedSEOServiceServer) GetAllAIOAudit(context.Context, *GetAllAIOAuditRequest) (*GetAllAIOAuditResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAllAIOAudit not implemented")
}
func (UnimplementedSEOServiceServer) GetAIOAudit(context.Context, *GetAIOAuditRequest) (*GetAIOAuditResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAIOAudit not implemented")
}
func (UnimplementedSEOServiceServer) GetAllAIOAuditScoreResults(context.Context, *GetAllAIOAuditScoreResultsRequest) (*GetAllAIOAuditScoreResultsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAllAIOAuditScoreResults not implemented")
}
func (UnimplementedSEOServiceServer) GetAIOAuditStatus(context.Context, *GetAIOAuditStatusRequest) (*GetAIOAuditStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAIOAuditStatus not implemented")
}
func (UnimplementedSEOServiceServer) TriggerAIOAudit(context.Context, *TriggerAIOAuditRequest) (*TriggerAIOAuditResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TriggerAIOAudit not implemented")
}
func (UnimplementedSEOServiceServer) GetSEODataSummary(context.Context, *GetSEODataSummaryRequest) (*GetSEODataSummaryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSEODataSummary not implemented")
}
func (UnimplementedSEOServiceServer) GetLocalSearchSEOData(context.Context, *GetLocalSearchSEODataRequest) (*GetLocalSearchSEODataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLocalSearchSEOData not implemented")
}
func (UnimplementedSEOServiceServer) StartLocalSEODataWorkflow(context.Context, *StartLocalSEODataWorkflowRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartLocalSEODataWorkflow not implemented")
}
func (UnimplementedSEOServiceServer) SaveSEOSettings(context.Context, *SaveSEOSettingsRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SaveSEOSettings not implemented")
}
func (UnimplementedSEOServiceServer) GetSEOSettings(context.Context, *GetSEOSettingsRequest) (*SEOSettingsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSEOSettings not implemented")
}
func (UnimplementedSEOServiceServer) GetActiveSEOAddons(context.Context, *GetActiveSEOAddonsRequest) (*GetActiveSEOAddonsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetActiveSEOAddons not implemented")
}
func (UnimplementedSEOServiceServer) StartSEOCategoryWorkflow(context.Context, *StartSEOCategoryWorkflowRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartSEOCategoryWorkflow not implemented")
}
func (UnimplementedSEOServiceServer) GetDataForSEOCategory(context.Context, *GetDataForSEOCategoryRequest) (*GetDataForSEOCategoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDataForSEOCategory not implemented")
}
func (UnimplementedSEOServiceServer) testEmbeddedByValue() {}

// UnsafeSEOServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SEOServiceServer will
// result in compilation errors.
type UnsafeSEOServiceServer interface {
	mustEmbedUnimplementedSEOServiceServer()
}

func RegisterSEOServiceServer(s grpc.ServiceRegistrar, srv SEOServiceServer) {
	// If the following call pancis, it indicates UnimplementedSEOServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&SEOService_ServiceDesc, srv)
}

func _SEOService_GetSEOData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSEODataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SEOServiceServer).GetSEOData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SEOService_GetSEOData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SEOServiceServer).GetSEOData(ctx, req.(*GetSEODataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SEOService_GetAllAIOAudit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllAIOAuditRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SEOServiceServer).GetAllAIOAudit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SEOService_GetAllAIOAudit_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SEOServiceServer).GetAllAIOAudit(ctx, req.(*GetAllAIOAuditRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SEOService_GetAIOAudit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAIOAuditRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SEOServiceServer).GetAIOAudit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SEOService_GetAIOAudit_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SEOServiceServer).GetAIOAudit(ctx, req.(*GetAIOAuditRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SEOService_GetAllAIOAuditScoreResults_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllAIOAuditScoreResultsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SEOServiceServer).GetAllAIOAuditScoreResults(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SEOService_GetAllAIOAuditScoreResults_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SEOServiceServer).GetAllAIOAuditScoreResults(ctx, req.(*GetAllAIOAuditScoreResultsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SEOService_GetAIOAuditStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAIOAuditStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SEOServiceServer).GetAIOAuditStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SEOService_GetAIOAuditStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SEOServiceServer).GetAIOAuditStatus(ctx, req.(*GetAIOAuditStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SEOService_TriggerAIOAudit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TriggerAIOAuditRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SEOServiceServer).TriggerAIOAudit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SEOService_TriggerAIOAudit_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SEOServiceServer).TriggerAIOAudit(ctx, req.(*TriggerAIOAuditRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SEOService_GetSEODataSummary_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSEODataSummaryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SEOServiceServer).GetSEODataSummary(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SEOService_GetSEODataSummary_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SEOServiceServer).GetSEODataSummary(ctx, req.(*GetSEODataSummaryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SEOService_GetLocalSearchSEOData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLocalSearchSEODataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SEOServiceServer).GetLocalSearchSEOData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SEOService_GetLocalSearchSEOData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SEOServiceServer).GetLocalSearchSEOData(ctx, req.(*GetLocalSearchSEODataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SEOService_StartLocalSEODataWorkflow_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StartLocalSEODataWorkflowRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SEOServiceServer).StartLocalSEODataWorkflow(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SEOService_StartLocalSEODataWorkflow_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SEOServiceServer).StartLocalSEODataWorkflow(ctx, req.(*StartLocalSEODataWorkflowRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SEOService_SaveSEOSettings_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SaveSEOSettingsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SEOServiceServer).SaveSEOSettings(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SEOService_SaveSEOSettings_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SEOServiceServer).SaveSEOSettings(ctx, req.(*SaveSEOSettingsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SEOService_GetSEOSettings_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSEOSettingsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SEOServiceServer).GetSEOSettings(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SEOService_GetSEOSettings_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SEOServiceServer).GetSEOSettings(ctx, req.(*GetSEOSettingsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SEOService_GetActiveSEOAddons_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetActiveSEOAddonsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SEOServiceServer).GetActiveSEOAddons(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SEOService_GetActiveSEOAddons_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SEOServiceServer).GetActiveSEOAddons(ctx, req.(*GetActiveSEOAddonsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SEOService_StartSEOCategoryWorkflow_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StartSEOCategoryWorkflowRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SEOServiceServer).StartSEOCategoryWorkflow(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SEOService_StartSEOCategoryWorkflow_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SEOServiceServer).StartSEOCategoryWorkflow(ctx, req.(*StartSEOCategoryWorkflowRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SEOService_GetDataForSEOCategory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDataForSEOCategoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SEOServiceServer).GetDataForSEOCategory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SEOService_GetDataForSEOCategory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SEOServiceServer).GetDataForSEOCategory(ctx, req.(*GetDataForSEOCategoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// SEOService_ServiceDesc is the grpc.ServiceDesc for SEOService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var SEOService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "listing_products.v1.SEOService",
	HandlerType: (*SEOServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetSEOData",
			Handler:    _SEOService_GetSEOData_Handler,
		},
		{
			MethodName: "GetAllAIOAudit",
			Handler:    _SEOService_GetAllAIOAudit_Handler,
		},
		{
			MethodName: "GetAIOAudit",
			Handler:    _SEOService_GetAIOAudit_Handler,
		},
		{
			MethodName: "GetAllAIOAuditScoreResults",
			Handler:    _SEOService_GetAllAIOAuditScoreResults_Handler,
		},
		{
			MethodName: "GetAIOAuditStatus",
			Handler:    _SEOService_GetAIOAuditStatus_Handler,
		},
		{
			MethodName: "TriggerAIOAudit",
			Handler:    _SEOService_TriggerAIOAudit_Handler,
		},
		{
			MethodName: "GetSEODataSummary",
			Handler:    _SEOService_GetSEODataSummary_Handler,
		},
		{
			MethodName: "GetLocalSearchSEOData",
			Handler:    _SEOService_GetLocalSearchSEOData_Handler,
		},
		{
			MethodName: "StartLocalSEODataWorkflow",
			Handler:    _SEOService_StartLocalSEODataWorkflow_Handler,
		},
		{
			MethodName: "SaveSEOSettings",
			Handler:    _SEOService_SaveSEOSettings_Handler,
		},
		{
			MethodName: "GetSEOSettings",
			Handler:    _SEOService_GetSEOSettings_Handler,
		},
		{
			MethodName: "GetActiveSEOAddons",
			Handler:    _SEOService_GetActiveSEOAddons_Handler,
		},
		{
			MethodName: "StartSEOCategoryWorkflow",
			Handler:    _SEOService_StartSEOCategoryWorkflow_Handler,
		},
		{
			MethodName: "GetDataForSEOCategory",
			Handler:    _SEOService_GetDataForSEOCategory_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "listing_products/v1/api.proto",
}

const (
	Citations_StartCitationWorkflow_FullMethodName = "/listing_products.v1.Citations/StartCitationWorkflow"
	Citations_GetCitationData_FullMethodName       = "/listing_products.v1.Citations/GetCitationData"
	Citations_DeleteCitations_FullMethodName       = "/listing_products.v1.Citations/DeleteCitations"
	Citations_GetCitationSummary_FullMethodName    = "/listing_products.v1.Citations/GetCitationSummary"
	Citations_ListCitationDomains_FullMethodName   = "/listing_products.v1.Citations/ListCitationDomains"
)

// CitationsClient is the client API for Citations service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// Citation service
type CitationsClient interface {
	StartCitationWorkflow(ctx context.Context, in *StartCitationWorkflowRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	GetCitationData(ctx context.Context, in *GetCitationDataRequest, opts ...grpc.CallOption) (*GetCitationDataResponse, error)
	DeleteCitations(ctx context.Context, in *DeleteCitationsRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	GetCitationSummary(ctx context.Context, in *GetCitationSummaryRequest, opts ...grpc.CallOption) (*GetCitationSummaryResponse, error)
	ListCitationDomains(ctx context.Context, in *ListCitationDomainsRequest, opts ...grpc.CallOption) (*ListCitationDomainsResponse, error)
}

type citationsClient struct {
	cc grpc.ClientConnInterface
}

func NewCitationsClient(cc grpc.ClientConnInterface) CitationsClient {
	return &citationsClient{cc}
}

func (c *citationsClient) StartCitationWorkflow(ctx context.Context, in *StartCitationWorkflowRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Citations_StartCitationWorkflow_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *citationsClient) GetCitationData(ctx context.Context, in *GetCitationDataRequest, opts ...grpc.CallOption) (*GetCitationDataResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetCitationDataResponse)
	err := c.cc.Invoke(ctx, Citations_GetCitationData_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *citationsClient) DeleteCitations(ctx context.Context, in *DeleteCitationsRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Citations_DeleteCitations_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *citationsClient) GetCitationSummary(ctx context.Context, in *GetCitationSummaryRequest, opts ...grpc.CallOption) (*GetCitationSummaryResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetCitationSummaryResponse)
	err := c.cc.Invoke(ctx, Citations_GetCitationSummary_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *citationsClient) ListCitationDomains(ctx context.Context, in *ListCitationDomainsRequest, opts ...grpc.CallOption) (*ListCitationDomainsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListCitationDomainsResponse)
	err := c.cc.Invoke(ctx, Citations_ListCitationDomains_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CitationsServer is the server API for Citations service.
// All implementations should embed UnimplementedCitationsServer
// for forward compatibility.
//
// Citation service
type CitationsServer interface {
	StartCitationWorkflow(context.Context, *StartCitationWorkflowRequest) (*emptypb.Empty, error)
	GetCitationData(context.Context, *GetCitationDataRequest) (*GetCitationDataResponse, error)
	DeleteCitations(context.Context, *DeleteCitationsRequest) (*emptypb.Empty, error)
	GetCitationSummary(context.Context, *GetCitationSummaryRequest) (*GetCitationSummaryResponse, error)
	ListCitationDomains(context.Context, *ListCitationDomainsRequest) (*ListCitationDomainsResponse, error)
}

// UnimplementedCitationsServer should be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedCitationsServer struct{}

func (UnimplementedCitationsServer) StartCitationWorkflow(context.Context, *StartCitationWorkflowRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartCitationWorkflow not implemented")
}
func (UnimplementedCitationsServer) GetCitationData(context.Context, *GetCitationDataRequest) (*GetCitationDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCitationData not implemented")
}
func (UnimplementedCitationsServer) DeleteCitations(context.Context, *DeleteCitationsRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteCitations not implemented")
}
func (UnimplementedCitationsServer) GetCitationSummary(context.Context, *GetCitationSummaryRequest) (*GetCitationSummaryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCitationSummary not implemented")
}
func (UnimplementedCitationsServer) ListCitationDomains(context.Context, *ListCitationDomainsRequest) (*ListCitationDomainsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListCitationDomains not implemented")
}
func (UnimplementedCitationsServer) testEmbeddedByValue() {}

// UnsafeCitationsServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CitationsServer will
// result in compilation errors.
type UnsafeCitationsServer interface {
	mustEmbedUnimplementedCitationsServer()
}

func RegisterCitationsServer(s grpc.ServiceRegistrar, srv CitationsServer) {
	// If the following call pancis, it indicates UnimplementedCitationsServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Citations_ServiceDesc, srv)
}

func _Citations_StartCitationWorkflow_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StartCitationWorkflowRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CitationsServer).StartCitationWorkflow(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Citations_StartCitationWorkflow_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CitationsServer).StartCitationWorkflow(ctx, req.(*StartCitationWorkflowRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Citations_GetCitationData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCitationDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CitationsServer).GetCitationData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Citations_GetCitationData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CitationsServer).GetCitationData(ctx, req.(*GetCitationDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Citations_DeleteCitations_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteCitationsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CitationsServer).DeleteCitations(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Citations_DeleteCitations_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CitationsServer).DeleteCitations(ctx, req.(*DeleteCitationsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Citations_GetCitationSummary_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCitationSummaryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CitationsServer).GetCitationSummary(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Citations_GetCitationSummary_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CitationsServer).GetCitationSummary(ctx, req.(*GetCitationSummaryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Citations_ListCitationDomains_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListCitationDomainsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CitationsServer).ListCitationDomains(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Citations_ListCitationDomains_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CitationsServer).ListCitationDomains(ctx, req.(*ListCitationDomainsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Citations_ServiceDesc is the grpc.ServiceDesc for Citations service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Citations_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "listing_products.v1.Citations",
	HandlerType: (*CitationsServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "StartCitationWorkflow",
			Handler:    _Citations_StartCitationWorkflow_Handler,
		},
		{
			MethodName: "GetCitationData",
			Handler:    _Citations_GetCitationData_Handler,
		},
		{
			MethodName: "DeleteCitations",
			Handler:    _Citations_DeleteCitations_Handler,
		},
		{
			MethodName: "GetCitationSummary",
			Handler:    _Citations_GetCitationSummary_Handler,
		},
		{
			MethodName: "ListCitationDomains",
			Handler:    _Citations_ListCitationDomains_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "listing_products/v1/api.proto",
}

const (
	PartnerSettingsService_CreatePartnerSettings_FullMethodName = "/listing_products.v1.PartnerSettingsService/CreatePartnerSettings"
	PartnerSettingsService_GetPartnerSettings_FullMethodName    = "/listing_products.v1.PartnerSettingsService/GetPartnerSettings"
	PartnerSettingsService_UpsertPartnerSettings_FullMethodName = "/listing_products.v1.PartnerSettingsService/UpsertPartnerSettings"
	PartnerSettingsService_GetConfiguration_FullMethodName      = "/listing_products.v1.PartnerSettingsService/GetConfiguration"
)

// PartnerSettingsServiceClient is the client API for PartnerSettingsService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PartnerSettingsServiceClient interface {
	// CreatePartnerSettings will create a new PartnerSettings
	CreatePartnerSettings(ctx context.Context, in *CreatePartnerSettingsRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// GetPartnerSettings returns information about the partner settings for a given partnerID / marketID
	GetPartnerSettings(ctx context.Context, in *GetPartnerSettingsRequest, opts ...grpc.CallOption) (*GetPartnerSettingsResponse, error)
	// UpsertPartnerSettings will create a new PartnerSettings if it does not exist yet, or update the existing PartnerSettings if it does.
	UpsertPartnerSettings(ctx context.Context, in *UpsertPartnerSettingsRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// GetConfiguration returns the configuration for a given partner
	GetConfiguration(ctx context.Context, in *GetConfigurationRequest, opts ...grpc.CallOption) (*GetConfigurationResponse, error)
}

type partnerSettingsServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPartnerSettingsServiceClient(cc grpc.ClientConnInterface) PartnerSettingsServiceClient {
	return &partnerSettingsServiceClient{cc}
}

func (c *partnerSettingsServiceClient) CreatePartnerSettings(ctx context.Context, in *CreatePartnerSettingsRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, PartnerSettingsService_CreatePartnerSettings_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *partnerSettingsServiceClient) GetPartnerSettings(ctx context.Context, in *GetPartnerSettingsRequest, opts ...grpc.CallOption) (*GetPartnerSettingsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetPartnerSettingsResponse)
	err := c.cc.Invoke(ctx, PartnerSettingsService_GetPartnerSettings_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *partnerSettingsServiceClient) UpsertPartnerSettings(ctx context.Context, in *UpsertPartnerSettingsRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, PartnerSettingsService_UpsertPartnerSettings_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *partnerSettingsServiceClient) GetConfiguration(ctx context.Context, in *GetConfigurationRequest, opts ...grpc.CallOption) (*GetConfigurationResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetConfigurationResponse)
	err := c.cc.Invoke(ctx, PartnerSettingsService_GetConfiguration_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PartnerSettingsServiceServer is the server API for PartnerSettingsService service.
// All implementations should embed UnimplementedPartnerSettingsServiceServer
// for forward compatibility.
type PartnerSettingsServiceServer interface {
	// CreatePartnerSettings will create a new PartnerSettings
	CreatePartnerSettings(context.Context, *CreatePartnerSettingsRequest) (*emptypb.Empty, error)
	// GetPartnerSettings returns information about the partner settings for a given partnerID / marketID
	GetPartnerSettings(context.Context, *GetPartnerSettingsRequest) (*GetPartnerSettingsResponse, error)
	// UpsertPartnerSettings will create a new PartnerSettings if it does not exist yet, or update the existing PartnerSettings if it does.
	UpsertPartnerSettings(context.Context, *UpsertPartnerSettingsRequest) (*emptypb.Empty, error)
	// GetConfiguration returns the configuration for a given partner
	GetConfiguration(context.Context, *GetConfigurationRequest) (*GetConfigurationResponse, error)
}

// UnimplementedPartnerSettingsServiceServer should be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedPartnerSettingsServiceServer struct{}

func (UnimplementedPartnerSettingsServiceServer) CreatePartnerSettings(context.Context, *CreatePartnerSettingsRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreatePartnerSettings not implemented")
}
func (UnimplementedPartnerSettingsServiceServer) GetPartnerSettings(context.Context, *GetPartnerSettingsRequest) (*GetPartnerSettingsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPartnerSettings not implemented")
}
func (UnimplementedPartnerSettingsServiceServer) UpsertPartnerSettings(context.Context, *UpsertPartnerSettingsRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpsertPartnerSettings not implemented")
}
func (UnimplementedPartnerSettingsServiceServer) GetConfiguration(context.Context, *GetConfigurationRequest) (*GetConfigurationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetConfiguration not implemented")
}
func (UnimplementedPartnerSettingsServiceServer) testEmbeddedByValue() {}

// UnsafePartnerSettingsServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PartnerSettingsServiceServer will
// result in compilation errors.
type UnsafePartnerSettingsServiceServer interface {
	mustEmbedUnimplementedPartnerSettingsServiceServer()
}

func RegisterPartnerSettingsServiceServer(s grpc.ServiceRegistrar, srv PartnerSettingsServiceServer) {
	// If the following call pancis, it indicates UnimplementedPartnerSettingsServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&PartnerSettingsService_ServiceDesc, srv)
}

func _PartnerSettingsService_CreatePartnerSettings_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreatePartnerSettingsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PartnerSettingsServiceServer).CreatePartnerSettings(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PartnerSettingsService_CreatePartnerSettings_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PartnerSettingsServiceServer).CreatePartnerSettings(ctx, req.(*CreatePartnerSettingsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PartnerSettingsService_GetPartnerSettings_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPartnerSettingsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PartnerSettingsServiceServer).GetPartnerSettings(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PartnerSettingsService_GetPartnerSettings_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PartnerSettingsServiceServer).GetPartnerSettings(ctx, req.(*GetPartnerSettingsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PartnerSettingsService_UpsertPartnerSettings_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpsertPartnerSettingsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PartnerSettingsServiceServer).UpsertPartnerSettings(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PartnerSettingsService_UpsertPartnerSettings_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PartnerSettingsServiceServer).UpsertPartnerSettings(ctx, req.(*UpsertPartnerSettingsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PartnerSettingsService_GetConfiguration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetConfigurationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PartnerSettingsServiceServer).GetConfiguration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PartnerSettingsService_GetConfiguration_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PartnerSettingsServiceServer).GetConfiguration(ctx, req.(*GetConfigurationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// PartnerSettingsService_ServiceDesc is the grpc.ServiceDesc for PartnerSettingsService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PartnerSettingsService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "listing_products.v1.PartnerSettingsService",
	HandlerType: (*PartnerSettingsServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreatePartnerSettings",
			Handler:    _PartnerSettingsService_CreatePartnerSettings_Handler,
		},
		{
			MethodName: "GetPartnerSettings",
			Handler:    _PartnerSettingsService_GetPartnerSettings_Handler,
		},
		{
			MethodName: "UpsertPartnerSettings",
			Handler:    _PartnerSettingsService_UpsertPartnerSettings_Handler,
		},
		{
			MethodName: "GetConfiguration",
			Handler:    _PartnerSettingsService_GetConfiguration_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "listing_products/v1/api.proto",
}

const (
	SEOSuggestedKeywordsService_GetOrGenerateSEOSuggestedKeywords_FullMethodName = "/listing_products.v1.SEOSuggestedKeywordsService/GetOrGenerateSEOSuggestedKeywords"
	SEOSuggestedKeywordsService_GetSEOSuggestedKeywords_FullMethodName           = "/listing_products.v1.SEOSuggestedKeywordsService/GetSEOSuggestedKeywords"
)

// SEOSuggestedKeywordsServiceClient is the client API for SEOSuggestedKeywordsService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SEOSuggestedKeywordsServiceClient interface {
	// GetOrGenerateSEOSuggestedKeywords will get or generate SEO suggested keywords for a given business
	GetOrGenerateSEOSuggestedKeywords(ctx context.Context, in *GetOrGenerateSEOSuggestedKeywordsRequest, opts ...grpc.CallOption) (*GetOrGenerateSEOSuggestedKeywordsResponse, error)
	// GetSEOSuggestedKeywords returns SEO suggested keywords for a given business
	GetSEOSuggestedKeywords(ctx context.Context, in *GetSEOSuggestedKeywordsRequest, opts ...grpc.CallOption) (*GetSEOSuggestedKeywordsResponse, error)
}

type sEOSuggestedKeywordsServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewSEOSuggestedKeywordsServiceClient(cc grpc.ClientConnInterface) SEOSuggestedKeywordsServiceClient {
	return &sEOSuggestedKeywordsServiceClient{cc}
}

func (c *sEOSuggestedKeywordsServiceClient) GetOrGenerateSEOSuggestedKeywords(ctx context.Context, in *GetOrGenerateSEOSuggestedKeywordsRequest, opts ...grpc.CallOption) (*GetOrGenerateSEOSuggestedKeywordsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetOrGenerateSEOSuggestedKeywordsResponse)
	err := c.cc.Invoke(ctx, SEOSuggestedKeywordsService_GetOrGenerateSEOSuggestedKeywords_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sEOSuggestedKeywordsServiceClient) GetSEOSuggestedKeywords(ctx context.Context, in *GetSEOSuggestedKeywordsRequest, opts ...grpc.CallOption) (*GetSEOSuggestedKeywordsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetSEOSuggestedKeywordsResponse)
	err := c.cc.Invoke(ctx, SEOSuggestedKeywordsService_GetSEOSuggestedKeywords_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SEOSuggestedKeywordsServiceServer is the server API for SEOSuggestedKeywordsService service.
// All implementations should embed UnimplementedSEOSuggestedKeywordsServiceServer
// for forward compatibility.
type SEOSuggestedKeywordsServiceServer interface {
	// GetOrGenerateSEOSuggestedKeywords will get or generate SEO suggested keywords for a given business
	GetOrGenerateSEOSuggestedKeywords(context.Context, *GetOrGenerateSEOSuggestedKeywordsRequest) (*GetOrGenerateSEOSuggestedKeywordsResponse, error)
	// GetSEOSuggestedKeywords returns SEO suggested keywords for a given business
	GetSEOSuggestedKeywords(context.Context, *GetSEOSuggestedKeywordsRequest) (*GetSEOSuggestedKeywordsResponse, error)
}

// UnimplementedSEOSuggestedKeywordsServiceServer should be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedSEOSuggestedKeywordsServiceServer struct{}

func (UnimplementedSEOSuggestedKeywordsServiceServer) GetOrGenerateSEOSuggestedKeywords(context.Context, *GetOrGenerateSEOSuggestedKeywordsRequest) (*GetOrGenerateSEOSuggestedKeywordsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOrGenerateSEOSuggestedKeywords not implemented")
}
func (UnimplementedSEOSuggestedKeywordsServiceServer) GetSEOSuggestedKeywords(context.Context, *GetSEOSuggestedKeywordsRequest) (*GetSEOSuggestedKeywordsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSEOSuggestedKeywords not implemented")
}
func (UnimplementedSEOSuggestedKeywordsServiceServer) testEmbeddedByValue() {}

// UnsafeSEOSuggestedKeywordsServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SEOSuggestedKeywordsServiceServer will
// result in compilation errors.
type UnsafeSEOSuggestedKeywordsServiceServer interface {
	mustEmbedUnimplementedSEOSuggestedKeywordsServiceServer()
}

func RegisterSEOSuggestedKeywordsServiceServer(s grpc.ServiceRegistrar, srv SEOSuggestedKeywordsServiceServer) {
	// If the following call pancis, it indicates UnimplementedSEOSuggestedKeywordsServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&SEOSuggestedKeywordsService_ServiceDesc, srv)
}

func _SEOSuggestedKeywordsService_GetOrGenerateSEOSuggestedKeywords_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOrGenerateSEOSuggestedKeywordsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SEOSuggestedKeywordsServiceServer).GetOrGenerateSEOSuggestedKeywords(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SEOSuggestedKeywordsService_GetOrGenerateSEOSuggestedKeywords_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SEOSuggestedKeywordsServiceServer).GetOrGenerateSEOSuggestedKeywords(ctx, req.(*GetOrGenerateSEOSuggestedKeywordsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SEOSuggestedKeywordsService_GetSEOSuggestedKeywords_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSEOSuggestedKeywordsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SEOSuggestedKeywordsServiceServer).GetSEOSuggestedKeywords(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SEOSuggestedKeywordsService_GetSEOSuggestedKeywords_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SEOSuggestedKeywordsServiceServer).GetSEOSuggestedKeywords(ctx, req.(*GetSEOSuggestedKeywordsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// SEOSuggestedKeywordsService_ServiceDesc is the grpc.ServiceDesc for SEOSuggestedKeywordsService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var SEOSuggestedKeywordsService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "listing_products.v1.SEOSuggestedKeywordsService",
	HandlerType: (*SEOSuggestedKeywordsServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetOrGenerateSEOSuggestedKeywords",
			Handler:    _SEOSuggestedKeywordsService_GetOrGenerateSEOSuggestedKeywords_Handler,
		},
		{
			MethodName: "GetSEOSuggestedKeywords",
			Handler:    _SEOSuggestedKeywordsService_GetSEOSuggestedKeywords_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "listing_products/v1/api.proto",
}
