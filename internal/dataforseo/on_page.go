package dataforseo

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"

	"github.com/vendasta/gosdks/logging"
	"github.com/vendasta/gosdks/verrors"
)

const (
	OnPagePath Path = "/on_page"
)

// OnPageTaskPostRequest represents the request structure for on_page task_post
type OnPageTaskPostRequest struct {
	Target                  string `json:"target"`
	MaxCrawlPages           int    `json:"max_crawl_pages"`
	CrawlMode               string `json:"crawl_mode,omitempty"`
	LoadResources           bool   `json:"load_resources,omitempty"`
	StoreRawHTML            bool   `json:"store_raw_html,omitempty"`
	EnableAIContentAnalysis bool   `json:"enable_ai_content_analysis,omitempty"`
	EnableJavascript        bool   `json:"enable_javascript,omitempty"`
	Tag                     string `json:"tag,omitempty"`
	CustomUserAgent         string `json:"custom_user_agent,omitempty"`
}

// OnPageTask represents a single task in the response
type OnPageTask struct {
	ID            string          `json:"id"`
	StatusCode    int64           `json:"status_code"`
	StatusMessage string          `json:"status_message"`
	Time          string          `json:"time"`
	Cost          float64         `json:"cost"`
	ResultCount   int64           `json:"result_count"`
	Path          []string        `json:"path"`
	Data          *OnPageTaskData `json:"data"`
	Result        interface{}     `json:"result"`
}

// OnPageTaskData represents the data field in the task response
type OnPageTaskData struct {
	API           string `json:"api"`
	Function      string `json:"function"`
	Target        string `json:"target"`
	MaxCrawlPages int    `json:"max_crawl_pages"`
	Tag           string `json:"tag,omitempty"`
}

// OnPageTaskResult represents a single task result in the tasks_ready response
type OnPageTaskResult struct {
	ID         string `json:"id"`
	Target     string `json:"target"`
	DatePosted string `json:"date_posted"`
	Tag        string `json:"tag"`
}

// OnPageResponse represents the response from on_page task_post
type OnPageResponse struct {
	Version       string        `json:"version"`
	StatusCode    int64         `json:"status_code"`
	StatusMessage string        `json:"status_message"`
	Time          string        `json:"time"`
	Cost          float64       `json:"cost"`
	TasksCount    int64         `json:"tasks_count"`
	TasksError    int64         `json:"tasks_error"`
	Tasks         []*OnPageTask `json:"tasks"`
}

// OnPageTaskPost posts a task to the DataForSEO on_page API
func (d *Client) OnPageTaskPost(ctx context.Context, requests []*OnPageTaskPostRequest, source RequestSource) (*OnPageResponse, error) {
	if len(requests) == 0 {
		return nil, verrors.New(verrors.InvalidArgument, "at least one request is required")
	}

	p, err := json.Marshal(requests)
	if err != nil {
		return nil, verrors.FromError(err)
	}

	body := bytes.NewBuffer(p)
	path := fmt.Sprintf("%s%s%s", rootURL, OnPagePath, taskPostPath)

	httpRequest, err := http.NewRequest(http.MethodPost, path, body)
	if err != nil {
		return nil, verrors.FromError(err)
	}

	httpRequest.Header.Set("Content-Type", "application/json")

	response, err := d.Do(ctx, httpRequest, "on-page", source)
	if err != nil {
		return nil, err
	}

	resp, err := parseOnPageResponse(ctx, response)
	if err != nil {
		return nil, err
	}

	TickDatadogPost(resp.TasksCount, resp.Cost, "on-page", source)

	return resp, nil
}

// parseOnPageResponse parses the response from the on_page API
func parseOnPageResponse(ctx context.Context, b []byte) (*OnPageResponse, error) {
	var response *OnPageResponse

	// Log attempt
	logging.Infof(ctx, "[OnPage] Attempting to parse response of %d bytes", len(b))

	// Try direct unmarshal first
	err := json.Unmarshal(b, &response)
	if err != nil {
		// Log detailed error information
		logging.Errorf(ctx, "[OnPage] JSON unmarshalling failed: %v", err)
		logging.Errorf(ctx, "[OnPage] Response body that failed to parse: %s", string(b))

		// Try to provide more specific error messages
		if strings.Contains(err.Error(), "cannot unmarshal") {
			logging.Errorf(ctx, "[OnPage] Type mismatch detected. Check field types in struct definition.")
		}
		if strings.Contains(err.Error(), "unexpected end") {
			logging.Errorf(ctx, "[OnPage] Incomplete JSON response detected.")
		}
		if strings.Contains(err.Error(), "invalid character") {
			logging.Errorf(ctx, "[OnPage] Invalid JSON syntax detected.")
		}

		return nil, verrors.FromError(err)
	}

	// Validate the parsed structure
	if response == nil {
		return nil, verrors.New(verrors.InvalidArgument, "parsed response is nil")
	}

	// Check for error status codes
	if response.StatusCode >= 40000 {
		logging.Errorf(ctx, "[OnPage] API returned error status code: %d", response.StatusCode)
	}

	// Check for empty tasks
	if len(response.Tasks) == 0 {
		logging.Errorf(ctx, "[OnPage] No tasks found in response")
	}

	logging.Infof(ctx, "[OnPage] Successfully parsed response")
	return response, nil
}

// OnPageTasksReadyResponse represents the response from on_page tasks_ready
type OnPageTasksReadyResponse struct {
	Version       string        `json:"version"`
	StatusCode    int64         `json:"status_code"`
	StatusMessage string        `json:"status_message"`
	Time          string        `json:"time"`
	Cost          float64       `json:"cost"`
	TasksCount    int64         `json:"tasks_count"`
	TasksError    int64         `json:"tasks_error"`
	Tasks         []*OnPageTask `json:"tasks"`
}

// OnPageTasksReady checks if on_page tasks are ready
func (d *Client) OnPageTasksReady(ctx context.Context, source RequestSource) (*OnPageTasksReadyResponse, error) {
	path := fmt.Sprintf("%s%s%s", rootURL, OnPagePath, taskReadyPath)

	httpRequest, err := http.NewRequest(http.MethodGet, path, nil)
	if err != nil {
		return nil, verrors.FromError(err)
	}

	response, err := d.Do(ctx, httpRequest, "on-page-tasks-ready", source)
	if err != nil {
		return nil, err
	}

	resp, err := parseOnPageTasksReadyResponse(ctx, response)
	if err != nil {
		return nil, err
	}

	TickDatadogGet("on-page-tasks-ready")

	return resp, nil
}

// parseOnPageTasksReadyResponse parses the response from the on_page tasks_ready API
func parseOnPageTasksReadyResponse(ctx context.Context, b []byte) (*OnPageTasksReadyResponse, error) {
	var response *OnPageTasksReadyResponse

	// Log attempt
	logging.Infof(ctx, "[OnPageTasksReady] Attempting to parse response of %d bytes", len(b))

	err := json.Unmarshal(b, &response)
	if err != nil {
		logging.Errorf(ctx, "[OnPageTasksReady] JSON unmarshalling failed: %v", err)
		logging.Errorf(ctx, "[OnPageTasksReady] Response body that failed to parse: %s", string(b))

		// Provide specific guidance for common type mismatches
		if strings.Contains(err.Error(), "cannot unmarshal") {
			logging.Errorf(ctx, "[OnPageTasksReady] Type mismatch detected. Check field types in struct definition.")
		}
		if strings.Contains(err.Error(), "unexpected end") {
			logging.Errorf(ctx, "[OnPageTasksReady] Incomplete JSON response detected.")
		}
		if strings.Contains(err.Error(), "invalid character") {
			logging.Errorf(ctx, "[OnPageTasksReady] Invalid JSON syntax detected.")
		}

		return nil, verrors.FromError(err)
	}

	// Validate the parsed structure
	if response == nil {
		return nil, verrors.New(verrors.InvalidArgument, "parsed response is nil")
	}

	// Check for error status codes
	if response.StatusCode >= 40000 {
		logging.Errorf(ctx, "[OnPageTasksReady] API returned error status code: %d", response.StatusCode)
	}

	logging.Infof(ctx, "[OnPageTasksReady] Successfully parsed response")
	return response, nil
}

// OnPageSummaryResponse represents the response from on_page summary
type OnPageSummaryResponse struct {
	Version       string               `json:"version"`
	StatusCode    int64                `json:"status_code"`
	StatusMessage string               `json:"status_message"`
	Time          string               `json:"time"`
	Cost          float64              `json:"cost"`
	TasksCount    int64                `json:"tasks_count"`
	TasksError    int64                `json:"tasks_error"`
	Tasks         []*OnPageSummaryTask `json:"tasks"`
}

// OnPageSummaryTask represents a single task in the summary response
type OnPageSummaryTask struct {
	ID            string                 `json:"id"`
	StatusCode    int64                  `json:"status_code"`
	StatusMessage string                 `json:"status_message"`
	Time          string                 `json:"time"`
	Cost          float64                `json:"cost"`
	ResultCount   int64                  `json:"result_count"`
	Path          []string               `json:"path"`
	Data          *OnPageSummaryData     `json:"data"`
	Result        []*OnPageSummaryResult `json:"result"`
}

// OnPageSummaryData represents the data field in the summary response
type OnPageSummaryData struct {
	API                    string `json:"api"`
	Function               string `json:"function"`
	Target                 string `json:"target"`
	MaxCrawlPages          int    `json:"max_crawl_pages"`
	EnableWWWRedirectCheck string `json:"enable_www_redirect_check,omitempty"`
}

// OnPageSummaryResult represents a single result in the summary response
type OnPageSummaryResult struct {
	CrawlProgress       string             `json:"crawl_progress"`
	CrawlStatus         *OnPageCrawlStatus `json:"crawl_status"`
	CrawlGatewayAddress string             `json:"crawl_gateway_address"`
	CrawlStopReason     string             `json:"crawl_stop_reason"`
	DomainInfo          *OnPageDomainInfo  `json:"domain_info"`
	PageMetrics         *OnPagePageMetrics `json:"page_metrics"`
}

// OnPageCrawlStatus represents the crawl status information
type OnPageCrawlStatus struct {
	MaxCrawlPages int64 `json:"max_crawl_pages"`
	PagesInQueue  int64 `json:"pages_in_queue"`
	PagesCrawled  int64 `json:"pages_crawled"`
}

// OnPageDomainInfo represents domain information
type OnPageDomainInfo struct {
	Name                        string              `json:"name"`
	CMS                         string              `json:"cms"`
	IP                          string              `json:"ip"`
	Server                      string              `json:"server"`
	CrawlStart                  string              `json:"crawl_start"`
	CrawlEnd                    string              `json:"crawl_end"`
	ExtendedCrawlStatus         string              `json:"extended_crawl_status"`
	SSLInfo                     *OnPageSSLInfo      `json:"ssl_info"`
	Checks                      *OnPageDomainChecks `json:"checks"`
	TotalPages                  int64               `json:"total_pages"`
	PageNotFoundStatusCode      int64               `json:"page_not_found_status_code"`
	CanonicalizationStatusCode  int64               `json:"canonicalization_status_code"`
	DirectoryBrowsingStatusCode int64               `json:"directory_browsing_status_code"`
	WWWRedirectStatusCode       *int64              `json:"www_redirect_status_code"`
	MainDomain                  string              `json:"main_domain"`
}

// OnPageSSLInfo represents SSL certificate information
type OnPageSSLInfo struct {
	ValidCertificate          bool   `json:"valid_certificate"`
	CertificateIssuer         string `json:"certificate_issuer"`
	CertificateSubject        string `json:"certificate_subject"`
	CertificateVersion        int64  `json:"certificate_version"`
	CertificateHash           string `json:"certificate_hash"`
	CertificateExpirationDate string `json:"certificate_expiration_date"`
}

// OnPageDomainChecks represents domain checks
type OnPageDomainChecks struct {
	Sitemap                   bool `json:"sitemap"`
	RobotsTxt                 bool `json:"robots_txt"`
	StartPageDenyFlag         bool `json:"start_page_deny_flag"`
	SSL                       bool `json:"ssl"`
	HTTP2                     bool `json:"http2"`
	TestCanonicalization      bool `json:"test_canonicalization"`
	TestWWWRedirect           bool `json:"test_www_redirect"`
	TestHiddenServerSignature bool `json:"test_hidden_server_signature"`
	TestPageNotFound          bool `json:"test_page_not_found"`
	TestDirectoryBrowsing     bool `json:"test_directory_browsing"`
	TestHTTPSRedirect         bool `json:"test_https_redirect"`
}

// OnPagePageMetrics represents page metrics
type OnPagePageMetrics struct {
	LinksExternal         int64                    `json:"links_external"`
	LinksInternal         int64                    `json:"links_internal"`
	DuplicateTitle        int64                    `json:"duplicate_title"`
	DuplicateDescription  int64                    `json:"duplicate_description"`
	DuplicateContent      int64                    `json:"duplicate_content"`
	BrokenLinks           int64                    `json:"broken_links"`
	BrokenResources       int64                    `json:"broken_resources"`
	LinksRelationConflict int64                    `json:"links_relation_conflict"`
	RedirectLoop          int64                    `json:"redirect_loop"`
	OnpageScore           float64                  `json:"onpage_score"`
	NonIndexable          int64                    `json:"non_indexable"`
	Checks                *OnPageSummaryPageChecks `json:"checks"`
}

// OnPageSummaryPageChecks represents page checks in summary response (uses integers)
type OnPageSummaryPageChecks struct {
	Canonical                         int64  `json:"canonical"`
	DuplicateMetaTags                 int64  `json:"duplicate_meta_tags"`
	NoDescription                     int64  `json:"no_description"`
	Frame                             int64  `json:"frame"`
	LargePageSize                     int64  `json:"large_page_size"`
	IrrelevantDescription             int64  `json:"irrelevant_description"`
	IrrelevantMetaKeywords            int64  `json:"irrelevant_meta_keywords"`
	IsHTTPS                           int64  `json:"is_https"`
	IsHTTP                            int64  `json:"is_http"`
	TitleTooLong                      int64  `json:"title_too_long"`
	LowContentRate                    int64  `json:"low_content_rate"`
	SmallPageSize                     int64  `json:"small_page_size"`
	NoH1Tag                           int64  `json:"no_h1_tag"`
	RecursiveCanonical                int64  `json:"recursive_canonical"`
	NoFavicon                         int64  `json:"no_favicon"`
	NoImageAlt                        int64  `json:"no_image_alt"`
	NoImageTitle                      int64  `json:"no_image_title"`
	SEOFriendlyURL                    int64  `json:"seo_friendly_url"`
	SEOFriendlyURLCharactersCheck     int64  `json:"seo_friendly_url_characters_check"`
	SEOFriendlyURLDynamicCheck        int64  `json:"seo_friendly_url_dynamic_check"`
	SEOFriendlyURLKeywordsCheck       int64  `json:"seo_friendly_url_keywords_check"`
	SEOFriendlyURLRelativeLengthCheck int64  `json:"seo_friendly_url_relative_length_check"`
	TitleTooShort                     int64  `json:"title_too_short"`
	NoContentEncoding                 int64  `json:"no_content_encoding"`
	HighWaitingTime                   int64  `json:"high_waiting_time"`
	HighLoadingTime                   int64  `json:"high_loading_time"`
	IsRedirect                        int64  `json:"is_redirect"`
	IsBroken                          int64  `json:"is_broken"`
	Is4xxCode                         int64  `json:"is_4xx_code"`
	Is5xxCode                         int64  `json:"is_5xx_code"`
	IsWWW                             int64  `json:"is_www"`
	NoDoctype                         int64  `json:"no_doctype"`
	NoEncodingMetaTag                 int64  `json:"no_encoding_meta_tag"`
	HighContentRate                   int64  `json:"high_content_rate"`
	LowCharacterCount                 int64  `json:"low_character_count"`
	HighCharacterCount                int64  `json:"high_character_count"`
	LowReadabilityRate                int64  `json:"low_readability_rate"`
	IrrelevantTitle                   int64  `json:"irrelevant_title"`
	DeprecatedHTMLTags                int64  `json:"deprecated_html_tags"`
	DuplicateTitleTag                 int64  `json:"duplicate_title_tag"`
	NoTitle                           int64  `json:"no_title"`
	Flash                             int64  `json:"flash"`
	LoremIpsum                        int64  `json:"lorem_ipsum"`
	HasMisspelling                    *int64 `json:"has_misspelling"`
	CanonicalToBroken                 int64  `json:"canonical_to_broken"`
	CanonicalToRedirect               int64  `json:"canonical_to_redirect"`
	HasLinksToRedirects               int64  `json:"has_links_to_redirects"`
	IsOrphanPage                      int64  `json:"is_orphan_page"`
	HasMetaRefreshRedirect            int64  `json:"has_meta_refresh_redirect"`
	MetaCharsetConsistency            int64  `json:"meta_charset_consistency"`
	SizeGreaterThan3MB                int64  `json:"size_greater_than_3mb"`
	HasHTMLDoctype                    int64  `json:"has_html_doctype"`
	HTTPSToHTTPLinks                  int64  `json:"https_to_http_links"`
	HasRenderBlockingResources        int64  `json:"has_render_blocking_resources"`
	RedirectChain                     int64  `json:"redirect_chain"`
	CanonicalChain                    int64  `json:"canonical_chain"`
	IsLinkRelationConflict            int64  `json:"is_link_relation_conflict"`
}

// OnPagePageChecks represents page checks
type OnPagePageChecks struct {
	Canonical                         bool  `json:"canonical"`
	DuplicateMetaTags                 bool  `json:"duplicate_meta_tags"`
	NoDescription                     bool  `json:"no_description"`
	Frame                             bool  `json:"frame"`
	LargePageSize                     bool  `json:"large_page_size"`
	IrrelevantDescription             bool  `json:"irrelevant_description"`
	IrrelevantMetaKeywords            bool  `json:"irrelevant_meta_keywords"`
	IsHTTPS                           bool  `json:"is_https"`
	IsHTTP                            bool  `json:"is_http"`
	TitleTooLong                      bool  `json:"title_too_long"`
	HasMetaTitle                      bool  `json:"has_meta_title"`
	LowContentRate                    bool  `json:"low_content_rate"`
	SmallPageSize                     bool  `json:"small_page_size"`
	NoH1Tag                           bool  `json:"no_h1_tag"`
	RecursiveCanonical                bool  `json:"recursive_canonical"`
	NoFavicon                         bool  `json:"no_favicon"`
	NoImageAlt                        bool  `json:"no_image_alt"`
	NoImageTitle                      bool  `json:"no_image_title"`
	SEOFriendlyURL                    bool  `json:"seo_friendly_url"`
	SEOFriendlyURLCharactersCheck     bool  `json:"seo_friendly_url_characters_check"`
	SEOFriendlyURLDynamicCheck        bool  `json:"seo_friendly_url_dynamic_check"`
	SEOFriendlyURLKeywordsCheck       bool  `json:"seo_friendly_url_keywords_check"`
	SEOFriendlyURLRelativeLengthCheck bool  `json:"seo_friendly_url_relative_length_check"`
	TitleTooShort                     bool  `json:"title_too_short"`
	NoContentEncoding                 bool  `json:"no_content_encoding"`
	HighWaitingTime                   bool  `json:"high_waiting_time"`
	HighLoadingTime                   bool  `json:"high_loading_time"`
	IsRedirect                        bool  `json:"is_redirect"`
	IsBroken                          bool  `json:"is_broken"`
	Is4xxCode                         bool  `json:"is_4xx_code"`
	Is5xxCode                         bool  `json:"is_5xx_code"`
	IsWWW                             bool  `json:"is_www"`
	NoDoctype                         bool  `json:"no_doctype"`
	NoEncodingMetaTag                 bool  `json:"no_encoding_meta_tag"`
	HighContentRate                   bool  `json:"high_content_rate"`
	LowCharacterCount                 bool  `json:"low_character_count"`
	HighCharacterCount                bool  `json:"high_character_count"`
	LowReadabilityRate                bool  `json:"low_readability_rate"`
	IrrelevantTitle                   bool  `json:"irrelevant_title"`
	DeprecatedHTMLTags                bool  `json:"deprecated_html_tags"`
	DuplicateTitleTag                 bool  `json:"duplicate_title_tag"`
	NoTitle                           bool  `json:"no_title"`
	Flash                             bool  `json:"flash"`
	LoremIpsum                        bool  `json:"lorem_ipsum"`
	HasMisspelling                    *bool `json:"has_misspelling"`
	CanonicalToBroken                 bool  `json:"canonical_to_broken"`
	CanonicalToRedirect               bool  `json:"canonical_to_redirect"`
	HasLinksToRedirects               bool  `json:"has_links_to_redirects"`
	IsOrphanPage                      bool  `json:"is_orphan_page"`
	HasMetaRefreshRedirect            bool  `json:"has_meta_refresh_redirect"`
	MetaCharsetConsistency            bool  `json:"meta_charset_consistency"`
	SizeGreaterThan3MB                bool  `json:"size_greater_than_3mb"`
	HasHTMLDoctype                    bool  `json:"has_html_doctype"`
	HTTPSToHTTPLinks                  bool  `json:"https_to_http_links"`
	HasRenderBlockingResources        bool  `json:"has_render_blocking_resources"`
	RedirectChain                     bool  `json:"redirect_chain"`
	CanonicalChain                    bool  `json:"canonical_chain"`
	IsLinkRelationConflict            bool  `json:"is_link_relation_conflict"`
}

// OnPageSummary calls the on_page summary endpoint for a specific task ID
func (d *Client) OnPageSummary(ctx context.Context, taskID string, source RequestSource) (*OnPageSummaryResponse, error) {
	path := fmt.Sprintf("%s%s/summary/%s", rootURL, OnPagePath, taskID)

	httpRequest, err := http.NewRequest(http.MethodGet, path, nil)
	if err != nil {
		return nil, verrors.FromError(err)
	}

	response, err := d.Do(ctx, httpRequest, "on-page-summary", source)
	if err != nil {
		return nil, err
	}

	resp, err := parseOnPageSummaryResponse(ctx, response)
	if err != nil {
		return nil, err
	}

	TickDatadogGet("on-page-summary")

	return resp, nil
}

// parseOnPageSummaryResponse parses the response from the on_page summary API
func parseOnPageSummaryResponse(ctx context.Context, b []byte) (*OnPageSummaryResponse, error) {
	var response *OnPageSummaryResponse

	// Log attempt
	logging.Infof(ctx, "[OnPageSummary] Attempting to parse response of %d bytes", len(b))

	err := json.Unmarshal(b, &response)
	if err != nil {
		logging.Errorf(ctx, "[OnPageSummary] JSON unmarshalling failed: %v", err)
		logging.Errorf(ctx, "[OnPageSummary] Response body that failed to parse: %s", string(b))

		// Provide specific guidance for common type mismatches
		if strings.Contains(err.Error(), "cannot unmarshal") {
			logging.Errorf(ctx, "[OnPageSummary] Type mismatch detected. Check field types in struct definition.")
		}
		if strings.Contains(err.Error(), "unexpected end") {
			logging.Errorf(ctx, "[OnPageSummary] Incomplete JSON response detected.")
		}
		if strings.Contains(err.Error(), "invalid character") {
			logging.Errorf(ctx, "[OnPageSummary] Invalid JSON syntax detected.")
		}

		return nil, verrors.FromError(err)
	}

	// Validate the parsed structure
	if response == nil {
		return nil, verrors.New(verrors.InvalidArgument, "parsed response is nil")
	}

	// Check for error status codes
	if response.StatusCode >= 40000 {
		logging.Errorf(ctx, "[OnPageSummary] API returned error status code: %d", response.StatusCode)
	}

	logging.Infof(ctx, "[OnPageSummary] Successfully parsed response")
	return response, nil
}

// OnPagePagesRequest represents the request structure for on_page pages
type OnPagePagesRequest struct {
	ID    string `json:"id"`
	Limit int    `json:"limit"`
}

// OnPagePagesResponse represents the response from on_page pages
type OnPagePagesResponse struct {
	Version       string             `json:"version"`
	StatusCode    int64              `json:"status_code"`
	StatusMessage string             `json:"status_message"`
	Time          string             `json:"time"`
	Cost          float64            `json:"cost"`
	TasksCount    int64              `json:"tasks_count"`
	TasksError    int64              `json:"tasks_error"`
	Tasks         []*OnPagePagesTask `json:"tasks"`
}

// OnPagePagesTask represents a single task in the pages response
type OnPagePagesTask struct {
	ID            string               `json:"id"`
	StatusCode    int64                `json:"status_code"`
	StatusMessage string               `json:"status_message"`
	Time          string               `json:"time"`
	Cost          float64              `json:"cost"`
	ResultCount   int64                `json:"result_count"`
	Path          []string             `json:"path"`
	Data          *OnPagePagesData     `json:"data"`
	Result        []*OnPagePagesResult `json:"result"`
}

// OnPagePagesData represents the data field in the pages response
type OnPagePagesData struct {
	API              string        `json:"api"`
	Function         string        `json:"function"`
	Filters          []interface{} `json:"filters,omitempty"`
	Limit            int           `json:"limit"`
	Target           string        `json:"target"`
	MaxCrawlPages    int           `json:"max_crawl_pages"`
	LoadResources    bool          `json:"load_resources"`
	StoreRawHTML     bool          `json:"store_raw_html"`
	EnableJavascript bool          `json:"enable_javascript,omitempty"`
	SupportCookies   bool          `json:"support_cookies,omitempty"`
	Tag              string        `json:"tag,omitempty"`
	CustomUserAgent  string        `json:"custom_user_agent,omitempty"`
}

// OnPagePagesResult represents a single result in the pages response
type OnPagePagesResult struct {
	CrawlProgress    string             `json:"crawl_progress"`
	CrawlStatus      *OnPageCrawlStatus `json:"crawl_status"`
	SearchAfterToken *string            `json:"search_after_token"`
	CurrentOffset    int64              `json:"current_offset"`
	TotalItemsCount  int64              `json:"total_items_count"`
	ItemsCount       int64              `json:"items_count"`
	Items            []*OnPagePageItem  `json:"items"`
}

// OnPagePageItem represents a single page item in the pages response
type OnPagePageItem struct {
	ResourceType            string                `json:"resource_type"`
	StatusCode              int64                 `json:"status_code"`
	Location                *string               `json:"location"`
	URL                     string                `json:"url"`
	Meta                    *OnPagePageMeta       `json:"meta"`
	PageTiming              *OnPagePageTiming     `json:"page_timing"`
	OnpageScore             float64               `json:"onpage_score"`
	TotalDomSize            int64                 `json:"total_dom_size"`
	CustomJSResponse        *string               `json:"custom_js_response"`
	CustomJSClientException *string               `json:"custom_js_client_exception"`
	ResourceErrors          *OnPageResourceErrors `json:"resource_errors"`
	BrokenResources         bool                  `json:"broken_resources"`
	BrokenLinks             bool                  `json:"broken_links"`
	DuplicateTitle          bool                  `json:"duplicate_title"`
	DuplicateDescription    bool                  `json:"duplicate_description"`
	DuplicateContent        bool                  `json:"duplicate_content"`
	ClickDepth              int64                 `json:"click_depth"`
	Size                    int64                 `json:"size"`
	EncodedSize             int64                 `json:"encoded_size"`
	TotalTransferSize       int64                 `json:"total_transfer_size"`
	FetchTime               string                `json:"fetch_time"`
	CacheControl            *OnPageCacheControl   `json:"cache_control"`
	Checks                  *OnPagePageChecks     `json:"checks"`
	ContentEncoding         string                `json:"content_encoding"`
	MediaType               string                `json:"media_type"`
	Server                  string                `json:"server"`
	IsResource              bool                  `json:"is_resource"`
	URLLength               int64                 `json:"url_length"`
	RelativeURLLength       int64                 `json:"relative_url_length"`
	LastModified            *OnPageLastModified   `json:"last_modified"`
}

// OnPagePageMeta represents the meta information for a page
type OnPagePageMeta struct {
	Title                          string            `json:"title"`
	Charset                        int64             `json:"charset"`
	Follow                         bool              `json:"follow"`
	Generator                      *string           `json:"generator"`
	HTags                          *OnPageHTags      `json:"htags"`
	Description                    string            `json:"description"`
	Favicon                        string            `json:"favicon"`
	MetaKeywords                   *string           `json:"meta_keywords"`
	Canonical                      *string           `json:"canonical"`
	InternalLinksCount             int64             `json:"internal_links_count"`
	ExternalLinksCount             int64             `json:"external_links_count"`
	InboundLinksCount              int64             `json:"inbound_links_count"`
	ImagesCount                    int64             `json:"images_count"`
	ImagesSize                     int64             `json:"images_size"`
	ScriptsCount                   int64             `json:"scripts_count"`
	ScriptsSize                    int64             `json:"scripts_size"`
	StylesheetsCount               int64             `json:"stylesheets_count"`
	StylesheetsSize                int64             `json:"stylesheets_size"`
	TitleLength                    int64             `json:"title_length"`
	DescriptionLength              int64             `json:"description_length"`
	RenderBlockingScriptsCount     int64             `json:"render_blocking_scripts_count"`
	RenderBlockingStylesheetsCount int64             `json:"render_blocking_stylesheets_count"`
	CumulativeLayoutShift          float64           `json:"cumulative_layout_shift"`
	MetaTitle                      string            `json:"meta_title"`
	Content                        *OnPageContent    `json:"content"`
	DeprecatedTags                 []string          `json:"deprecated_tags"`
	DuplicateMetaTags              []string          `json:"duplicate_meta_tags"`
	Spell                          []string          `json:"spell"`
	SocialMediaTags                map[string]string `json:"social_media_tags"`
}

// OnPageHTags represents the heading tags for a page
type OnPageHTags struct {
	H1 []string `json:"h1"`
	H2 []string `json:"h2"`
	H3 []string `json:"h3"`
	H4 []string `json:"h4"`
	H5 []string `json:"h5"`
	H6 []string `json:"h6"`
}

// OnPageContent represents the content analysis for a page
type OnPageContent struct {
	PlainTextSize                    int64    `json:"plain_text_size"`
	PlainTextRate                    float64  `json:"plain_text_rate"`
	PlainTextWordCount               int64    `json:"plain_text_word_count"`
	AutomatedReadabilityIndex        float64  `json:"automated_readability_index"`
	ColemanLiauReadabilityIndex      float64  `json:"coleman_liau_readability_index"`
	DaleChallReadabilityIndex        float64  `json:"dale_chall_readability_index"`
	FleschKincaidReadabilityIndex    float64  `json:"flesch_kincaid_readability_index"`
	SmogReadabilityIndex             float64  `json:"smog_readability_index"`
	DescriptionToContentConsistency  float64  `json:"description_to_content_consistency"`
	TitleToContentConsistency        float64  `json:"title_to_content_consistency"`
	MetaKeywordsToContentConsistency *float64 `json:"meta_keywords_to_content_consistency"`
}

// OnPagePageTiming represents the page timing information
type OnPagePageTiming struct {
	TimeToInteractive      int64 `json:"time_to_interactive"`
	DomComplete            int64 `json:"dom_complete"`
	LargestContentfulPaint int64 `json:"largest_contentful_paint"`
	FirstInputDelay        int64 `json:"first_input_delay"`
	ConnectionTime         int64 `json:"connection_time"`
	TimeToSecureConnection int64 `json:"time_to_secure_connection"`
	RequestSentTime        int64 `json:"request_sent_time"`
	WaitingTime            int64 `json:"waiting_time"`
	DownloadTime           int64 `json:"download_time"`
	DurationTime           int64 `json:"duration_time"`
	FetchStart             int64 `json:"fetch_start"`
	FetchEnd               int64 `json:"fetch_end"`
}

// OnPageResourceErrors represents resource errors for a page
type OnPageResourceErrors struct {
	Errors   []*OnPageResourceError `json:"errors"`
	Warnings []*OnPageResourceError `json:"warnings"`
}

// OnPageResourceError represents a single resource error
type OnPageResourceError struct {
	Line       int64  `json:"line"`
	Column     int64  `json:"column"`
	Message    string `json:"message"`
	StatusCode int64  `json:"status_code"`
}

// OnPageLastModified represents the last modified information
type OnPageLastModified struct {
	Header  *string `json:"header"`
	Sitemap *string `json:"sitemap"`
	MetaTag *string `json:"meta_tag"`
}

// OnPageCacheControl represents cache control information
type OnPageCacheControl struct {
	Cachable bool  `json:"cachable"`
	TTL      int64 `json:"ttl"`
}

// OnPagePages calls the on_page pages endpoint for a specific task ID
func (d *Client) OnPagePages(ctx context.Context, requests []*OnPagePagesRequest, source RequestSource) (*OnPagePagesResponse, error) {
	if len(requests) == 0 {
		return nil, verrors.New(verrors.InvalidArgument, "at least one request is required")
	}

	p, err := json.Marshal(requests)
	if err != nil {
		return nil, verrors.FromError(err)
	}

	// For OnPagePages, we need to send the request as a POST with the task ID in the body
	body := bytes.NewBuffer(p)
	path := fmt.Sprintf("%s%s/pages", rootURL, OnPagePath)

	// Log request details for debugging
	logging.Infof(ctx, "[OnPagePages] Request URL: %s", path)
	logging.Infof(ctx, "[OnPagePages] Request Body: %s", string(p))
	logging.Infof(ctx, "[OnPagePages] Request Count: %d", len(requests))
	for i, req := range requests {
		logging.Infof(ctx, "[OnPagePages] Request[%d] ID: %s, Limit: %d", i, req.ID, req.Limit)
	}

	httpRequest, err := http.NewRequest(http.MethodPost, path, body)
	if err != nil {
		logging.Errorf(ctx, "[OnPagePages] Failed to create HTTP request: %v", err)
		return nil, verrors.FromError(err)
	}

	httpRequest.Header.Set("Content-Type", "application/json")

	response, err := d.Do(ctx, httpRequest, "on-page-pages", source)
	if err != nil {
		logging.Errorf(ctx, "[OnPagePages] API call failed: %v", err)
		return nil, err
	}

	// Log response details for debugging
	logging.Infof(ctx, "[OnPagePages] Response received, length: %d bytes", len(response))
	logging.Infof(ctx, "[OnPagePages] Response Body: %s", string(response))

	resp, err := parseOnPagePagesResponse(ctx, response)
	if err != nil {
		logging.Errorf(ctx, "[OnPagePages] Failed to parse response: %v", err)
		return nil, err
	}

	// Log parsed response details
	logging.Infof(ctx, "[OnPagePages] Parsed response - StatusCode: %d, StatusMessage: %s, TasksCount: %d, TasksError: %d",
		resp.StatusCode, resp.StatusMessage, resp.TasksCount, resp.TasksError)

	if len(resp.Tasks) > 0 {
		task := resp.Tasks[0]
		logging.Infof(ctx, "[OnPagePages] Task[0] - ID: %s, StatusCode: %d, StatusMessage: %s, ResultCount: %d",
			task.ID, task.StatusCode, task.StatusMessage, task.ResultCount)

		if len(task.Result) > 0 {
			result := task.Result[0]
			logging.Infof(ctx, "[OnPagePages] Result[0] - ItemsCount: %d, TotalItemsCount: %d, CrawlProgress: %s",
				result.ItemsCount, result.TotalItemsCount, result.CrawlProgress)

			if len(result.Items) > 0 {
				logging.Infof(ctx, "[OnPagePages] Found %d items in result", len(result.Items))
				for i, item := range result.Items {
					logging.Infof(ctx, "[OnPagePages] Item[%d] - URL: %s, StatusCode: %d, ResourceType: %s",
						i, item.URL, item.StatusCode, item.ResourceType)
				}
			}
		}
	}

	TickDatadogPost(resp.TasksCount, resp.Cost, "on-page-pages", source)

	return resp, nil
}

// parseOnPagePagesResponse parses the response from the on_page pages API
func parseOnPagePagesResponse(ctx context.Context, b []byte) (*OnPagePagesResponse, error) {
	var response *OnPagePagesResponse

	// Log attempt
	logging.Infof(ctx, "[OnPagePages] Attempting to parse response of %d bytes", len(b))

	err := json.Unmarshal(b, &response)
	if err != nil {
		logging.Errorf(ctx, "[OnPagePages] JSON unmarshalling failed: %v", err)
		logging.Errorf(ctx, "[OnPagePages] Response body that failed to parse: %s", string(b))

		// Provide specific guidance for common type mismatches
		if strings.Contains(err.Error(), "cannot unmarshal") {
			logging.Errorf(ctx, "[OnPagePages] Type mismatch detected. Check field types in struct definition.")
			if strings.Contains(err.Error(), "array into Go struct field") {
				logging.Errorf(ctx, "[OnPagePages] Array/List type mismatch - field expects string but received array. Check if field should be []string instead of *string.")
			}
			if strings.Contains(err.Error(), "bool into Go struct field") {
				logging.Errorf(ctx, "[OnPagePages] Boolean type mismatch - field expects int64 but received bool. Check if field should be bool instead of int64.")
			}
			if strings.Contains(err.Error(), "int64 into Go struct field") {
				logging.Errorf(ctx, "[OnPagePages] Integer type mismatch - field expects bool but received int64. Check if field should be int64 instead of bool.")
			}
		}
		if strings.Contains(err.Error(), "unexpected end") {
			logging.Errorf(ctx, "[OnPagePages] Incomplete JSON response detected.")
		}
		if strings.Contains(err.Error(), "invalid character") {
			logging.Errorf(ctx, "[OnPagePages] Invalid JSON syntax detected.")
		}

		return nil, verrors.FromError(err)
	}

	// Validate the parsed structure
	if response == nil {
		return nil, verrors.New(verrors.InvalidArgument, "parsed response is nil")
	}

	// Check for error status codes
	if response.StatusCode >= 40000 {
		logging.Errorf(ctx, "[OnPagePages] API returned error status code: %d", response.StatusCode)
	}

	// Check for empty tasks
	if len(response.Tasks) == 0 {
		logging.Errorf(ctx, "[OnPagePages] No tasks found in response")
	}

	// Log successful parsing
	logging.Infof(ctx, "[OnPagePages] Successfully parsed response with %d tasks", len(response.Tasks))

	return response, nil
}

// OnPageRawHTMLRequest represents the request structure for on_page raw_html
type OnPageRawHTMLRequest struct {
	ID  string `json:"id"`
	URL string `json:"url"`
}

// OnPageRawHTMLResponse represents the response from on_page raw_html
type OnPageRawHTMLResponse struct {
	Version       string               `json:"version"`
	StatusCode    int64                `json:"status_code"`
	StatusMessage string               `json:"status_message"`
	Time          string               `json:"time"`
	Cost          float64              `json:"cost"`
	TasksCount    int64                `json:"tasks_count"`
	TasksError    int64                `json:"tasks_error"`
	Tasks         []*OnPageRawHTMLTask `json:"tasks"`
}

// OnPageRawHTMLTask represents a single task in the raw_html response
type OnPageRawHTMLTask struct {
	ID            string                 `json:"id"`
	StatusCode    int64                  `json:"status_code"`
	StatusMessage string                 `json:"status_message"`
	Time          string                 `json:"time"`
	Cost          float64                `json:"cost"`
	ResultCount   int64                  `json:"result_count"`
	Path          []string               `json:"path"`
	Data          *OnPageRawHTMLData     `json:"data"`
	Result        []*OnPageRawHTMLResult `json:"result"`
}

// OnPageRawHTMLData represents the data field in the raw_html response
type OnPageRawHTMLData struct {
	API              string `json:"api"`
	Function         string `json:"function"`
	URL              string `json:"url"`
	Target           string `json:"target"`
	MaxCrawlPages    int    `json:"max_crawl_pages"`
	LoadResources    bool   `json:"load_resources"`
	StoreRawHTML     bool   `json:"store_raw_html"`
	EnableJavascript bool   `json:"enable_javascript,omitempty"`
	Tag              string `json:"tag,omitempty"`
	CustomUserAgent  string `json:"custom_user_agent,omitempty"`
}

// OnPageRawHTMLResult represents a single result in the raw_html response
type OnPageRawHTMLResult struct {
	CrawlProgress string              `json:"crawl_progress"`
	CrawlStatus   *OnPageCrawlStatus  `json:"crawl_status"`
	ItemsCount    int64               `json:"items_count"`
	Items         *OnPageRawHTMLItems `json:"items"`
}

// OnPageRawHTMLItems represents the items field in the raw_html result
type OnPageRawHTMLItems struct {
	HTML string `json:"html"`
}

// OnPageRawHTML calls the on_page raw_html endpoint for a specific URL
func (d *Client) OnPageRawHTML(ctx context.Context, requests []*OnPageRawHTMLRequest, source RequestSource) (*OnPageRawHTMLResponse, error) {
	if len(requests) == 0 {
		return nil, verrors.New(verrors.InvalidArgument, "at least one request is required")
	}

	p, err := json.Marshal(requests)
	if err != nil {
		return nil, verrors.FromError(err)
	}

	// For OnPageRawHTML, we need to send the request as a POST with the URL in the body
	body := bytes.NewBuffer(p)
	path := fmt.Sprintf("%s%s/raw_html", rootURL, OnPagePath)

	// Log request details for debugging
	logging.Infof(ctx, "[OnPageRawHTML] Request URL: %s", path)
	logging.Infof(ctx, "[OnPageRawHTML] Request Body: %s", string(p))
	logging.Infof(ctx, "[OnPageRawHTML] Request Count: %d", len(requests))
	for i, req := range requests {
		logging.Infof(ctx, "[OnPageRawHTML] Request[%d] ID: %s, URL: %s", i, req.ID, req.URL)
	}

	httpRequest, err := http.NewRequest(http.MethodPost, path, body)
	if err != nil {
		logging.Errorf(ctx, "[OnPageRawHTML] Failed to create HTTP request: %v", err)
		return nil, verrors.FromError(err)
	}

	httpRequest.Header.Set("Content-Type", "application/json")

	response, err := d.Do(ctx, httpRequest, "on-page-raw-html", source)
	if err != nil {
		logging.Errorf(ctx, "[OnPageRawHTML] API call failed: %v", err)
		return nil, err
	}

	// Log response details for debugging
	logging.Infof(ctx, "[OnPageRawHTML] Response received, length: %d bytes", len(response))
	logging.Infof(ctx, "[OnPageRawHTML] Response Body: %s", string(response))

	resp, err := parseOnPageRawHTMLResponse(ctx, response)
	if err != nil {
		logging.Errorf(ctx, "[OnPageRawHTML] Failed to parse response: %v", err)
		return nil, err
	}

	// Log parsed response details
	logging.Infof(ctx, "[OnPageRawHTML] Parsed response - StatusCode: %d, StatusMessage: %s, TasksCount: %d, TasksError: %d",
		resp.StatusCode, resp.StatusMessage, resp.TasksCount, resp.TasksError)

	if len(resp.Tasks) > 0 {
		task := resp.Tasks[0]
		logging.Infof(ctx, "[OnPageRawHTML] Task[0] - ID: %s, StatusCode: %d, StatusMessage: %s, ResultCount: %d",
			task.ID, task.StatusCode, task.StatusMessage, task.ResultCount)

		if len(task.Result) > 0 {
			result := task.Result[0]
			logging.Infof(ctx, "[OnPageRawHTML] Result[0] - ItemsCount: %d, CrawlProgress: %s",
				result.ItemsCount, result.CrawlProgress)

			if result.Items != nil {
				htmlLength := len(result.Items.HTML)
				logging.Infof(ctx, "[OnPageRawHTML] Raw HTML received, length: %d characters", htmlLength)
			}
		}
	}

	TickDatadogPost(resp.TasksCount, resp.Cost, "on-page-raw-html", source)

	return resp, nil
}

// parseOnPageRawHTMLResponse parses the response from the on_page raw_html API
func parseOnPageRawHTMLResponse(ctx context.Context, b []byte) (*OnPageRawHTMLResponse, error) {
	var response *OnPageRawHTMLResponse

	// Log attempt
	logging.Infof(ctx, "[OnPageRawHTML] Attempting to parse response of %d bytes", len(b))

	err := json.Unmarshal(b, &response)
	if err != nil {
		logging.Errorf(ctx, "[OnPageRawHTML] JSON unmarshalling failed: %v", err)
		logging.Errorf(ctx, "[OnPageRawHTML] Response body that failed to parse: %s", string(b))

		// Provide specific guidance for common type mismatches
		if strings.Contains(err.Error(), "cannot unmarshal") {
			logging.Errorf(ctx, "[OnPageRawHTML] Type mismatch detected. Check field types in struct definition.")
			if strings.Contains(err.Error(), "array into Go struct field") {
				logging.Errorf(ctx, "[OnPageRawHTML] Array/List type mismatch - field expects string but received array. Check if field should be []string instead of *string.")
			}
			if strings.Contains(err.Error(), "bool into Go struct field") {
				logging.Errorf(ctx, "[OnPageRawHTML] Boolean type mismatch - field expects int64 but received bool. Check if field should be bool instead of int64.")
			}
			if strings.Contains(err.Error(), "int64 into Go struct field") {
				logging.Errorf(ctx, "[OnPageRawHTML] Integer type mismatch - field expects bool but received int64. Check if field should be int64 instead of bool.")
			}
		}
		if strings.Contains(err.Error(), "unexpected end") {
			logging.Errorf(ctx, "[OnPageRawHTML] Incomplete JSON response detected.")
		}
		if strings.Contains(err.Error(), "invalid character") {
			logging.Errorf(ctx, "[OnPageRawHTML] Invalid JSON syntax detected.")
		}

		return nil, verrors.FromError(err)
	}

	// Validate the parsed structure
	if response == nil {
		return nil, verrors.New(verrors.InvalidArgument, "parsed response is nil")
	}

	// Check for error status codes
	if response.StatusCode >= 40000 {
		logging.Errorf(ctx, "[OnPageRawHTML] API returned error status code: %d", response.StatusCode)
	}

	// Check for empty tasks
	if len(response.Tasks) == 0 {
		logging.Errorf(ctx, "[OnPageRawHTML] No tasks found in response")
	}

	// Log successful parsing
	logging.Infof(ctx, "[OnPageRawHTML] Successfully parsed response with %d tasks", len(response.Tasks))

	return response, nil
}
