package dataforseo

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"regexp"
	"strings"
	"time"

	"github.com/vendasta/gosdks/basesdk/v2"
	"github.com/vendasta/gosdks/logging"
	"github.com/vendasta/gosdks/statsd"
	"github.com/vendasta/gosdks/verrors"
	"github.com/vendasta/listing-products/internal/constants"
	dataforseotaskservice "github.com/vendasta/listing-products/internal/dataforseo/task/service"
	"golang.org/x/time/rate"
)

type Path string

type RequestSource string

const (
	taskPostPath        = "/task_post"
	taskGetPath         = "/task_get"
	taskReadyPath       = "/tasks_ready"
	livePath            = "/live"
	taskGetRegularPath  = "/task_get/regular"
	taskGetAdvancedPath = "/task_get/advanced"

	GoogleAdsStatusPath           Path = "/keywords_data/google_ads/status"
	GoogleMapsSerpPath            Path = "/serp/google/maps"
	GoogleOrganicSerpPath         Path = "/serp/google/organic"
	GoogleKeywordSearchVolumePath Path = "/keywords_data/google_ads/search_volume"
	GoogleKeywordsForSitePath     Path = "/keywords_data/google_ads/keywords_for_site"
	locationGetPath               Path = "/keywords_data/google_ads/locations"
	rootURL                            = "https://api.dataforseo.com/v3"

	RequestSourceFreeKeywordTracking RequestSource = "free_keyword_tracking"
	RequestSourcePaidKeywordTracking RequestSource = "paid_keyword_tracking"
	RequestSourceCategoryWorkflow    RequestSource = "category_workflow"
	RequestSourceCitations           RequestSource = "citations"
)

func TickDatadogPost(tasks int64, cost float64, request string, source RequestSource) {
	statsd.Count(constants.DataForSEOTaskPostMetric, tasks, []string{
		fmt.Sprintf("request:%s", request), fmt.Sprintf("source:%s", source),
	}, 1)
	statsd.Count(constants.DataForSEOCostMetric, int64(float64(cost*10000)), []string{
		fmt.Sprintf("request:%s", request), fmt.Sprintf("source:%s", source),
	}, 1)
}

func TickDatadogGet(request string) {
	statsd.Incr(constants.DataForSEOTaskGetMetric, []string{
		fmt.Sprintf("request:%s", request),
	}, 1)
}

func HandleUnexpectedError(ctx context.Context, statusCode int, request string, source RequestSource, body []byte) error {
	tags := []string{
		fmt.Sprintf("request:%s", request), fmt.Sprintf("status_code:%d", statusCode),
	}
	if source != "" {
		tags = append(tags, fmt.Sprintf("source:%s", source))
	}

	statsd.Incr(constants.DataForSEOUnexpectedErrorMetric, tags, 1)
	logging.Errorf(ctx, "unexpected dataforseo error calling %s, status code: %d: %s", request, statusCode, body)
	return verrors.New(verrors.Internal, "unexpected dataforseo error calling %s, status code: %d, %s", request, statusCode, body)
}

func (d *Client) Do(ctx context.Context, req *http.Request, request string, source RequestSource) ([]byte, error) {
	d.apiAuth.SignRequest(req)
	response, err := d.httpClient.Do(req)
	if err != nil {
		logging.Errorf(ctx, "error calling %s, err: %s", req.URL, err.Error())
		return nil, verrors.FromError(err)
	}
	defer response.Body.Close()
	body, err := io.ReadAll(response.Body)

	// DataForSEO returns 200 even if there is an error in the request, so higher status codes could indicate an outage
	if response.StatusCode >= 400 {
		return nil, HandleUnexpectedError(ctx, response.StatusCode, request, source, body)
	}

	return body, err
}

// InvalidKeywordCharactersRegex includes all invalid keyword characters that should be removed: https://dataforseo.com/help-center/using-symbols-in-keywords-when-setting-a-google-ads-task
var InvalidKeywordCharactersRegex = regexp.MustCompile(`[\x{0000}-\x{001F}]|[\x{0021}]|[\x{0025}]|[\x{0028}-\x{002A}]|[\x{002C}]|[\x{003B}-\x{0040}]|[\x{005C}]|[\x{005E}]|[\x{0060}]|[\x{007B}-\x{009F}]|[\x{00A1}-\x{00A2}]|[\x{00A4}-\x{00A9}]|[\x{00AB}-\x{00B4}]|[\x{00B6}]|[\x{00B8}-\x{00B9}]|[\x{00BB}-\x{00BF}]|[\x{00D7}]|[\x{00F7}]|[\x{0250}-\x{0258}]|[\x{025A}-\x{02AF}]|[\x{02C2}-\x{02C5}]|[\x{02D2}-\x{02DF}]|[\x{02E5}-\x{02EB}]|[\x{02ED}]|[\x{02EF}-\x{02FF}]|[\x{0375}]|[\x{037E}]|[\x{0384}-\x{0385}]|[\x{0387}]|[\x{03F6}]|[\x{0482}]|[\x{0488}-\x{0489}]|[\x{055A}-\x{0560}]|[\x{0588}-\x{058F}]|[\x{05BE}]|[\x{05C0}]|[\x{05C3}]|[\x{05C6}]|[\x{05EF}]|[\x{05F3}-\x{060F}]|[\x{061B}-\x{061F}]|[\x{066A}-\x{066D}]|[\x{06D4}]|[\x{06DD}-\x{06DE}]|[\x{06E9}]|[\x{06FD}-\x{06FE}]|[\x{0700}-\x{070F}]|[\x{07F6}-\x{07F9}]|[\x{07FD}-\x{07FF}]|[\x{0830}-\x{083E}]|[\x{085E}]|[\x{0870}-\x{089F}]|[\x{08B5}]|[\x{08BE}-\x{08D3}]|[\x{08E2}]|[\x{0964}-\x{0965}]|[\x{0970}]|[\x{09F2}-\x{09FB}]|[\x{09FD}-\x{09FE}]|[\x{0A76}]|[\x{0AF0}-\x{0AF1}]|[\x{0B55}]|[\x{0B70}]|[\x{0B72}-\x{0B77}]|[\x{0BF0}-\x{0BFA}]|[\x{0C04}]|[\x{0C3C}]|[\x{0C5D}]|[\x{0C77}-\x{0C7F}]|[\x{0C84}]|[\x{0CDD}]|[\x{0D04}]|[\x{0D4F}]|[\x{0D58}-\x{0D5E}]|[\x{0D70}-\x{0D79}]|[\x{0D81}]|[\x{0DF4}]|[\x{0E3F}]|[\x{0E4F}]|[\x{0E5A}-\x{0E5B}]|[\x{0E86}]|[\x{0E89}]|[\x{0E8C}]|[\x{0E8E}-\x{0E93}]|[\x{0E98}]|[\x{0EA0}]|[\x{0EA8}-\x{0EA9}]|[\x{0EAC}]|[\x{0EBA}]|[\x{0F01}-\x{0F17}]|[\x{0F1A}-\x{0F1F}]|[\x{0F2A}-\x{0F34}]|[\x{0F36}]|[\x{0F38}]|[\x{0F3A}-\x{0F3D}]|[\x{0F85}]|[\x{0FBE}-\x{0FC5}]|[\x{0FC7}-\x{0FDA}]|[\x{104A}-\x{104F}]|[\x{109E}-\x{109F}]|[\x{10FB}]|[\x{1360}-\x{137C}]|[\x{1390}-\x{1399}]|[\x{1400}]|[\x{166D}-\x{166E}]|[\x{169B}-\x{169C}]|[\x{16EB}-\x{16ED}]|[\x{170D}]|[\x{1715}-\x{171F}]|[\x{1735}-\x{1736}]|[\x{17D4}-\x{17D6}]|[\x{17D8}-\x{17DB}]|[\x{17F0}-\x{180A}]|[\x{180E}-\x{180F}]|[\x{1878}]|[\x{1940}-\x{1945}]|[\x{19DA}-\x{19FF}]|[\x{1A1E}-\x{1A1F}]|[\x{1AA0}-\x{1AA6}]|[\x{1AA8}-\x{1AAD}]|[\x{1ABE}-\x{1ACE}]|[\x{1B4C}]|[\x{1B5A}-\x{1B6A}]|[\x{1B74}-\x{1B7E}]|[\x{1BFC}-\x{1BFF}]|[\x{1C3B}-\x{1C3F}]|[\x{1C7E}-\x{1C7F}]|[\x{1C90}-\x{1CC7}]|[\x{1CD3}]|[\x{1CFA}]|[\x{1DFA}]|[\x{1FBD}]|[\x{1FBF}-\x{1FC1}]|[\x{1FCD}-\x{1FCF}]|[\x{1FDD}-\x{1FDF}]|[\x{1FED}-\x{1FEF}]|[\x{1FFD}-\x{1FFE}]|[\x{200B}-\x{2027}]|[\x{202A}-\x{202E}]|[\x{2030}-\x{203E}]|[\x{2041}-\x{2053}]|[\x{2055}-\x{205E}]|[\x{2060}-\x{2070}]|[\x{2074}-\x{207E}]|[\x{2080}-\x{208E}]|[\x{20A0}-\x{20AB}]|[\x{20AD}-\x{20C0}]|[\x{20DD}-\x{20E0}]|[\x{20E2}-\x{20E4}]|[\x{2100}-\x{2101}]|[\x{2103}-\x{2106}]|[\x{2108}-\x{2109}]|[\x{2114}]|[\x{2116}-\x{2118}]|[\x{211E}-\x{2123}]|[\x{2125}]|[\x{2127}]|[\x{2129}]|[\x{212E}]|[\x{213A}-\x{213B}]|[\x{2140}-\x{2144}]|[\x{214A}-\x{214D}]|[\x{214F}-\x{2169}]|[\x{2170}-\x{2179}]|[\x{2189}-\x{2BFF}]|[\x{2C2F}]|[\x{2C5F}]|[\x{2CE5}-\x{2CEA}]|[\x{2CF9}-\x{2CFF}]|[\x{2D70}]|[\x{2E00}-\x{2E2E}]|[\x{2E30}-\x{2FFB}]|[\x{3001}-\x{3004}]|[\x{3006}-\x{3020}]|[\x{3030}]|[\x{3036}-\x{3037}]|[\x{303D}-\x{303F}]|[\x{309B}-\x{309C}]|[\x{30A0}]|[\x{30FD}-\x{30FE}]|[\x{312F}]|[\x{3190}-\x{319F}]|[\x{31BB}-\x{31E3}]|[\x{3200}-\x{33FF}]|[\x{4DBF}-\x{4DFF}]|[\x{4E28}]|[\x{4EDD}]|[\x{4F00}]|[\x{4F03}]|[\x{4F39}]|[\x{4F56}]|[\x{4F92}]|[\x{4F94}]|[\x{4F9A}]|[\x{4FC9}]|[\x{4FFF}]|[\x{5040}]|[\x{5042}]|[\x{5046}]|[\x{5094}]|[\x{50D8}]|[\x{50F4}]|[\x{514A}]|[\x{5164}]|[\x{519D}]|[\x{51BE}]|[\x{51EC}]|[\x{529C}]|[\x{52AF}]|[\x{5307}]|[\x{5324}]|[\x{53DD}]|[\x{548A}]|[\x{54FF}]|[\x{5759}]|[\x{5765}]|[\x{57AC}]|[\x{57C7}-\x{57C8}]|[\x{58B2}]|[\x{590B}]|[\x{595B}]|[\x{595D}]|[\x{5963}]|[\x{5CA6}]|[\x{5CF5}]|[\x{5D42}]|[\x{5D53}]|[\x{5DD0}]|[\x{5F21}]|[\x{5F34}]|[\x{5F45}]|[\x{608A}]|[\x{60DE}]|[\x{6111}]|[\x{6130}]|[\x{6198}]|[\x{6213}]|[\x{62A6}]|[\x{63F5}]|[\x{6460}]|[\x{649D}]|[\x{661E}]|[\x{6624}]|[\x{662E}]|[\x{6659}]|[\x{6699}]|[\x{66A0}]|[\x{66B2}]|[\x{66BF}]|[\x{66FA}-\x{66FB}]|[\x{670E}]|[\x{6766}]|[\x{6801}]|[\x{6852}]|[\x{68C8}]|[\x{68CF}]|[\x{6998}]|[\x{6A30}]|[\x{6A46}]|[\x{6A73}]|[\x{6A7E}]|[\x{6AE2}]|[\x{6AE4}]|[\x{6C6F}]|[\x{6C86}]|[\x{6D96}]|[\x{6DCF}]|[\x{6DF2}]|[\x{6EBF}]|[\x{6FB5}]|[\x{7007}]|[\x{7104}]|[\x{710F}]|[\x{7146}]|[\x{72B1}]|[\x{72BE}]|[\x{7324}]|[\x{73BD}]|[\x{73F5}]|[\x{7429}]|[\x{769C}]|[\x{7821}]|[\x{7864}]|[\x{7994}]|[\x{799B}]|[\x{7AE7}]|[\x{7B9E}]|[\x{7D48}]|[\x{7E8A}]|[\x{8362}]|[\x{837F}]|[\x{83F6}]|[\x{84DC}]|[\x{856B}]|[\x{8807}]|[\x{88F5}]|[\x{891C}]|[\x{8A37}]|[\x{8AA7}]|[\x{8ABE}]|[\x{8ADF}]|[\x{8B53}]|[\x{8B7F}]|[\x{8CF0}]|[\x{8D12}]|[\x{9067}]|[\x{91DA}]|[\x{91DE}]|[\x{91E4}]|[\x{91EE}]|[\x{9206}]|[\x{923C}]|[\x{924E}]|[\x{9259}]|[\x{9288}]|[\x{92A7}]|[\x{92D3}]|[\x{92D5}]|[\x{92D7}]|[\x{92E0}]|[\x{92E7}]|[\x{92FF}]|[\x{9302}]|[\x{931D}]|[\x{9325}]|[\x{93A4}]|[\x{93C6}]|[\x{93F8}]|[\x{9431}]|[\x{9445}]|[\x{969D}]|[\x{96AF}]|[\x{9733}]|[\x{9743}]|[\x{974D}]|[\x{974F}]|[\x{9755}]|[\x{9857}]|[\x{9927}]|[\x{9ADC}]|[\x{9B72}]|[\x{9B75}]|[\x{9B8F}]|[\x{9BB1}]|[\x{9BBB}]|[\x{9C00}]|[\x{9E19}]|[\x{9FEB}-\x{9FFF}]|[\x{A490}-\x{A4C6}]|[\x{A4FE}-\x{A4FF}]|[\x{A60D}-\x{A60F}]|[\x{A670}-\x{A673}]|[\x{A67E}]|[\x{A6F2}-\x{A716}]|[\x{A720}-\x{A721}]|[\x{A789}-\x{A78A}]|[\x{A7AF}]|[\x{A7B8}-\x{A7F6}]|[\x{A828}-\x{A839}]|[\x{A874}-\x{A877}]|[\x{A8CE}-\x{A8CF}]|[\x{A8F8}-\x{A8FA}]|[\x{A8FC}]|[\x{A8FE}-\x{A8FF}]|[\x{A92E}-\x{A92F}]|[\x{A95F}]|[\x{A9C1}-\x{A9CD}]|[\x{A9DE}-\x{A9DF}]|[\x{AA5C}-\x{AA5F}]|[\x{AA77}-\x{AA79}]|[\x{AADE}-\x{AADF}]|[\x{AAF0}-\x{AAF1}]|[\x{AB5B}]|[\x{AB66}-\x{AB6B}]|[\x{ABEB}]|[\x{E000}-\x{F8FF}]|[\x{FA0E}-\x{FA0F}]|[\x{FA11}]|[\x{FA13}-\x{FA15}]|[\x{FA1F}-\x{FA21}]|[\x{FA23}-\x{FA24}]|[\x{FA27}-\x{FA29}]|[\x{FB00}-\x{FB06}]|[\x{FB29}]|[\x{FBB2}-\x{FBC2}]|[\x{FD3E}-\x{FD4F}]|[\x{FDCF}]|[\x{FDFC}-\x{FDFF}]|[\x{FE10}-\x{FE19}]|[\x{FE30}-\x{FE32}]|[\x{FE35}-\x{FE4C}]|[\x{FE50}-\x{FE6B}]|[\x{FEFF}-\x{FF05}]|[\x{FF07}-\x{FF0A}]|[\x{FF0C}-\x{FF0F}]|[\x{FF1A}-\x{FF20}]|[\x{FF3B}-\x{FF3E}]|[\x{FF40}]|[\x{FF5B}-\x{FFFF}]`)

type API string

type Device string

const (
	Mobile  Device = "mobile"
	Desktop Device = "desktop"
)

type Priority int

const (
	Normal Priority = 1
	High   Priority = 2
)

type Client struct {
	basesdkClient       basesdk.SDKClient
	httpClient          basesdk.HTTPClient
	apiAuth             apiKeyAuth
	taskService         dataforseotaskservice.Service
	serpPostRateLimiter *rate.Limiter
}

//go:generate mockgen -destination mock_client.go -source=client.go -package=dataforseo
type SERPClient interface {
	GoogleAdsStatusTaskGet(ctx context.Context) (*GoogleAdsStatusResponse, error)
	GoogleMapsSERPTaskPost(ctx context.Context, date string, ignoreDataLakeResults bool, requests []*GoogleMapsSERPTaskPostRequest, source RequestSource, ro ...RequestOpts) (*GoogleMapsSERPResponse, error)
	GoogleMapsSERPTaskGet(ctx context.Context, businessID string, keyword string, vicinity string, date string, taskID string, ignoreDataLakeResults bool) (*GoogleMapsSERPResponse, error)
	GoogleOrganicSERPTaskGet(ctx context.Context, businessID string, keyword string, vicinity string, date string, taskID string, ignoreDataLakeResults bool) (*GoogleOrganicSERPResponse, error)
	GoogleOrganicSERPTaskPost(ctx context.Context, date string, ignoreDataLakeResults bool, requests []*GoogleOrganicSERPTaskPostRequest, source RequestSource, ro ...RequestOpts) (*GoogleOrganicSERPResponse, error)
	GoogleKeywordVolumeTaskPost(ctx context.Context, request *GoogleKeywordVolumeTaskPostRequest, source RequestSource) (*GoogleKeywordVolumeResponse, error)
	GoogleKeywordVolumeTaskGet(ctx context.Context, taskID string) (*GoogleKeywordVolumeResponse, error)
	GetLocations(ctx context.Context) ([]*Location, error)
	GoogleKeywordsForSiteLivePost(ctx context.Context, date string, requests []*GoogleKeywordsForSiteRequest, source RequestSource) (*GoogleKeywordsForSiteResponse, error)
	GoogleKeywordsForSiteTaskPost(ctx context.Context, date string, requests []*GoogleKeywordsForSiteRequest, source RequestSource) (*GoogleKeywordsForSiteResponse, error)
	GoogleKeywordsForSiteTaskGet(ctx context.Context, businessID string, date string, taskID string) (*GoogleKeywordsForSiteResponse, error)
	OnPageTaskPost(ctx context.Context, requests []*OnPageTaskPostRequest, source RequestSource) (*OnPageResponse, error)
	OnPageTasksReady(ctx context.Context, source RequestSource) (*OnPageTasksReadyResponse, error)
	OnPageSummary(ctx context.Context, taskID string, source RequestSource) (*OnPageSummaryResponse, error)
	OnPagePages(ctx context.Context, requests []*OnPagePagesRequest, source RequestSource) (*OnPagePagesResponse, error)
	OnPageRawHTML(ctx context.Context, requests []*OnPageRawHTMLRequest, source RequestSource) (*OnPageRawHTMLResponse, error)
}

type requestOptions struct {
	device         Device
	priority       Priority
	depth          int
	searchPlaces   bool
	searchParams   string
	ignoreDataLake bool
}

func defaultRequestOptions() *requestOptions {
	return &requestOptions{
		device:       Mobile,
		priority:     Normal,
		depth:        100,
		searchPlaces: true,
		searchParams: "safesearch=on",
	}
}

// RequestOpts configures the requestOpts for making a dataforseo api call
type RequestOpts func(o *requestOptions)

// SetDevice sets the device to perform the search on
func SetDevice(device Device) RequestOpts {
	return func(ro *requestOptions) {
		ro.device = device
	}
}

// SetPriority sets the priority execution of the request
func SetPriority(priority Priority) RequestOpts {
	return func(ro *requestOptions) {
		ro.priority = priority
	}
}

// SetDepth sets the maximum amount of results to return
func SetDepth(depth int) RequestOpts {
	return func(ro *requestOptions) {
		ro.depth = depth
	}
}

// SetSearchPlaces sets whether to expand searches even if there are no results
func SetSearchPlaces(searchPlaces bool) RequestOpts {
	return func(ro *requestOptions) {
		ro.searchPlaces = searchPlaces
	}
}

func eventsPerMinute(eventCount int) rate.Limit {
	return rate.Every(time.Minute / time.Duration(eventCount))
}

// NewClient creates a dataforseo client
func NewClient(apikey string, taskService dataforseotaskservice.Service) *Client {
	auth := apiKeyAuth{apiKey: apikey}
	baseClient := basesdk.BaseClient{Authorization: auth, RootURL: rootURL}
	return &Client{
		basesdkClient:       baseClient,
		httpClient:          &http.Client{Timeout: 30 * time.Second},
		apiAuth:             auth,
		taskService:         taskService,
		serpPostRateLimiter: rate.NewLimiter(eventsPerMinute(20), 1),
	}
}

type apiKeyAuth struct {
	apiKey string
}

// SignRequest takes in an http request pointer and adds the key to the header
func (a apiKeyAuth) SignRequest(in *http.Request) {
	in.Header.Set("Authorization", fmt.Sprintf("Basic %s", []byte(a.apiKey)))
}

func MakeTag(businessID, keyword, vicinity string, tags ...string) string {
	tag := fmt.Sprintf("%s:%s:%s", businessID, keyword, vicinity)
	if len(tags) > 0 {
		tag = fmt.Sprintf("%s:%s", tag, strings.Join(tags, ":"))
	}
	return tag
}

func ParseTag(tag string) (businessID, keyword string, vicinity string, tags []string) {
	parts := strings.Split(tag, ":")
	if len(parts) < 3 {
		return "", "", "", nil
	}
	if len(parts) > 3 {
		tags = parts[3:]
	}

	return parts[0], parts[1], parts[2], tags
}
