{"info": {"name": "listing-products", "description": "APIs for listing-products", "schema": "https://schema.getpostman.com/json/collection/v2.1.0"}, "item": [{"name": "listing_products.v1.ListingProductsService", "description": "", "variable": [], "item": [{"name": "GetGoogleMyBusinessInsightsData", "description": "GetGoogleMyBusinessInsightsData gets Google My Business insights for a business ", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.ListingProductsService/GetGoogleMyBusinessInsightsData", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"businessId\": \"\",\n  \"startDate\": {\n    \"seconds\": 0,\n    \"nanos\": 0\n  },\n  \"endDate\": {\n    \"seconds\": 0,\n    \"nanos\": 0\n  }\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GetGoogleMyBusinessInsightsDataBucketed", "description": "GetGoogleMyBusinessInsightsDataBucketed gets bucketed Google My Business insights for a business ", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.ListingProductsService/GetGoogleMyBusinessInsightsDataBucketed", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"businessId\": \"\",\n  \"startDate\": {\n    \"seconds\": 0,\n    \"nanos\": 0\n  },\n  \"endDate\": {\n    \"seconds\": 0,\n    \"nanos\": 0\n  }\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "UpdateVendorSyncFlag", "description": "UpdateVendorSyncFlag sets the syncing property for a Vendor such as Bing or Apple. ", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.ListingProductsService/UpdateVendorSyncFlag", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"businessId\": \"\",\n  \"vendorId\": \"\",\n  \"syncing\": false\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "SubmitTurboLister", "description": "SubmitTurboLister initiates a workflow to submit listing data for a specified vendor This endpoint is async and returns success when the task has been added to the Cadence workflow list. ", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.ListingProductsService/SubmitTurboLister", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"businessId\": \"\",\n  \"vendorId\": \"\",\n  \"force\": false\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GetAddonAttributes", "description": "GetAddonAttributes fetches Addon attributes using the AddonID in request ", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.ListingProductsService/GetAddonAttributes", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"addonId\": \"\"\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "CreateAddonAttributes", "description": "CreateAddonAttributes creates new record for Addon attributes ", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.ListingProductsService/CreateAddonAttributes", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"addonId\": \"\",\n  \"addonAttributes\": {\n    \"addonType\": \"ADDON_TYPE_UNSPECIFIED\",\n    \"providerType\": \"PROVIDER_TYPE_UNSPECIFIED\",\n    \"providerPlanId\": \"\",\n    \"sourceId\": 0\n  }\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "UpdateAddonAttributes", "description": "UpdateAddonAttributes updates existing record for Addon attributes ", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.ListingProductsService/UpdateAddonAttributes", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"addonId\": \"\",\n  \"addonAttributes\": {\n    \"addonType\": \"ADDON_TYPE_UNSPECIFIED\",\n    \"providerType\": \"PROVIDER_TYPE_UNSPECIFIED\",\n    \"providerPlanId\": \"\",\n    \"sourceId\": 0\n  },\n  \"fieldMask\": {\n    \"paths\": [\n      null\n    ]\n  }\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "DeleteAddonAttributes", "description": "DeleteAddonAttributes deletes existing record for Addon attributes ", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.ListingProductsService/DeleteAddonAttributes", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"addonId\": \"\"\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GetMultiAddonAttributes", "description": "GetMultiAddonAttributes gets a list of Addon attributes based on included filters ", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.ListingProductsService/GetMultiAddonAttributes", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"addonIds\": [\n    null\n  ],\n  \"addonTypes\": [\n    \"ADDON_TYPE_UNSPECIFIED\"\n  ],\n  \"providerTypes\": [\n    \"PROVIDER_TYPE_UNSPECIFIED\"\n  ]\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "BulkConnectLocationsForGoogleUser", "description": "BulkConnectLocationsForGoogleUser will take all the Google Business Profile locations for the given Google user and attempt to connect them to account groups in the given partner. It will return a list of the connections made and the list of GBP locations that could not be connected. ", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.ListingProductsService/BulkConnectLocationsForGoogleUser", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"businessId\": [\n    null\n  ]\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "ManuallyFixYextProvisioning", "description": "Manual* IF YOU USE THESE ENDPOINTS IN CODE YOU WILL BREAK EVERYTHING AND LIKELY DIE ManuallyFixYextProvisioning bypasses all of our Marketplace duplicate activation checks and can make things much much worse. ManuallyFixYextProvisioning is a one off to fix a broken system state. Interactions with Yext should be done in the Yext microservice. This will look up the plan ID for the activation and pass the account to the Yext microservice to try to provision it again since the automated process is broken. Please try to fix the bug that resulted in the account getting into this state in addition to running this endpoint. ", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.ListingProductsService/ManuallyFixYextProvisioning", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"businessId\": \"\",\n  \"addonId\": \"\",\n  \"activationId\": \"\"\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "ManuallyProvisionUberall", "description": "ManuallyProvisionUberall bypasses all of our Marketplace duplicate activation checks and can make things much much worse. This will try to provision the account again since the automated process is broken. Please try to fix the bug that resulted in the account getting into this state in addition to running this endpoint. ", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.ListingProductsService/ManuallyProvisionUberall", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"businessId\": \"\",\n  \"addonId\": \"\",\n  \"activationId\": \"\"\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "ManuallyDeactivateUberallProvisioning", "description": "ManuallyDeactivateUberallProvisioning bypasses all standard operations regarding Uberall location cancelling and deactivation. It bypasses all Marketplace checks and immediately ends all service on an Uberall location. ", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.ListingProductsService/ManuallyDeactivateUberallProvisioning", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"businessId\": \"\"\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GetGoogleBusinessInfo", "description": "GetGoogleBusinessInfo is used for human Support on Demand interaction only. Google is very strict about how their data is used and it should only be pulled when a user or agent working on behalf of the user has taken action to request it. This endpoint pulls the business information directly from Google's API using the tokens stored in Core Services. ", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.ListingProductsService/GetGoogleBusinessInfo", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"businessId\": \"\"\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GetFacebookPageInfo", "description": "", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.ListingProductsService/GetFacebookPageInfo", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"businessId\": \"\"\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GetAppleBusinessConnectInfo", "description": "", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.ListingProductsService/GetAppleBusinessConnectInfo", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"businessId\": \"\"\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GetBingPlacesInfo", "description": "", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.ListingProductsService/GetBingPlacesInfo", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"businessId\": \"\"\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "TriggerBingInsightsBackFill", "description": "", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.ListingProductsService/TriggerBingInsightsBackFill", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"businessId\": \"\",\n  \"firstSuccessfulSync\": false,\n  \"startDate\": \"\",\n  \"endDate\": \"\"\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "CheckSubmissionStatus", "description": "CheckSubmissionStatus takes in business_id in request and using this business_id we fetch the latest data-axle submission_id for that business(account) from vtsore. The submission_id obtained is then used to fetch the latest submission status from data-axle. ", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.ListingProductsService/CheckSubmissionStatus", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"businessId\": \"\"\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GetInfoGroupId", "description": "GetInfoGroupId uses business_id and returns InfoGroupID from DataAxleLocation model if available ", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.ListingProductsService/GetInfoGroupId", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"businessId\": \"\"\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GetListingDistributionActivationStatus", "description": "GetListingDistributionActivationStatus gets the status of Listing Distribution for the account provided in the request ", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.ListingProductsService/GetListingDistributionActivationStatus", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"accountGroupId\": \"\"\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GetDirectSyncSourceInfo", "description": "GetDirectSyncSourceInfo returns information about the directly submitted sources for a given business ", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.ListingProductsService/GetDirectSyncSourceInfo", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"businessId\": \"\",\n  \"refresh\": false\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GetSyncData", "description": "GetDirectSyncSourceInfo returns information about the directly submitted sources for a given business ", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.ListingProductsService/GetSyncData", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"businessId\": \"\",\n  \"refresh\": false,\n  \"includeHidden\": false,\n  \"sourceIds\": [\n    null\n  ]\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GetSyndicationInfo", "description": "GetSyndicationInfo returns information about the last syndication workflows performed for a given business ", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.ListingProductsService/GetSyndicationInfo", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"businessId\": \"\",\n  \"vendorId\": \"\"\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GetProductSettings", "description": "GetProductSettings returns information about the partner settings and checks permissions for a given partnerID / marketID ", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.ListingProductsService/GetProductSettings", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"partnerId\": \"\",\n  \"marketId\": \"\"\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GetDoctorDotComCategories", "description": "GetDoctorDotComCategories returns the list of categories for Doctor.com ", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.ListingProductsService/GetDoctorDotComCategories", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}}, {"name": "FetchAndSubmitSourceURLsToCS", "description": "Deprecated: no more usage of this. ", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.ListingProductsService/FetchAndSubmitSourceURLsToCS", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"businessId\": \"\",\n  \"vendorIds\": [\n    null\n  ]\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "StartBusinessDataScoreWorkflow", "description": "StartBusinessDataScoreWorkflow starts a workflow to calculate the business data score for one or more businesses ", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.ListingProductsService/StartBusinessDataScoreWorkflow", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"businessIds\": [\n    null\n  ]\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "IsPartnerUser", "description": "IsPartnerUser checks if the user making the request is a partner ", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.ListingProductsService/IsPartnerUser", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"partnerId\": \"\"\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "IsLocalSEOProActiveForAccount", "description": "IsLocalSEOProActiveForAccount checks if the account has LSEO Pro active ", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.ListingProductsService/IsLocalSEOProActiveForAccount", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"accountGroupId\": \"\"\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GetLDData", "description": "GetLDData returns the DirectSyncSource information for Neustar, Data Axle, and Foursquare ", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.ListingProductsService/GetLDData", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"businessId\": \"\"\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "SyncToSources", "description": "", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.ListingProductsService/SyncToSources", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"businessId\": \"\",\n  \"trigger\": \"\"\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GetNeustarPublications", "description": "", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.ListingProductsService/GetNeustarPublications", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"businessId\": \"\"\n}", "options": {"raw": {"language": "json"}}}}}], "auth": {"type": "", "bearer": []}}, {"name": "listing_products.v1.ListingSourceService", "description": "", "variable": [], "item": [{"name": "GetListingSources", "description": "GetListingSources fetches all listing sources, optionally filtered by provider ", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.ListingSourceService/GetListingSources", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"provider\": \"PROVIDER_UNSPECIFIED\",\n  \"fieldMask\": {\n    \"paths\": [\n      null\n    ]\n  }\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GetListingSourceById", "description": "GetListingSourceById fetches details for a source based on its numeric ID ", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.ListingSourceService/GetListingSourceById", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"sourceId\": 0,\n  \"fieldMask\": {\n    \"paths\": [\n      null\n    ]\n  }\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GetListingSourceByProviderId", "description": "GetListingSourceByProviderId fetches details for a source based on its provider ID ", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.ListingSourceService/GetListingSourceByProviderId", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"provider\": \"PROVIDER_UNSPECIFIED\",\n  \"id\": \"\",\n  \"fieldMask\": {\n    \"paths\": [\n      null\n    ]\n  }\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GetPartnerSources", "description": "GetPartnerSources gets all sources that have not been disabled by the partner ", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.ListingSourceService/GetPartnerSources", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"partnerId\": \"\",\n  \"marketId\": \"\",\n  \"fieldMask\": {\n    \"paths\": [\n      null\n    ]\n  }\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "CreateListingSource", "description": "CreateListingSource creates a new source ", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.ListingSourceService/CreateListingSource", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"source\": {\n    \"adminNotes\": \"\",\n    \"createListingHtml\": \"\",\n    \"createListingUrl\": \"\",\n    \"claimListingHtml\": \"\",\n    \"descriptionHtml\": \"\",\n    \"domains\": [\n      null\n    ],\n    \"editListingHtml\": \"\",\n    \"editListingUrl\": \"\",\n    \"enabledFlag\": false,\n    \"hasListingsFlag\": false,\n    \"hasNoRatingsReviewsFlag\": false,\n    \"hasReviewsFlag\": false,\n    \"hasListingReporting\": false,\n    \"lspIcon\": \"\",\n    \"iconClass16px\": \"\",\n    \"iconClass32px\": \"\",\n    \"iconClass50px\": \"\",\n    \"iconUrl16px\": \"\",\n    \"iconUrl32px\": \"\",\n    \"iconUrl50px\": \"\",\n    \"isDirectlySubmittedSource\": false,\n    \"isLdSource\": false,\n    \"isLocationWhitelistedByDefaultFlag\": false,\n    \"isPartnerWhitelistedByDefaultFlag\": false,\n    \"isTaxonomyWhitelistedByDefaultFlag\": false,\n    \"ignoredAnchorDataMatches\": [\n      null\n    ],\n    \"locationBlacklist\": [\n      null\n    ],\n    \"locationSpecificOverrides\": {},\n    \"locationWhitelist\": [\n      null\n    ],\n    \"name\": \"\",\n    \"partnerBlacklist\": [\n      null\n    ],\n    \"partnerSpecificOverrides\": {},\n    \"partnerWhitelist\": [\n      null\n    ],\n    \"popularity\": 0,\n    \"sourceId\": 0,\n    \"sourceTypeId\": \"\",\n    \"sourceTypeName\": \"\",\n    \"submitListingHelpText\": \"\",\n    \"submitListingValidationRegex\": \"\",\n    \"supportsSubmittedListing\": false,\n    \"taxonomyBlacklist\": [\n      null\n    ],\n    \"taxonomyWhitelist\": [\n      null\n    ],\n    \"uberallId\": \"\",\n    \"yextId\": \"\",\n    \"availableForServiceAreaBusiness\": false,\n    \"syncRequirement\": \"SYNC_REQUIREMENT_UNSET\",\n    \"created\": {\n      \"seconds\": 0,\n      \"nanos\": 0\n    },\n    \"updated\": {\n      \"seconds\": 0,\n      \"nanos\": 0\n    },\n    \"deleted\": {\n      \"seconds\": 0,\n      \"nanos\": 0\n    }\n  }\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "DeleteListingSource", "description": "DeleteListingSource sets a source to deleted ", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.ListingSourceService/DeleteListingSource", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"sourceId\": 0\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "UndeleteListingSource", "description": "UndeleteListingSource restores a deleted source ", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.ListingSourceService/UndeleteListingSource", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"sourceId\": 0\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "UpdateListingSource", "description": "UpdateListingSource updates fields of a source ", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.ListingSourceService/UpdateListingSource", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"source\": {\n    \"adminNotes\": \"\",\n    \"createListingHtml\": \"\",\n    \"createListingUrl\": \"\",\n    \"claimListingHtml\": \"\",\n    \"descriptionHtml\": \"\",\n    \"domains\": [\n      null\n    ],\n    \"editListingHtml\": \"\",\n    \"editListingUrl\": \"\",\n    \"enabledFlag\": false,\n    \"hasListingsFlag\": false,\n    \"hasNoRatingsReviewsFlag\": false,\n    \"hasReviewsFlag\": false,\n    \"hasListingReporting\": false,\n    \"lspIcon\": \"\",\n    \"iconClass16px\": \"\",\n    \"iconClass32px\": \"\",\n    \"iconClass50px\": \"\",\n    \"iconUrl16px\": \"\",\n    \"iconUrl32px\": \"\",\n    \"iconUrl50px\": \"\",\n    \"isDirectlySubmittedSource\": false,\n    \"isLdSource\": false,\n    \"isLocationWhitelistedByDefaultFlag\": false,\n    \"isPartnerWhitelistedByDefaultFlag\": false,\n    \"isTaxonomyWhitelistedByDefaultFlag\": false,\n    \"ignoredAnchorDataMatches\": [\n      null\n    ],\n    \"locationBlacklist\": [\n      null\n    ],\n    \"locationSpecificOverrides\": {},\n    \"locationWhitelist\": [\n      null\n    ],\n    \"name\": \"\",\n    \"partnerBlacklist\": [\n      null\n    ],\n    \"partnerSpecificOverrides\": {},\n    \"partnerWhitelist\": [\n      null\n    ],\n    \"popularity\": 0,\n    \"sourceId\": 0,\n    \"sourceTypeId\": \"\",\n    \"sourceTypeName\": \"\",\n    \"submitListingHelpText\": \"\",\n    \"submitListingValidationRegex\": \"\",\n    \"supportsSubmittedListing\": false,\n    \"taxonomyBlacklist\": [\n      null\n    ],\n    \"taxonomyWhitelist\": [\n      null\n    ],\n    \"uberallId\": \"\",\n    \"yextId\": \"\",\n    \"availableForServiceAreaBusiness\": false,\n    \"syncRequirement\": \"SYNC_REQUIREMENT_UNSET\",\n    \"created\": {\n      \"seconds\": 0,\n      \"nanos\": 0\n    },\n    \"updated\": {\n      \"seconds\": 0,\n      \"nanos\": 0\n    },\n    \"deleted\": {\n      \"seconds\": 0,\n      \"nanos\": 0\n    }\n  },\n  \"fieldMask\": {\n    \"paths\": [\n      null\n    ]\n  }\n}", "options": {"raw": {"language": "json"}}}}}], "auth": {"type": "", "bearer": []}}, {"name": "listing_products.v1.ListingProfileService", "description": "Listing Profile Service ", "variable": [], "item": [{"name": "Create", "description": "", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.ListingProfileService/Create", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"nap\": {\n    \"companyName\": \"\",\n    \"address\": \"\",\n    \"address2\": \"\",\n    \"city\": \"\",\n    \"state\": \"\",\n    \"zip\": \"\",\n    \"country\": \"\",\n    \"website\": \"\",\n    \"workNumber\": [\n      null\n    ],\n    \"callTrackingNumber\": [\n      null\n    ],\n    \"location\": {\n      \"latitude\": 0,\n      \"longitude\": 0\n    },\n    \"timezone\": \"\",\n    \"serviceAreaBusiness\": false,\n    \"serviceArea\": {\n      \"businessType\": \"SERVICE_AREA_BUSINESS_TYPE_UNSPECIFIED\",\n      \"places\": [\n        {\n          \"placeId\": \"\",\n          \"placeName\": \"\",\n          \"city\": \"\"\n        }\n      ]\n    }\n  },\n  \"businessLocationId\": \"\",\n  \"updateOperations\": [\n    {\n      \"nap\": {\n        \"companyName\": \"\",\n        \"address\": \"\",\n        \"address2\": \"\",\n        \"city\": \"\",\n        \"state\": \"\",\n        \"zip\": \"\",\n        \"country\": \"\",\n        \"website\": \"\",\n        \"workNumber\": [\n          null\n        ],\n        \"callTrackingNumber\": [\n          null\n        ],\n        \"location\": {\n          \"latitude\": 0,\n          \"longitude\": 0\n        },\n        \"timezone\": \"\",\n        \"serviceAreaBusiness\": false,\n        \"serviceArea\": {\n          \"businessType\": \"SERVICE_AREA_BUSINESS_TYPE_UNSPECIFIED\",\n          \"places\": [\n            {\n              \"placeId\": \"\",\n              \"placeName\": \"\",\n              \"city\": \"\"\n            }\n          ]\n        }\n      },\n      \"fieldMask\": {\n        \"paths\": [\n          null\n        ]\n      }\n    }\n  ],\n  \"createOperations\": [\n    {\n      \"activateAccounts\": {\n        \"activations\": [\n          {\n            \"appKey\": {\n              \"appId\": \"\",\n              \"editionId\": \"\"\n            },\n            \"isTrial\": false\n          }\n        ]\n      }\n    }\n  ]\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GetMulti", "description": "", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.ListingProfileService/GetMulti", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"businessIds\": [\n    null\n  ],\n  \"projectionFilter\": {\n    \"externalIdentifiers\": false,\n    \"socialUrls\": false,\n    \"hoursOfOperation\": false,\n    \"richData\": false,\n    \"napData\": false,\n    \"businessHours\": false,\n    \"googleAttributes\": false,\n    \"googleAttributesMetadata\": false,\n    \"bingAttributes\": false,\n    \"bingAttributesMetadata\": false,\n    \"legacyProductDetails\": false\n  },\n  \"readFilter\": {\n    \"includeDeleted\": false\n  },\n  \"languageCode\": \"\"\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "Update", "description": "", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.ListingProfileService/Update", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"businessId\": \"\",\n  \"updateOperations\": [\n    {\n      \"nap\": {\n        \"companyName\": \"\",\n        \"address\": \"\",\n        \"address2\": \"\",\n        \"city\": \"\",\n        \"state\": \"\",\n        \"zip\": \"\",\n        \"country\": \"\",\n        \"website\": \"\",\n        \"workNumber\": [\n          null\n        ],\n        \"callTrackingNumber\": [\n          null\n        ],\n        \"location\": {\n          \"latitude\": 0,\n          \"longitude\": 0\n        },\n        \"timezone\": \"\",\n        \"serviceAreaBusiness\": false,\n        \"serviceArea\": {\n          \"businessType\": \"SERVICE_AREA_BUSINESS_TYPE_UNSPECIFIED\",\n          \"places\": [\n            {\n              \"placeId\": \"\",\n              \"placeName\": \"\",\n              \"city\": \"\"\n            }\n          ]\n        }\n      },\n      \"fieldMask\": {\n        \"paths\": [\n          null\n        ]\n      }\n    }\n  ],\n  \"ifUnmodifiedSince\": {\n    \"seconds\": 0,\n    \"nanos\": 0\n  },\n  \"languageCode\": \"\"\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "Delete", "description": "", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.ListingProfileService/Delete", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"businessId\": \"\"\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GetMoreHoursTypes", "description": "", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.ListingProfileService/GetMoreHoursTypes", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"businessId\": \"\",\n  \"languageCode\": \"\"\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GetAttributeMetadata", "description": "", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.ListingProfileService/GetAttributeMetadata", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"businessId\": \"\",\n  \"vendorId\": \"\",\n  \"languageCode\": \"\"\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GetDoctorDotComCategories", "description": "", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.ListingProfileService/GetDoctorDotComCategories", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}}, {"name": "LegacyAPICreate", "description": "", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.ListingProfileService/LegacyAPICreate", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"businessId\": \"\",\n  \"args\": \"\"\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "LegacyAPIUpdate", "description": "", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.ListingProfileService/LegacyAPIUpdate", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"args\": \"\"\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GetBusinessProfileFieldStatus", "description": "", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.ListingProfileService/GetBusinessProfileFieldStatus", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"businessId\": \"\"\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GetMultiAccountGroup", "description": "", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.ListingProfileService/GetMultiAccountGroup", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"businessIds\": [\n    null\n  ],\n  \"projectionFilter\": {\n    \"externalIdentifiers\": false,\n    \"socialUrls\": false,\n    \"hoursOfOperation\": false,\n    \"richData\": false,\n    \"napData\": false,\n    \"businessHours\": false,\n    \"googleAttributes\": false,\n    \"googleAttributesMetadata\": false,\n    \"bingAttributes\": false,\n    \"bingAttributesMetadata\": false,\n    \"legacyProductDetails\": false\n  },\n  \"readFilter\": {\n    \"includeDeleted\": false\n  },\n  \"languageCode\": \"\"\n}", "options": {"raw": {"language": "json"}}}}}], "auth": {"type": "", "bearer": []}}, {"name": "listing_products.v1.SuggestionService", "description": "", "variable": [], "item": [{"name": "Upsert", "description": "", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.SuggestionService/Upsert", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"businessId\": \"\",\n  \"updateOperations\": [\n    {\n      \"nap\": {\n        \"companyName\": \"\",\n        \"address\": \"\",\n        \"address2\": \"\",\n        \"city\": \"\",\n        \"state\": \"\",\n        \"zip\": \"\",\n        \"country\": \"\",\n        \"website\": \"\",\n        \"workNumber\": [\n          null\n        ],\n        \"callTrackingNumber\": [\n          null\n        ],\n        \"location\": {\n          \"latitude\": 0,\n          \"longitude\": 0\n        },\n        \"timezone\": \"\",\n        \"serviceAreaBusiness\": false,\n        \"serviceArea\": {\n          \"businessType\": \"SERVICE_AREA_BUSINESS_TYPE_UNSPECIFIED\",\n          \"places\": [\n            {\n              \"placeId\": \"\",\n              \"placeName\": \"\",\n              \"city\": \"\"\n            }\n          ]\n        }\n      },\n      \"fieldMask\": {\n        \"paths\": [\n          null\n        ]\n      }\n    }\n  ]\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GetSuggestion", "description": "", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.SuggestionService/GetSuggestion", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"businessId\": \"\",\n  \"fieldType\": \"\",\n  \"languageCode\": \"\"\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GetMulti", "description": "", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.SuggestionService/GetMulti", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"businessIds\": [\n    null\n  ],\n  \"partnerId\": \"\",\n  \"fieldType\": \"\",\n  \"languageType\": \"\"\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "Delete", "description": "", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.SuggestionService/Delete", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"businessId\": \"\"\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "List", "description": "", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.SuggestionService/List", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"partnerId\": \"\",\n  \"nextCursor\": \"\",\n  \"hasMore\": false,\n  \"pageSize\": 0\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "SuggestFieldUpdate", "description": "", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.SuggestionService/SuggestFieldUpdate", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"businessId\": \"\",\n  \"fieldType\": \"\",\n  \"existingValue\": \"\",\n  \"option\": \"UPDATE_OPTION_UNSPECIFIED\",\n  \"tone\": \"\",\n  \"languageCode\": \"\"\n}", "options": {"raw": {"language": "json"}}}}}], "auth": {"type": "", "bearer": []}}, {"name": "listing_products.v1.SEOService", "description": "SEO service ", "variable": [], "item": [{"name": "GetSEOData", "description": "Deprecated: use SEODataSummary & LocalSearchData ", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.SEOService/GetSEOData", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"businessId\": \"\",\n  \"keywords\": [\n    null\n  ],\n  \"startDate\": {\n    \"seconds\": 0,\n    \"nanos\": 0\n  },\n  \"endDate\": {\n    \"seconds\": 0,\n    \"nanos\": 0\n  }\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GetAllAIOAudit", "description": "", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.SEOService/GetAllAIOAudit", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"businessId\": \"\"\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GetAIOAudit", "description": "", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.SEOService/GetAIOAudit", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"businessId\": \"\",\n  \"brandName\": \"\",\n  \"websiteUrl\": \"\"\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GetAllAIOAuditScoreResults", "description": "", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.SEOService/GetAllAIOAuditScoreResults", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"businessId\": \"\",\n  \"brandName\": \"\",\n  \"websiteUrl\": \"\"\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GetAIOAuditStatus", "description": "", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.SEOService/GetAIOAuditStatus", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"businessId\": \"\",\n  \"brandName\": \"\",\n  \"websiteUrl\": \"\"\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "TriggerAIOAudit", "description": "", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.SEOService/TriggerAIOAudit", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"businessId\": \"\",\n  \"brandName\": \"\",\n  \"websiteUrl\": \"\"\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GetSEODataSummary", "description": "Get SEO summary data except Vicinity Data for keywords ", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.SEOService/GetSEODataSummary", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"businessId\": \"\",\n  \"keywords\": [\n    null\n  ],\n  \"startDate\": {\n    \"seconds\": 0,\n    \"nanos\": 0\n  },\n  \"endDate\": {\n    \"seconds\": 0,\n    \"nanos\": 0\n  }\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GetLocalSearchSEOData", "description": "Get Vicinity Data for keywords ", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.SEOService/GetLocalSearchSEOData", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"businessId\": \"\",\n  \"keyword\": \"\",\n  \"startDate\": {\n    \"seconds\": 0,\n    \"nanos\": 0\n  },\n  \"endDate\": {\n    \"seconds\": 0,\n    \"nanos\": 0\n  }\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "StartLocalSEODataWorkflow", "description": "", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.SEOService/StartLocalSEODataWorkflow", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"businesses\": [\n    {\n      \"businessId\": \"\",\n      \"keywords\": [\n        null\n      ]\n    }\n  ],\n  \"forceSerpWorkflow\": false,\n  \"date\": {\n    \"seconds\": 0,\n    \"nanos\": 0\n  },\n  \"forceKeywordInfoWorkflow\": false,\n  \"forceSuggestedKeywordsWorkflow\": false,\n  \"ignoreDataLakeResults\": false\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "SaveSEOSettings", "description": "", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.SEOService/SaveSEOSettings", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"businessId\": \"\",\n  \"localSearchRadius\": 0,\n  \"favoriteKeywords\": [\n    null\n  ],\n  \"fieldMask\": {\n    \"paths\": [\n      null\n    ]\n  },\n  \"isFullSearchEnabled\": false\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GetSEOSettings", "description": "", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.SEOService/GetSEOSettings", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"businessId\": \"\"\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GetActiveSEOAddons", "description": "", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.SEOService/GetActiveSEOAddons", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"businessId\": \"\"\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "StartSEOCategoryWorkflow", "description": "", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.SEOService/StartSEOCategoryWorkflow", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"businessIds\": [\n    null\n  ]\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GetDataForSEOCategory", "description": "", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.SEOService/GetDataForSEOCategory", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"businessId\": \"\"\n}", "options": {"raw": {"language": "json"}}}}}], "auth": {"type": "", "bearer": []}}, {"name": "listing_products.v1.Citations", "description": "Citation service ", "variable": [], "item": [{"name": "StartCitationWorkflow", "description": "", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.Citations/StartCitationWorkflow", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"businessIds\": [\n    null\n  ],\n  \"searchType\": 0\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GetCitationData", "description": "", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.Citations/GetCitationData", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"businessId\": \"\",\n  \"startDate\": {\n    \"seconds\": 0,\n    \"nanos\": 0\n  },\n  \"endDate\": {\n    \"seconds\": 0,\n    \"nanos\": 0\n  }\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "DeleteCitations", "description": "", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.Citations/DeleteCitations", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"businessId\": \"\",\n  \"urls\": [\n    null\n  ]\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GetCitationSummary", "description": "", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.Citations/GetCitationSummary", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"businessId\": \"\",\n  \"startDate\": {\n    \"seconds\": 0,\n    \"nanos\": 0\n  },\n  \"endDate\": {\n    \"seconds\": 0,\n    \"nanos\": 0\n  }\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "ListCitationDomains", "description": "", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.Citations/ListCitationDomains", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"businessId\": \"\",\n  \"pageSize\": 0,\n  \"cursor\": \"\",\n  \"startDate\": {\n    \"seconds\": 0,\n    \"nanos\": 0\n  },\n  \"endDate\": {\n    \"seconds\": 0,\n    \"nanos\": 0\n  }\n}", "options": {"raw": {"language": "json"}}}}}], "auth": {"type": "", "bearer": []}}, {"name": "listing_products.v1.PartnerSettingsService", "description": "", "variable": [], "item": [{"name": "CreatePartnerSettings", "description": "CreatePartnerSettings will create a new PartnerSettings ", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.PartnerSettingsService/CreatePartnerSettings", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"partnerId\": \"\",\n  \"marketId\": \"\",\n  \"isSmbKeyWordEditingDisabled\": false,\n  \"isFavoriteNewKeywordsDisabled\": false,\n  \"isSyncKeywordsDisabled\": false\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GetPartnerSettings", "description": "GetPartnerSettings returns information about the partner settings for a given partnerID / marketID ", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.PartnerSettingsService/GetPartnerSettings", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"partnerId\": \"\",\n  \"marketId\": \"\"\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "UpsertPartnerSettings", "description": "UpsertPartnerSettings will create a new PartnerSettings if it does not exist yet, or update the existing PartnerSettings if it does. ", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.PartnerSettingsService/UpsertPartnerSettings", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"partnerId\": \"\",\n  \"marketId\": \"\",\n  \"isSmbKeyWordEditingDisabled\": false,\n  \"isFavoriteNewKeywordsDisabled\": false,\n  \"isSyncKeywordsDisabled\": false\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GetConfiguration", "description": "GetConfiguration returns the configuration for a given partner ", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.PartnerSettingsService/GetConfiguration", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"partnerId\": \"\",\n  \"businessId\": \"\"\n}", "options": {"raw": {"language": "json"}}}}}], "auth": {"type": "", "bearer": []}}, {"name": "listing_products.v1.SEOSuggestedKeywordsService", "description": "", "variable": [], "item": [{"name": "GetOrGenerateSEOSuggestedKeywords", "description": "GetOrGenerateSEOSuggestedKeywords will get or generate SEO suggested keywords for a given business ", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.SEOSuggestedKeywordsService/GetOrGenerateSEOSuggestedKeywords", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"businessId\": \"\"\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GetSEOSuggestedKeywords", "description": "GetSEOSuggestedKeywords returns SEO suggested keywords for a given business ", "variable": [], "request": {"url": "https://listing-products-api-{{env}}.vendasta-internal.com/listing_products.v1.SEOSuggestedKeywordsService/GetSEOSuggestedKeywords", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"businessId\": \"\"\n}", "options": {"raw": {"language": "json"}}}}}], "auth": {"type": "", "bearer": []}}], "variable": [], "auth": []}