version: "2"
linters:
  default: none
  enable:
    - asciicheck
    - bidichk
    - bodyclose
    - contextcheck
    - cyclop
    - dupl
    - durationcheck
    - errcheck
    - errname
    - errorlint
    - exhaustive
    - forbidigo
    - funlen
    - gochecknoinits
    - gocognit
    - goconst
    - gocritic
    - gocyclo
    - godot
    - gomoddirectives
    - gomodguard
    - goprintffuncname
    - gosec
    - govet
    - ineffassign
    - lll
    - makezero
    - mnd
    - nestif
    - nilerr
    - nilnil
    - nolintlint
    - nosprintfhostport
    - predeclared
    - promlinter
    - revive
    - rowserrcheck
    - sqlclosecheck
    - staticcheck
    - testpackage
    - tparallel
    - unconvert
    - unparam
    - unused
    - usetesting
    - wastedassign
    - whitespace
  settings:
    cyclop:
      max-complexity: 30
      package-average: 10
    errcheck:
      check-type-assertions: true
    funlen:
      lines: 100
      statements: 50
    gocognit:
      min-complexity: 20
    gocritic:
      settings:
        captLocal:
          paramsOnly: false
        underef:
          skipRecvDeref: false
    gomodguard:
      blocked:
        modules:
          - github.com/golang/protobuf:
              recommendations:
                - google.golang.org/protobuf
              reason: see https://developers.google.com/protocol-buffers/docs/reference/go/faq#modules
          - github.com/satori/go.uuid:
              recommendations:
                - github.com/google/uuid
              reason: satori's package is not maintained
          - github.com/gofrs/uuid:
              recommendations:
                - github.com/google/uuid
              reason: 'see recommendation from dev-infra team: https://confluence.gtforge.com/x/gQI6Aw'
    govet:
      disable:
        - fieldalignment
      enable-all: true
      settings:
        shadow:
          strict: true
    mnd:
      ignored-functions:
        - os.Chmod
        - os.Mkdir
        - os.MkdirAll
        - os.OpenFile
        - os.WriteFile
        - prometheus.ExponentialBuckets
        - prometheus.ExponentialBucketsRange
        - prometheus.LinearBuckets
        - strconv.FormatFloat
        - strconv.FormatInt
        - strconv.FormatUint
        - strconv.ParseFloat
        - strconv.ParseInt
        - strconv.ParseUint
    nakedret:
      max-func-lines: 0
    nolintlint:
      require-explanation: true
      require-specific: true
      allow-no-explanation:
        - funlen
        - gocognit
        - lll
    rowserrcheck:
      packages:
        - github.com/jmoiron/sqlx
  exclusions:
    generated: lax
    presets:
      - comments
      - common-false-positives
      - legacy
      - std-error-handling
    rules:
      - linters:
          - forbidigo
          - mnd
          - revive
        path : ^examples/.*\.go$
      - linters:
          - lll
        source: ^//\s*go:generate\s
      - linters:
          - godot
        source: (noinspection|TODO)
      - linters:
          - gocritic
        source: //noinspection
      - linters:
          - errorlint
        source: ^\s+if _, ok := err\.\([^.]+\.InternalError\); ok {
      - linters:
          - bodyclose
          - dupl
          - funlen
          - goconst
          - gosec
          - noctx
          - wrapcheck
          - staticcheck
        path: _test\.go
    paths:
      - third_party$
      - builtin$
      - examples$
issues:
  max-same-issues: 50
formatters:
  enable:
    - goimports
  exclusions:
    generated: lax
    paths:
      - third_party$
      - builtin$
      - examples$
