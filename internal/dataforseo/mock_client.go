// Code generated by MockGen. DO NOT EDIT.
// Source: client.go

// Package dataforseo is a generated GoMock package.
package dataforseo

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockSERPClient is a mock of SERPClient interface.
type MockSERPClient struct {
	ctrl     *gomock.Controller
	recorder *MockSERPClientMockRecorder
}

// MockSERPClientMockRecorder is the mock recorder for MockSERPClient.
type MockSERPClientMockRecorder struct {
	mock *MockSERPClient
}

// NewMockSERPClient creates a new mock instance.
func NewMockSERPClient(ctrl *gomock.Controller) *MockSERPClient {
	mock := &MockSERPClient{ctrl: ctrl}
	mock.recorder = &MockSERPClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockSERPClient) EXPECT() *MockSERPClientMockRecorder {
	return m.recorder
}

// GetLocations mocks base method.
func (m *MockSERPClient) GetLocations(ctx context.Context) ([]*Location, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLocations", ctx)
	ret0, _ := ret[0].([]*Location)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLocations indicates an expected call of GetLocations.
func (mr *MockSERPClientMockRecorder) GetLocations(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLocations", reflect.TypeOf((*MockSERPClient)(nil).GetLocations), ctx)
}

// GoogleAdsStatusTaskGet mocks base method.
func (m *MockSERPClient) GoogleAdsStatusTaskGet(ctx context.Context) (*GoogleAdsStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GoogleAdsStatusTaskGet", ctx)
	ret0, _ := ret[0].(*GoogleAdsStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GoogleAdsStatusTaskGet indicates an expected call of GoogleAdsStatusTaskGet.
func (mr *MockSERPClientMockRecorder) GoogleAdsStatusTaskGet(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GoogleAdsStatusTaskGet", reflect.TypeOf((*MockSERPClient)(nil).GoogleAdsStatusTaskGet), ctx)
}

// GoogleKeywordVolumeTaskGet mocks base method.
func (m *MockSERPClient) GoogleKeywordVolumeTaskGet(ctx context.Context, taskID string) (*GoogleKeywordVolumeResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GoogleKeywordVolumeTaskGet", ctx, taskID)
	ret0, _ := ret[0].(*GoogleKeywordVolumeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GoogleKeywordVolumeTaskGet indicates an expected call of GoogleKeywordVolumeTaskGet.
func (mr *MockSERPClientMockRecorder) GoogleKeywordVolumeTaskGet(ctx, taskID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GoogleKeywordVolumeTaskGet", reflect.TypeOf((*MockSERPClient)(nil).GoogleKeywordVolumeTaskGet), ctx, taskID)
}

// GoogleKeywordVolumeTaskPost mocks base method.
func (m *MockSERPClient) GoogleKeywordVolumeTaskPost(ctx context.Context, request *GoogleKeywordVolumeTaskPostRequest, source RequestSource) (*GoogleKeywordVolumeResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GoogleKeywordVolumeTaskPost", ctx, request, source)
	ret0, _ := ret[0].(*GoogleKeywordVolumeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GoogleKeywordVolumeTaskPost indicates an expected call of GoogleKeywordVolumeTaskPost.
func (mr *MockSERPClientMockRecorder) GoogleKeywordVolumeTaskPost(ctx, request, source interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GoogleKeywordVolumeTaskPost", reflect.TypeOf((*MockSERPClient)(nil).GoogleKeywordVolumeTaskPost), ctx, request, source)
}

// GoogleKeywordsForSiteLivePost mocks base method.
func (m *MockSERPClient) GoogleKeywordsForSiteLivePost(ctx context.Context, date string, requests []*GoogleKeywordsForSiteRequest, source RequestSource) (*GoogleKeywordsForSiteResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GoogleKeywordsForSiteLivePost", ctx, date, requests, source)
	ret0, _ := ret[0].(*GoogleKeywordsForSiteResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GoogleKeywordsForSiteLivePost indicates an expected call of GoogleKeywordsForSiteLivePost.
func (mr *MockSERPClientMockRecorder) GoogleKeywordsForSiteLivePost(ctx, date, requests, source interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GoogleKeywordsForSiteLivePost", reflect.TypeOf((*MockSERPClient)(nil).GoogleKeywordsForSiteLivePost), ctx, date, requests, source)
}

// GoogleKeywordsForSiteTaskGet mocks base method.
func (m *MockSERPClient) GoogleKeywordsForSiteTaskGet(ctx context.Context, businessID, date, taskID string) (*GoogleKeywordsForSiteResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GoogleKeywordsForSiteTaskGet", ctx, businessID, date, taskID)
	ret0, _ := ret[0].(*GoogleKeywordsForSiteResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GoogleKeywordsForSiteTaskGet indicates an expected call of GoogleKeywordsForSiteTaskGet.
func (mr *MockSERPClientMockRecorder) GoogleKeywordsForSiteTaskGet(ctx, businessID, date, taskID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GoogleKeywordsForSiteTaskGet", reflect.TypeOf((*MockSERPClient)(nil).GoogleKeywordsForSiteTaskGet), ctx, businessID, date, taskID)
}

// GoogleKeywordsForSiteTaskPost mocks base method.
func (m *MockSERPClient) GoogleKeywordsForSiteTaskPost(ctx context.Context, date string, requests []*GoogleKeywordsForSiteRequest, source RequestSource) (*GoogleKeywordsForSiteResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GoogleKeywordsForSiteTaskPost", ctx, date, requests, source)
	ret0, _ := ret[0].(*GoogleKeywordsForSiteResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GoogleKeywordsForSiteTaskPost indicates an expected call of GoogleKeywordsForSiteTaskPost.
func (mr *MockSERPClientMockRecorder) GoogleKeywordsForSiteTaskPost(ctx, date, requests, source interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GoogleKeywordsForSiteTaskPost", reflect.TypeOf((*MockSERPClient)(nil).GoogleKeywordsForSiteTaskPost), ctx, date, requests, source)
}

// GoogleMapsSERPTaskGet mocks base method.
func (m *MockSERPClient) GoogleMapsSERPTaskGet(ctx context.Context, businessID, keyword, vicinity, date, taskID string, ignoreDataLakeResults bool) (*GoogleMapsSERPResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GoogleMapsSERPTaskGet", ctx, businessID, keyword, vicinity, date, taskID, ignoreDataLakeResults)
	ret0, _ := ret[0].(*GoogleMapsSERPResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GoogleMapsSERPTaskGet indicates an expected call of GoogleMapsSERPTaskGet.
func (mr *MockSERPClientMockRecorder) GoogleMapsSERPTaskGet(ctx, businessID, keyword, vicinity, date, taskID, ignoreDataLakeResults interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GoogleMapsSERPTaskGet", reflect.TypeOf((*MockSERPClient)(nil).GoogleMapsSERPTaskGet), ctx, businessID, keyword, vicinity, date, taskID, ignoreDataLakeResults)
}

// GoogleMapsSERPTaskPost mocks base method.
func (m *MockSERPClient) GoogleMapsSERPTaskPost(ctx context.Context, date string, ignoreDataLakeResults bool, requests []*GoogleMapsSERPTaskPostRequest, source RequestSource, ro ...RequestOpts) (*GoogleMapsSERPResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, date, ignoreDataLakeResults, requests, source}
	for _, a := range ro {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GoogleMapsSERPTaskPost", varargs...)
	ret0, _ := ret[0].(*GoogleMapsSERPResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GoogleMapsSERPTaskPost indicates an expected call of GoogleMapsSERPTaskPost.
func (mr *MockSERPClientMockRecorder) GoogleMapsSERPTaskPost(ctx, date, ignoreDataLakeResults, requests, source interface{}, ro ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, date, ignoreDataLakeResults, requests, source}, ro...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GoogleMapsSERPTaskPost", reflect.TypeOf((*MockSERPClient)(nil).GoogleMapsSERPTaskPost), varargs...)
}

// GoogleOrganicSERPTaskGet mocks base method.
func (m *MockSERPClient) GoogleOrganicSERPTaskGet(ctx context.Context, businessID, keyword, vicinity, date, taskID string, ignoreDataLakeResults bool) (*GoogleOrganicSERPResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GoogleOrganicSERPTaskGet", ctx, businessID, keyword, vicinity, date, taskID, ignoreDataLakeResults)
	ret0, _ := ret[0].(*GoogleOrganicSERPResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GoogleOrganicSERPTaskGet indicates an expected call of GoogleOrganicSERPTaskGet.
func (mr *MockSERPClientMockRecorder) GoogleOrganicSERPTaskGet(ctx, businessID, keyword, vicinity, date, taskID, ignoreDataLakeResults interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GoogleOrganicSERPTaskGet", reflect.TypeOf((*MockSERPClient)(nil).GoogleOrganicSERPTaskGet), ctx, businessID, keyword, vicinity, date, taskID, ignoreDataLakeResults)
}

// GoogleOrganicSERPTaskPost mocks base method.
func (m *MockSERPClient) GoogleOrganicSERPTaskPost(ctx context.Context, date string, ignoreDataLakeResults bool, requests []*GoogleOrganicSERPTaskPostRequest, source RequestSource, ro ...RequestOpts) (*GoogleOrganicSERPResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, date, ignoreDataLakeResults, requests, source}
	for _, a := range ro {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GoogleOrganicSERPTaskPost", varargs...)
	ret0, _ := ret[0].(*GoogleOrganicSERPResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GoogleOrganicSERPTaskPost indicates an expected call of GoogleOrganicSERPTaskPost.
func (mr *MockSERPClientMockRecorder) GoogleOrganicSERPTaskPost(ctx, date, ignoreDataLakeResults, requests, source interface{}, ro ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, date, ignoreDataLakeResults, requests, source}, ro...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GoogleOrganicSERPTaskPost", reflect.TypeOf((*MockSERPClient)(nil).GoogleOrganicSERPTaskPost), varargs...)
}

// OnPagePages mocks base method.
func (m *MockSERPClient) OnPagePages(ctx context.Context, requests []*OnPagePagesRequest, source RequestSource) (*OnPagePagesResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OnPagePages", ctx, requests, source)
	ret0, _ := ret[0].(*OnPagePagesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OnPagePages indicates an expected call of OnPagePages.
func (mr *MockSERPClientMockRecorder) OnPagePages(ctx, requests, source interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OnPagePages", reflect.TypeOf((*MockSERPClient)(nil).OnPagePages), ctx, requests, source)
}

// OnPageSummary mocks base method.
func (m *MockSERPClient) OnPageSummary(ctx context.Context, taskID string, source RequestSource) (*OnPageSummaryResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OnPageSummary", ctx, taskID, source)
	ret0, _ := ret[0].(*OnPageSummaryResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OnPageSummary indicates an expected call of OnPageSummary.
func (mr *MockSERPClientMockRecorder) OnPageSummary(ctx, taskID, source interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OnPageSummary", reflect.TypeOf((*MockSERPClient)(nil).OnPageSummary), ctx, taskID, source)
}

// OnPageTaskPost mocks base method.
func (m *MockSERPClient) OnPageTaskPost(ctx context.Context, requests []*OnPageTaskPostRequest, source RequestSource) (*OnPageResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OnPageTaskPost", ctx, requests, source)
	ret0, _ := ret[0].(*OnPageResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OnPageTaskPost indicates an expected call of OnPageTaskPost.
func (mr *MockSERPClientMockRecorder) OnPageTaskPost(ctx, requests, source interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OnPageTaskPost", reflect.TypeOf((*MockSERPClient)(nil).OnPageTaskPost), ctx, requests, source)
}

// OnPageTasksReady mocks base method.
func (m *MockSERPClient) OnPageTasksReady(ctx context.Context, source RequestSource) (*OnPageTasksReadyResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OnPageTasksReady", ctx, source)
	ret0, _ := ret[0].(*OnPageTasksReadyResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OnPageTasksReady indicates an expected call of OnPageTasksReady.
func (mr *MockSERPClientMockRecorder) OnPageTasksReady(ctx, source interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OnPageTasksReady", reflect.TypeOf((*MockSERPClient)(nil).OnPageTasksReady), ctx, source)
}

// OnPageRawHTML mocks base method.
func (m *MockSERPClient) OnPageRawHTML(ctx context.Context, requests []*OnPageRawHTMLRequest, source RequestSource) (*OnPageRawHTMLResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OnPageRawHTML", ctx, requests, source)
	ret0, _ := ret[0].(*OnPageRawHTMLResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OnPageRawHTML indicates an expected call of OnPageRawHTML.
func (mr *MockSERPClientMockRecorder) OnPageRawHTML(ctx, requests, source interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OnPageRawHTML", reflect.TypeOf((*MockSERPClient)(nil).OnPageRawHTML), ctx, requests, source)
}
