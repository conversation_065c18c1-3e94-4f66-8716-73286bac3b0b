package seoworkflow

import (
	"context"
	"testing"

	seosettingsmodel "github.com/vendasta/listing-products/internal/seosettings/model"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	cssdk "github.com/vendasta/CS/sdks/go/v1"
	"github.com/vendasta/generated-protos-go/vstorepb"
	dataforseo "github.com/vendasta/listing-products/internal/dataforseo"
	listingprofilemodel "github.com/vendasta/listing-products/internal/listingprofile/model"
	listingprofileservice "github.com/vendasta/listing-products/internal/listingprofile/service"
	seodata "github.com/vendasta/listing-products/internal/seo/model"
	seodataservice "github.com/vendasta/listing-products/internal/seo/service"
	seosettingsservice "github.com/vendasta/listing-products/internal/seosettings/service"
)

func Test_fetchMapsResult(t *testing.T) {
	type seoDataCall struct {
		businessID string
		keyword    string
		entries    []*seodata.LocalSearchData
	}

	type testCase struct {
		name               string
		task               *PostedTask
		dataForSeoResponse *dataforseo.GoogleMapsSERPResponse
		listingProfile     *listingprofilemodel.ListingProfile
		expectedResult     *seodata.LocalSearchData
		expectedErr        error
	}
	cases := []*testCase{
		{
			name: "",
			task: &PostedTask{
				ID:         "05292227-3621-0066-0000-12bb5d24b98c",
				BusinessID: "AG-H7TQNDHQ5C",
				Keyword:    "Dr. Connor McDavid",
				Vicinity:   "VICINITY_B1",
				Lat:        47.24934839840781,
				Lng:        -122.58799936329886,
			},
			listingProfile: &listingprofilemodel.ListingProfile{
				BusinessID:          "AG-M8GZ83PL",
				CompanyName:         "Dr. Connor McDavid",
				Address:             "212 Saskatchewan Crescent E",
				City:                "Saskatoon",
				State:               "SK",
				Zip:                 "S7N 0K6",
				Country:             "CA",
				Website:             "http://connerdoesteeth.com",
				WorkNumber:          []string{"+1 3066536001"},
				Location:            &vstorepb.GeoPoint{Latitude: 52.11968239999999, Longitude: -106.6665434},
				Timezone:            "America/Los_Angeles",
				ServiceAreaBusiness: true,
				ServiceArea: &listingprofilemodel.ServiceArea{
					BusinessType: 1, Places: []*listingprofilemodel.GooglePlace{
						{
							ID: "ChIJK5ntR7_2BFMRkCZ3lTKeBAU", Name: "Saskatoon, SK", City: "Saskatoon",
						}, {ID: "ChIJr84nbFdnBFMR_IZ0urNu8pA", Name: "Warman, SK", City: "Warman"},
					},
				},
				RichData: &listingprofilemodel.RichData{
					Description:      "You got bones? I know bones. I'm a big bone guy. Legs, arms, pelvis, ribs. I know them inside and out. You don't got bones? I can still help. I'll find your bones. And if I don't they don't exist. Teeth? Teeth aren't bones, but they're close enough for me. I will find them and operate!!",
					ShortDescription: "Sports Medicine specializing in bone health.",
				},
			},
			dataForSeoResponse: &dataforseo.GoogleMapsSERPResponse{

				StatusCode: 20000,
				TasksCount: 1,
				TasksError: 0,
				Tasks: []*dataforseo.GoogleMapsTask{
					{
						ID:         "05292227-3621-0066-0000-12bb5d24b98c",
						StatusCode: 20100,
						Data: &dataforseo.Data{
							Keyword:            "Dr. Connor McDavid",
							LocationCoordinate: "47.24934839840781,-122.5456633,13z",
							Device:             "mobile",
							Depth:              "100",
							LanguageCode:       "en",
						},
						Result: []*dataforseo.GoogleMapsResult{
							{
								Keyword: "Dr. Connor McDavid",
								Items: []*dataforseo.GoogleMapsItem{
									{
										Type:         "maps_search",
										Rank:         8,
										BusinessName: "Fake McDavid",
										URL:          "http://connerdoesnotdoteeth.com/",
										Address:      "215 Saskatchewan Crescent E, Saskatoon, SK S7N 0L6",
										Phone:        "+***********",
										Rating: &dataforseo.Rating{
											Value: 4.1,
											Count: 31,
										},
									},
									{
										Type:         "maps_search",
										Rank:         2,
										BusinessName: "Dr. Connor McDavid",
										URL:          "http://connerdoesteeth.com/",
										Address:      "212 Saskatchewan Crescent E, Saskatoon, SK S7N 0K6",
										Phone:        "+***********",
										PlaceID:      "",
										CustomerID:   "",
										Rating: &dataforseo.Rating{
											Value: 4.1,
											Count: 31,
										},
									},
								},
							},
						},
					},
				},
			},
			expectedResult: &seodata.LocalSearchData{
				Keyword: "Dr. Connor McDavid", Vicinity: "VICINITY_B1",
				SearchLocation: &seodata.Geo{Latitude: 47.24934839840781, Longitude: -122.58799936329886},
				Results: []*seodata.LocalSearchResult{
					{
						BusinessName:   "Dr. Connor McDavid",
						Address:        "212 Saskatchewan Crescent E, Saskatoon, SK S7N 0K6",
						Url:            "http://connerdoesteeth.com/",
						Rank:           2,
						IsMainBusiness: true,
						Reviews:        &seodata.LocalSearchReviews{Rating: 4.1, Count: "31"},
						PhoneNumber:    "+***********",
					},
					{
						BusinessName:   "Fake McDavid",
						Address:        "215 Saskatchewan Crescent E, Saskatoon, SK S7N 0L6",
						Url:            "http://connerdoesnotdoteeth.com/",
						Rank:           8,
						IsMainBusiness: false,
						Reviews:        &seodata.LocalSearchReviews{Rating: 4.1, Count: "31"},
						PhoneNumber:    "+***********",
					},
				},
			},
			expectedErr: nil,
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			ctx := context.Background()
			ctrl := gomock.NewController(t)

			mockLpService := listingprofileservice.NewMockInterface(ctrl)
			mockLpService.EXPECT().Get(gomock.Any(), gomock.Any(), false).Return(c.listingProfile, nil).AnyTimes()

			mockDataForSeo := dataforseo.NewMockSERPClient(ctrl)
			mockDataForSeo.EXPECT().GoogleMapsSERPTaskGet(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(c.dataForSeoResponse, nil).Times(1)

			mockSeoDataService := seodataservice.NewMockService(ctrl)

			mockSeoSettingsService := seosettingsservice.NewMockService(ctrl)

			mockCSGMB := cssdk.NewMockGoogleMyBusinessClientInterface(ctrl)
			mockCSGMB.EXPECT().GetGoogleMyBusinessLocation(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&cssdk.GoogleMyBusinessLocation{}, nil).AnyTimes()

			ctx = NewActivityContext(ctx, nil, mockLpService, nil, mockSeoDataService, mockDataForSeo, mockSeoSettingsService, nil, nil, nil, nil, nil, nil, nil, mockCSGMB, nil, nil, nil, nil, nil, nil, nil)

			resp, err := fetchMapsResult(ctx, &SERPWorkflowParams{
				BusinessID: "AG-123",
			}, c.task, "2023-05-29", "")

			assert.Equal(t, c.expectedResult, resp)
			assert.Equal(t, c.expectedErr, err)
		})
	}
}

func Test_getAndStoreOrganicResult(t *testing.T) {
	type seoDataCall struct {
		businessID string
		keyword    string
		rank       int
	}

	type testCase struct {
		name               string
		task               *PostedTask
		dataForSeoResponse *dataforseo.GoogleOrganicSERPResponse
		listingProfile     *listingprofilemodel.ListingProfile
		seoDataCall        *seoDataCall
		expectedErr        error
		seoSetting         *seosettingsmodel.SEOSettings
	}
	cases := []*testCase{
		{
			name: "Test all tasks are fetched and organic ranks are saved",
			task: &PostedTask{
				ID:         "05240009-3621-0066-0000-e1d08f9c6849",
				BusinessID: "AG-H7TQNDHQ5C",
				Keyword:    "Dr. Connor McDavid",
				Vicinity:   "VICINITY_CITY",
				Lat:        50.028949,
				Lng:        19.955279,
			},
			listingProfile: &listingprofilemodel.ListingProfile{
				BusinessID:  "AG-H7TQNDHQ5C",
				CompanyName: "Dr. Connor McDavid",
				Address:     "212 Saskatchewan Crescent E",
				City:        "Saskatoon",
				State:       "SK",
				Zip:         "S7N 0K6",
				Country:     "CA",
				Website:     "http://connerdoesteeth.com",
				WorkNumber:  []string{"+1 3066536001"},
				Location:    &vstorepb.GeoPoint{Latitude: 52.11968239999999, Longitude: -106.6665434},
				Timezone:    "America/Los_Angeles",
				RichData: &listingprofilemodel.RichData{
					Description:      "You got bones? I know bones. I'm a big bone guy. Legs, arms, pelvis, ribs. I know them inside and out. You don't got bones? I can still help. I'll find your bones. And if I don't they don't exist. Teeth? Teeth aren't bones, but they're close enough for me. I will find them and operate!!",
					ShortDescription: "Sports Medicine specializing in bone health.",
				},
			},
			dataForSeoResponse: &dataforseo.GoogleOrganicSERPResponse{
				StatusCode: 20000,
				TasksCount: 1,
				TasksError: 0,
				Tasks: []*dataforseo.GoogleOrganicTask{
					{
						ID:         "05292227-3621-0066-0000-12bb5d24b98c",
						StatusCode: 20100,
						Data: &dataforseo.Data{
							Keyword:            "Dr. Connor McDavid",
							LocationCoordinate: "47.24934839840781,-122.5456633,13z",
							Device:             "mobile",
							Depth:              "100",
							LanguageCode:       "en",
						},
						Result: []*dataforseo.GoogleOrganicResult{
							{
								Keyword: "Dr. Connor McDavid",
								Items: []*dataforseo.GoogleOrganicItem{
									{
										Type:      "organic",
										RankGroup: 8,
										URL:       "http://connerdoesnotdoteeth.com/",
									},
									{
										Type:      "organic",
										RankGroup: 2,
										URL:       "https://connerdoesteeth.com/teeth",
									},
								},
							},
						},
					},
				},
			},
			seoDataCall: &seoDataCall{
				businessID: "AG-H7TQNDHQ5C",
				keyword:    "Dr. Connor McDavid",
				rank:       2,
			},
			seoSetting: &seosettingsmodel.SEOSettings{
				BusinessID:          "AG-H7TQNDHQ5C",
				IsFullSearchEnabled: false,
			},
			expectedErr: nil,
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			ctx := context.Background()
			ctrl := gomock.NewController(t)

			mockLpService := listingprofileservice.NewMockInterface(ctrl)
			mockLpService.EXPECT().Get(gomock.Any(), gomock.Any(), false).Return(c.listingProfile, nil).AnyTimes()

			mockDataForSeo := dataforseo.NewMockSERPClient(ctrl)
			mockDataForSeo.EXPECT().GoogleOrganicSERPTaskGet(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(c.dataForSeoResponse, nil).Times(1)
			mockSeoSettingsService := seosettingsservice.NewMockService(ctrl)
			mockSeoSettingsService.EXPECT().Get(gomock.Any(), gomock.Any()).Return(c.seoSetting, nil).AnyTimes()

			mockSeoDataService := seodataservice.NewMockService(ctrl)

			mockSeoDataService.EXPECT().UpsertOrganicRank(gomock.Any(), gomock.Any(), c.seoDataCall.keyword, "2023-05-29", c.seoDataCall.rank).Return(nil).Times(1)

			ctx = NewActivityContext(ctx, nil, mockLpService, nil, mockSeoDataService, mockDataForSeo, mockSeoSettingsService, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil)

			err := getAndStoreOrganicResult(ctx, &SERPWorkflowParams{
				Date: "2023-05-29",
			}, c.task)

			assert.Equal(t, c.expectedErr, err)
		})
	}
}

func Test_getRankFromOrganicResults(t *testing.T) {
	type testCase struct {
		name         string
		items        []*dataforseo.GoogleOrganicItem
		website      string
		expectedRank int
	}
	cases := []*testCase{
		{
			name: "Test matches domains with differing www. prefixes",
			items: []*dataforseo.GoogleOrganicItem{
				{
					Type:         "organic",
					RankGroup:    8,
					RankAbsolute: 12,
					Domain:       "assuredpsychology.com",
					Title:        "Assured Psychology: Psychology Services | Clinical, Couple Mental ...",
					Description:  "Assured Psychology INC. is your reliable clinic for compassionate treatment for mental health suffering, trauma pain, general ...",
					URL:          "https://assuredpsychology.com/",
					Breadcrumb:   "https://assuredpsychology.com",
				},
			},
			website:      "http://www.assuredpsychology.com/",
			expectedRank: 8,
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {

			rank := getRankFromOrganicResults(c.items, c.website, false)

			assert.Equal(t, c.expectedRank, rank)
		})
	}

}

func Test_getMainBusinessIndex(t *testing.T) {
	type testCase struct {
		name               string
		comapnyName        string
		address            string
		website            string
		country            string
		phone              string
		items              []*dataforseo.GoogleMapsItem
		placeID            string
		expectedMatchIndex int
		expectedErr        error
	}
	cases := []*testCase{
		{
			name:        "Test matches business with differently formatted address",
			comapnyName: "The Conti Bar",
			address:     "1-21 Ocean Beach Road",
			website:     "https://thecontinentalsorrento.com.au/bars/",
			country:     "AU",
			phone:       "+61 3 5935 1200",
			items: []*dataforseo.GoogleMapsItem{
				{
					Rank:         6,
					Domain:       "thecontinentalsorrento.com.au",
					BusinessName: "The Continental Sorrento",
					URL:          "https://thecontinentalsorrento.com.au/",
					Address:      "1/21 Ocean Beach Rd, Sorrento VIC 3943",
					Phone:        "+***********",
					AddressInfo: &dataforseo.AddressInfo{
						Address: "1-21 Ocean Beach Rd",
					},
				},
			},
			expectedMatchIndex: 0,
			expectedErr:        nil,
		},
		{
			name:        "Test matches business with matching google place ID",
			comapnyName: "The Conti Bar",
			address:     "1-21 Ocean Beach Road",
			website:     "https://thecontinentalsorrento.com.au/bars/",
			country:     "AU",
			phone:       "+61 3 5935 1200",
			items: []*dataforseo.GoogleMapsItem{
				{
					Rank: 2,
				},
				{
					Rank:    6,
					PlaceID: "ChIJr84nbFdnBFMR_IZ0urNu8pA",
				},
			},
			placeID:            "ChIJr84nbFdnBFMR_IZ0urNu8pA",
			expectedMatchIndex: 1,
			expectedErr:        nil,
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			resp := getMainBusinessIndex(c.items, c.comapnyName, c.address, c.website, c.country, []string{c.phone}, c.placeID)

			assert.Equal(t, c.expectedMatchIndex, resp)
		})
	}
}
