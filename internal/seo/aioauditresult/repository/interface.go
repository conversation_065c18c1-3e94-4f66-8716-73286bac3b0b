// Code generated by the Vendasta codegen tool (github.com/vendasta/codegen). DO NOT EDIT.
// This means that any changes you make to this file may be overwritten by a subsequent run of the codegen tool, so be cautious.
package aioauditresultrepository

import (
	"context"
	"time"

	model "github.com/vendasta/listing-products/internal/seo/aioauditresult"
)

// MutateFunc is a function that mutates an existing AIOAuditResult.
// This function is executed inside of a transaction, so it should not make any RPCs or
// perform non-idempotent operations that affect something other than the AIOAuditResult.
//
// For example, sending a datadog tick or incrementing a counter in an instance cache inside of a
// MutateFunc is a bad idea, but incrementing a counter on the actual AIOAuditResult is ok,
// because the AIOAuditResult will be reset if the transaction needs to retry.
type MutateFunc func(ctx context.Context, aIOAuditResult *model.AIOAuditResult) error

// Repository reads from and writes to a storage device(s)
//
//go:generate mockgen -destination mock.go -source=interface.go -package=aioauditresultrepository
type Repository interface {
	Reader
	Writer
}

// Reader reads AIOAuditResults from storage
type Reader interface {
	// Get returns the AIOAuditResult specified by key, or a verrors.NotFound error if the AIOAuditResult is missing
	Get(ctx context.Context, key model.Key) (*model.AIOAuditResult, error)
	// GetMulti returns AIOAuditResults or nils for missing entities in the same order that the keys are passed
	//
	// This method does not return a verrors.NotFound even if all the AIOAuditResults are missing,
	// you need to handle the list of results.
	GetMulti(ctx context.Context, keys []model.Key) ([]*model.AIOAuditResult, error)

	// List returns a list of AIOAuditResults that match the given column values.
	// Deleted AIOAuditResult are not included.
	// Key filters work like this: https://github.com/vendasta/vstore/blob/cdf5080ff/vstore/sdks/go/v1/api.go#L244-L253
	// An empty key filter means don't filter by key columns.
	List(ctx context.Context, keyFilter model.Key, cursor string, pageSize int64) (results []*model.AIOAuditResult, nextCursor string, hasMore bool, err error)
	// ListByDate returns a list of AIOAuditResult entries for a business within a given date range.
	ListByDate(ctx context.Context, businessID string, startDate, endDate time.Time) (results []*model.AIOAuditResult, err error)
	// ListByWebsiteURL returns a list of AIOAuditResult entries for a business and website URL within a given date range.
	ListByWebsiteURL(ctx context.Context, businessID string, websiteURL string, startDate, endDate time.Time) (results []*model.AIOAuditResult, err error)
}

// Writer writes AIOAuditResults to storage
type Writer interface {
	// Create will transactionally insert a AIOAuditResult into storage.
	// This method will return a verrors.AlreadyExists if the AIOAuditResult already exists.
	Create(ctx context.Context, aIOAuditResult *model.AIOAuditResult) error
	// Mutate will apply the given MutateFuncs to an existing AIOAuditResult specified by the given key.
	// Mutations are applied in order inside a transaction that can automatically retry on transient failures.
	Mutate(ctx context.Context, key model.Key, mutations ...MutateFunc) error
	// Upsert will apply the given MutateFuncs to a AIOAuditResult specified by the given key.
	//
	// If the AIOAuditResult exists, then this function works similarly to Mutate, but if the AIOAuditResult doesn't exist,
	// then this function will create it and apply the mutations to a zero valued AIOAuditResult.
	// This can be used when the distinction between inserting a new entity and updating an existing entity is unimportant.
	//
	// Mutations are applied in order inside a transaction that can automatically retry on transient failures.
	Upsert(ctx context.Context, key model.Key, mutations ...MutateFunc) error
}
