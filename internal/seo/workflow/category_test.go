package seoworkflow

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/short-hop/vmockhelper"
	"github.com/stretchr/testify/assert"
	cssdk "github.com/vendasta/CS/sdks/go/v1"
	category "github.com/vendasta/category/sdks/go/v1"
	category_v1 "github.com/vendasta/generated-protos-go/category/v1"
	listing_products_v1 "github.com/vendasta/generated-protos-go/listing_products/v1"
	"github.com/vendasta/generated-protos-go/vstorepb"
	"github.com/vendasta/listing-products/internal/dataforseo"
	model "github.com/vendasta/listing-products/internal/dataforseocategories"
	repository "github.com/vendasta/listing-products/internal/dataforseocategories/repository"
	dataforseocategoriesservice "github.com/vendasta/listing-products/internal/dataforseocategories/service"
	listingprofilemodel "github.com/vendasta/listing-products/internal/listingprofile/model"
	listingprofileservice "github.com/vendasta/listing-products/internal/listingprofile/service"
	seodata "github.com/vendasta/listing-products/internal/seo/model"
	seodataservice "github.com/vendasta/listing-products/internal/seo/service"
	suggestionsService "github.com/vendasta/listing-products/internal/suggestions/service"
	"go.temporal.io/sdk/testsuite"
	"go.temporal.io/sdk/worker"
)

func Test_CategoryWorkflow(t *testing.T) {
	type testCase struct {
		name                     string
		workflowParams           *CategoryWorkflowParams
		listingProfile           *listingprofilemodel.ListingProfile
		existingSEOData          []*seodata.SEOData
		existingDataLakeResponse *dataforseo.GoogleMapsSERPResponse
		dataForSEOPost           *DataForSeoPost
		dataForSEOGetResponse    *dataforseo.GoogleMapsSERPResponse
		expectedCategoryEntry    *model.DataForSEOCategories
		expectedResponse         interface{}
		expectedWorkflowErr      error
	}
	cases := []*testCase{
		{
			name: "Test retries when results are not ready yet",
			workflowParams: &CategoryWorkflowParams{
				BusinessID: "AG-1",
				Date:       "2020-01-01",
			},
			listingProfile: &listingprofilemodel.ListingProfile{
				BusinessID:   "AG-1",
				CompanyName:  "Dr. John Doe",
				Website:      "www.website.com",
				Address:      "Address1",
				WorkNumber:   []string{"**************"},
				Location:     &vstorepb.GeoPoint{Latitude: 10, Longitude: 20},
				VCategoryIDs: []string{"oldCategory"},
			},
			dataForSEOPost: &DataForSeoPost{
				Req: []*dataforseo.GoogleMapsSERPTaskPostRequest{
					{
						BusinessID:   "AG-1",
						Keyword:      "Dr. John Doe",
						LanguageCode: "en",
						Location:     &dataforseo.MapLocation{Latitude: 10, Longitude: 20, Zoom: 13},
						Vicinity:     "VICINITY_C3",
					},
				},
				Resp: &dataforseo.GoogleMapsSERPResponse{
					Tasks: []*dataforseo.GoogleMapsTask{
						{
							ID:            "id1",
							StatusCode:    20100,
							StatusMessage: "",
						},
					},
				},
			},
			dataForSEOGetResponse: &dataforseo.GoogleMapsSERPResponse{
				Tasks: []*dataforseo.GoogleMapsTask{
					{
						ID:            "id1",
						StatusCode:    20100,
						StatusMessage: "",
						Result: []*dataforseo.GoogleMapsResult{
							{
								Items: []*dataforseo.GoogleMapsItem{
									{
										Type:         "maps_search",
										Rank:         1,
										BusinessName: "Name1",
										URL:          "Website1",
										Address:      "Address1",
										Phone:        "**************",
									},
									{
										Type:         "maps_search",
										Rank:         2,
										BusinessName: "Dr. John Doe",
										URL:          "www.website.com",
										Address:      "Address1",
										Phone:        "**************",
										CategoryIds: []string{
											"PrimaryCategory", "SecondaryCategory", "SecondaryCategory2",
										},
									},
									{
										Type:         "maps_search",
										Rank:         3,
										BusinessName: "NOMATCH",
										URL:          "NOMATCH",
										Address:      "NOMATCH",
										Phone:        "**************",
									},
								},
							},
						},
					},
				},
			},
			expectedCategoryEntry: &model.DataForSEOCategories{
				BusinessID:        "AG-1",
				PrimaryCategoryID: "PrimaryCategory",
				CategoryIDs: []string{
					"PrimaryCategory", "SecondaryCategory", "SecondaryCategory2",
				},
				TaskID:      "id1",
				RawResponse: `{"type":"maps_search","rank_group":2,"rank_absolute":0,"domain":"","title":"Dr. John Doe","url":"www.website.com","contact_url":"","rating_distribution":null,"snippet":"","address":"Address1","place_id":"","phone":"**************","main_image":"","total_photos":0,"category":"","additional_categories":null,"category_ids":["PrimaryCategory","SecondaryCategory","SecondaryCategory2"],"feature_id":"","cid":"","latitude":0,"longitude":0,"is_claimed":false,"local_justifications":null,"is_directory_item":false,"rating":null,"address_info":null,"work_hours":null}`,
			},
			expectedResponse:    nil,
			expectedWorkflowErr: nil,
		},
		{
			name: "Test fetches data from data lake when it exists",
			workflowParams: &CategoryWorkflowParams{
				BusinessID: "AG-1",
				Date:       "2020-01-01",
			},
			listingProfile: &listingprofilemodel.ListingProfile{
				BusinessID:   "AG-1",
				CompanyName:  "Dr. John Doe",
				Website:      "www.website.com",
				Address:      "Address1",
				WorkNumber:   []string{"**************"},
				Location:     &vstorepb.GeoPoint{Latitude: 10, Longitude: 20},
				VCategoryIDs: []string{"oldCategory"},
			},
			existingSEOData: []*seodata.SEOData{
				{
					LocalRank: 1,
					Keyword:   "Dr. John Doe",
					Date:      "2020-01-01",
				},
			},
			existingDataLakeResponse: &dataforseo.GoogleMapsSERPResponse{
				Tasks: []*dataforseo.GoogleMapsTask{
					{
						ID:         "id1",
						StatusCode: 20100,
						Result: []*dataforseo.GoogleMapsResult{
							{
								Items: []*dataforseo.GoogleMapsItem{
									{
										Type:         "maps_search",
										Rank:         1,
										BusinessName: "Name1",
										URL:          "Other Website",
										Address:      "Other Address",
									},
									{
										Type:         "maps_search",
										Rank:         2,
										BusinessName: "Dr. John Doe",
										URL:          "www.website.com",
										Address:      "Address1",
										CategoryIds: []string{
											"PrimaryCategory", "SecondaryCategory", "SecondaryCategory2",
										},
									},
								},
							},
						},
					},
				},
			},
			expectedCategoryEntry: &model.DataForSEOCategories{
				BusinessID:        "AG-1",
				PrimaryCategoryID: "PrimaryCategory",
				CategoryIDs: []string{
					"PrimaryCategory", "SecondaryCategory", "SecondaryCategory2",
				},
				TaskID:      "id1",
				RawResponse: `{"type":"maps_search","rank_group":2,"rank_absolute":0,"domain":"","title":"Dr. John Doe","url":"www.website.com","contact_url":"","rating_distribution":null,"snippet":"","address":"Address1","place_id":"","phone":"","main_image":"","total_photos":0,"category":"","additional_categories":null,"category_ids":["PrimaryCategory","SecondaryCategory","SecondaryCategory2"],"feature_id":"","cid":"","latitude":0,"longitude":0,"is_claimed":false,"local_justifications":null,"is_directory_item":false,"rating":null,"address_info":null,"work_hours":null}`,
			},
			expectedResponse:    nil,
			expectedWorkflowErr: nil,
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			ctx := context.Background()
			ctrl := gomock.NewController(t)

			mockLpService := listingprofileservice.NewMockInterface(ctrl)

			mockLpService.EXPECT().Get(gomock.Any(), c.listingProfile.BusinessID, false).Return(c.listingProfile, nil).AnyTimes()

			mockDataForSEO := dataforseo.NewMockSERPClient(ctrl)

			mockSEOData := seodataservice.NewMockService(ctrl)
			mockSEOData.EXPECT().QueryDateRange(gomock.Any(), c.workflowParams.BusinessID, gomock.Any(), gomock.Any()).Return(c.existingSEOData, nil).AnyTimes()
			if len(c.existingSEOData) > 0 {
				mockDataForSEO.EXPECT().GoogleMapsSERPTaskGet(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), "", false).Return(c.existingDataLakeResponse, nil).Times(1)
			} else {
				mockDataForSEO.EXPECT().GoogleMapsSERPTaskPost(gomock.Any(), c.workflowParams.Date, true, c.dataForSEOPost.Req, gomock.Any()).Return(c.dataForSEOPost.Resp, nil).Times(1)
				mockDataForSEO.EXPECT().GoogleMapsSERPTaskGet(gomock.Any(), c.workflowParams.BusinessID, c.listingProfile.CompanyName, listing_products_v1.Vicinity_VICINITY_C3.String(), c.workflowParams.Date, c.dataForSEOPost.Resp.Tasks[0].ID, true).Return(c.dataForSEOGetResponse, nil).Times(1)
			}

			vmockhelper.MockCallsAndPrintExpected(mockDataForSEO, "mockDataForSEO")

			mockDataForSEOCategory := dataforseocategoriesservice.NewMockService(ctrl)

			mockDataForSEOCategory.EXPECT().Upsert(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, businessID string, mutators ...repository.MutateFunc) error {
				result := &model.DataForSEOCategories{
					BusinessID: c.listingProfile.BusinessID,
				}
				for _, mutator := range mutators {
					err := mutator(ctx, result)
					if err != nil {
						return err
					}
				}
				assert.Equal(t, c.expectedCategoryEntry, result)
				return nil
			}).Times(1)

			mockCSGMB := cssdk.NewMockGoogleMyBusinessClientInterface(ctrl)
			mockCSGMB.EXPECT().GetGoogleMyBusinessLocation(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&cssdk.GoogleMyBusinessLocation{}, nil).AnyTimes()

			mockCategory := category.NewMockCategoriesClientInterface(ctrl)
			mockCategory.EXPECT().GetCategoryByExternalIDsAndType(gomock.Any(), gomock.Any()).Return(&category_v1.GetCategoryByExternalIDsAndTypeResponse{
				Category: []*category_v1.Category{
					{
						ExternalId: c.expectedCategoryEntry.PrimaryCategoryID,
					},
				},
			}, nil).AnyTimes()

			mockSuggestions := suggestionsService.NewMockService(ctrl)
			mockSuggestions.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

			testSuite := &testsuite.WorkflowTestSuite{}
			env := testSuite.NewTestWorkflowEnvironment()
			env.RegisterWorkflow(CategoryWorkflow)
			env.RegisterActivity(getExistingItemFromDataLake)
			env.RegisterActivity(postCategoryMapsRequest)
			env.RegisterActivity(getSERPItemForCategoryTask)
			env.RegisterActivity(storeDataForSEOCategory)
			ctx = NewActivityContext(ctx, nil, mockLpService, nil, mockSEOData, mockDataForSEO, nil, nil, nil, nil, nil, nil, mockDataForSEOCategory, nil, mockCSGMB, mockCategory, mockSuggestions, nil, nil, nil, nil, nil)

			env.SetWorkerOptions(worker.Options{
				BackgroundActivityContext: ctx,
			})

			env.ExecuteWorkflow(CategoryWorkflow, c.workflowParams)

			if !env.IsWorkflowCompleted() {
				t.Errorf("Workflow failed to complete. Error (%s)", env.GetWorkflowError())
			}

			assert.Equal(t, c.expectedWorkflowErr, env.GetWorkflowError())
		})
	}
}
