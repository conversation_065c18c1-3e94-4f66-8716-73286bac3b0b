This is a simple sdk to interact with the open ai completions api: https://beta.openai.com/docs/api-reference/completions

## Usage

This sdk uses secret manager to store the api key. The service accounts for your project will need to have the role `Secret Manager Secret Accessor` assigned to the secrets permissions:

- https://console.cloud.google.com/security/secret-manager/secret/open-ai-api-key/permissions?project=repcore-prod

```go
    import (
    	secretmanager "cloud.google.com/go/secretmanager/apiv1"
		"github.com/vendasta/gosdks/openai"
	)

	// Retrieve secrets
	secretManagerClient, err := secretmanager.NewClient(ctx)
	if err != nil {
        logging.Criticalf(ctx, "your exit message here")
        os.Exit(yourExitCode)
	}
	defer secretManagerClient.Close()
	
	// create open ai client
	client, err := openai.NewClient(ctx, secretManagerClient)
    if err != nil {
        logging.Criticalf(ctx, "your exit message here")
        os.Exit(yourExitCode)
    }
	
	// create a completion
	message, _ := client.CreateCompletion(ctx context.Context, "Write me a tweet about vendasta technologies in saskatoon saskatchewan")
```

## Chat Completion

### Function calls

`CreateChatCompletion` supports defining functions that can be requested to be called when more information is required to answer a chat query.
The `WithFunction` option takes in a `FunctionDefinition` which names and describes the function and its parameters.

The `FunctionDefinition` parameters should be a struct that is annotated with the `description` and `required` tags where appropriate. For example:

```
	type BusinessInfoParams struct {
		BusinessID            string `description:"The id of the business to fetch information for" required:"true"`
		IncludeContactDetails bool   `description:"Whether or not to include information about the contact for the business"`
	}
```

When calling chat completion with a function definition provided, the response will include `ToolCalls` if function calls are requested.
In this case, a new chat completion should be requested that includes the response message appended to the previous chat messages, followed by messages containing the function result for each function call requested:

```
    func getBusinessInfo(ctx context.Context, params *BusinessInfoParams) string {
        // Gets formatted information about a business
        // ....
    }
    
    func respondToUser(request request []openai.ChatCompletionMessageRequest) string {
        //...
        
		response, err := openAIClient.CreateChatCompletion(ctx, request, openai.WithModel(openai.GPT4), openai.WithFunction(openai.FunctionDefinition{
			Name:        "getBusinessInfo",
			Description: "This function will return information about a business's location and services",
			Parameters:  BusinessInfoParams{},
		}))

		if len(response.ToolCalls) > 0 {
			request = append(request, openai.ChatCompletionMessageRequest{
				Role:      string(response.Role),
				ToolCalls: response.ToolCalls,
			})

			for _, toolCall := range response.ToolCalls {
				content := ""
				switch toolCall.Function.Name {
				case "getBusinessInfo":
					content = getBusinessInfo(ctx, toolCall.Function.Arguments.(*BusinessInfoParams))
				}

				request = append(request, openai.ChatCompletionMessageRequest{
					Role:       string(openai.Tool),
					Content:    content,
					ToolCallID: toolCall.ID,
				})
			}

			response, err = openAIClient.CreateChatCompletion(ctx, request, openai.WithModel(openai.GPT4))
		}

		return response.Content
    }
```

## Image Generation

The SDK supports image generation using both DALL-E models (dall-e-2, dall-e-3) and the new GPT-image-1 model.

### DALL-E Models

```go
    // Using DALL-E-3
    request := openai.CreateImagesRequest{
        Prompt:  "A beautiful sunset over mountains",
        Model:   openai.CreateImageModelDallE3,
        Size:    openai.CreateImageSize1024x1024,
        N:       1,
        Quality: openai.CreateImageQualityHD,
        Style:   openai.CreateImageStyleVivid,
    }

    images, err := client.CreateImages(ctx, request)
    if err != nil {
        // handle error
    }

    // Access the generated image
    imageURL := images[0].URL
```

### GPT-image-1 Model

```go
    // Using GPT-image-1 with advanced features
    request := openai.CreateImagesRequest{
        Prompt:            "Parrot on a skateboard performing a trick. Large bold text \"SKATE MASTER\" banner at the bottom of the image. Cartoon style, natural light, high detail, 1:1 aspect ratio.",
        Model:             openai.CreateImageModelGptImage1,
        Size:              openai.CreateImageSize1024x1024,
        N:                 1,
        Quality:           openai.CreateImageQualityHigh,
        Background:        openai.CreateImageBackgroundOpaque,
        OutputFormat:      openai.CreateImageOutputFormatJPEG,
        OutputCompression: 90,
    }

    images, err := client.CreateImages(ctx, request)
    if err != nil {
        // handle error
    }

    // Access the generated image
    imageURL := images[0].B64JSON
```

### Supported Parameters by Model

**DALL-E Models (dall-e-2, dall-e-3):**
- `Prompt` - Text description of the desired image
- `Model` - Model to use (`dall-e-2`, `dall-e-3`)
- `Size` - Image dimensions (e.g., `1024x1024`)
- `N` - Number of images to generate (1-10)
- `Quality` - Image quality (`standard`, `hd`)
- `Style` - Image style (`vivid`, `natural`) - DALL-E-3 only
- `ResponseFormat` - Response format (`url`, `b64_json`)

**GPT-image-1 Model:**
- `Prompt` - Text description of the desired image
- `Model` - Model to use (`gpt-image-1`)
- `Size` - Image dimensions (includes portrait and landscape options)
- `N` - Number of images to generate (1-10)
- `Quality` - Image quality (`high`, `medium`, `low`)
- `Background` - Background transparency (`transparent`, `opaque`)
- `OutputFormat` - Output format (`png`, `jpeg`, `webp`)
- `OutputCompression` - Compression level (1-100, higher = more compression)

## Integration Tests
To run the integrations tests, you will need to set the environment variable `INTEGRATION_TEST` to `true`.

You can do that by running the following command:
```INTEGRATION_TEST=true go test ./...```