// Code generated by the Vendasta codegen tool (github.com/vendasta/codegen). DO NOT EDIT.
// This means that any changes you make to this file may be overwritten by a subsequent run of the codegen tool, so be cautious.
package aioauditresultrepository

import (
	"context"
	"time"

	"github.com/vendasta/gosdks/logging"
	"github.com/vendasta/gosdks/validation"
	"github.com/vendasta/gosdks/verrors"
	model "github.com/vendasta/listing-products/internal/seo/aioauditresult"
	vstore "github.com/vendasta/vstore/vstore/sdks/go/v1"
)

// New returns a new VStoreRepository object
func New(client vstore.Interface) *VStoreRepository {
	return &VStoreRepository{client: client}
}

// VStoreRepository implements Repository using VStore as a storage device
type VStoreRepository struct {
	client vstore.Interface
}

// Create will transactionally insert a AIOAuditResult into storage.
// Create sets the Created time on the AIOAuditResult automatically.
// Create sets the Updated time on the AIOAuditResult automatically.
// This method will return a verrors.AlreadyExists if the AIOAuditResult already exists.
func (r *VStoreRepository) Create(ctx context.Context, x *model.AIOAuditResult) error {
	err := r.client.ReadWriteTransaction(ctx, func(ctx context.Context, t vstore.Transaction) error {
		o := &model.AIOAuditResult{}
		err := t.Get(ctx, model.NewKey(x.BusinessID, x.WebsiteURL, x.AuditDate).KeySet(), o)
		if err == vstore.ErrNoSuchEntity {
			x.Created = time.Now().UTC()
			x.Updated = x.Created
			err = validation.NewValidator().Rule(x).Validate()
			if err != nil {
				logging.Debugf(ctx, "Validation error during creation of AIOAuditResult:%s", err.Error())
				return err
			}
			return t.Insert(model.Kind, x)
		}
		if err != nil {
			return err
		}
		// overwriting a AIOAuditResult that isn't marked deleted is not allowed
		if o.Deleted.IsZero() {
			return vstore.ErrEntityAlreadyExists
		}
		x.Created = time.Now().UTC()
		x.Updated = x.Created
		err = validation.NewValidator().Rule(x).Validate()
		if err != nil {
			logging.Debugf(ctx, "Validation error during creation of AIOAuditResult:%s", err.Error())
			return err
		}
		return t.Replace(model.Kind, x)
	})
	if err == vstore.ErrEntityAlreadyExists {
		return verrors.New(verrors.AlreadyExists, "AIOAuditResult already exists")
	}
	return err
}

// Get returns the AIOAuditResult specified by key, or a verrors.NotFound error if the AIOAuditResult is missing
func (r *VStoreRepository) Get(ctx context.Context, key model.Key) (*model.AIOAuditResult, error) {
	e := &model.AIOAuditResult{}
	err := r.client.Get(ctx, key.KeySet(), e)
	if err == vstore.ErrNoSuchEntity {
		return nil, verrors.New(verrors.NotFound, "AIOAuditResult not found")
	}
	if err != nil {
		return nil, err
	}

	if !e.Deleted.IsZero() {
		return nil, verrors.New(verrors.NotFound, "AIOAuditResult not found")
	}

	return e, nil
}

// GetMulti returns AIOAuditResults or nils for missing entities in the same order that the keys are passed
//
// This method does not return a verrors.NotFound even if all the AIOAuditResults are missing,
// you need to handle the list of results.
func (r *VStoreRepository) GetMulti(ctx context.Context, keys []model.Key) ([]*model.AIOAuditResult, error) {
	ks := make([]vstore.KeySet, len(keys))
	for i := range keys {
		ks[i] = keys[i].KeySet()
	}
	e := make([]*model.AIOAuditResult, len(keys))
	err := r.client.GetMulti(ctx, ks, e)
	if err != nil {
		return nil, err
	}

	// remove any AIOAuditResults marked deleted from the results
	for i, o := range e {
		if o == nil {
			continue
		}
		if !o.Deleted.IsZero() {
			e[i] = nil
		}
	}

	return e, nil
}

// List see repository.Reader interface.
func (r *VStoreRepository) List(ctx context.Context, keyFilter model.Key, cursor string, pageSize int64) (results []*model.AIOAuditResult, nextCursor string, hasMore bool, err error) {
	query := vstore.NewQuery(model.Kind)
	if pageSize < 1 {
		return nil, "", false,
			verrors.New(verrors.InvalidArgument, "must give page-size for %s.List", model.Kind)
	}
	query.Limit(pageSize)

	if keyFilter != (model.Key{}) {
		var keys []string
		for _, k := range keyFilter.KeySet().Keys() {
			if k != "" {
				keys = append(keys, k)
			}
		}
		query.KeyFilter(keys)
	}

	query.Where("deleted", nil)

	if cursor != "" {
		query.Cursor(cursor)
	}

	resultsIterator, err := r.client.Query(ctx, query)
	if err != nil {
		return nil, "", false, err
	}
	err = resultsIterator.Do(func(result vstore.Result) error {
		m := &model.AIOAuditResult{}
		err := result.ToStruct(m)
		if err != nil {
			return err
		}
		results = append(results, m)
		return nil
	})
	if err != nil {
		return nil, "", false, err
	}

	return results, resultsIterator.Cursor(), resultsIterator.HasMore(), nil
}

func (r *VStoreRepository) ListByDate(ctx context.Context, businessID string, startDate, endDate time.Time) (results []*model.AIOAuditResult, err error) {
	query := vstore.NewQuery(model.Kind).Index(model.BusinessIDAuditDateDeletedIndex)
	query.Limit(400)

	var days []string
	for d := startDate; d.Before(endDate); d = d.AddDate(0, 0, 1) {
		days = append(days, d.Format("2006-01-02"))
	}

	query.Where("business_id", businessID)
	query.WhereColumnInList("audit_date", days)
	query.Where("deleted", nil)

	resultsIterator, err := r.client.Query(ctx, query)
	if err != nil {
		return nil, err
	}
	err = resultsIterator.Do(func(result vstore.Result) error {
		m := &model.AIOAuditResult{}
		err := result.ToStruct(m)
		if err != nil {
			return err
		}
		results = append(results, m)
		return nil
	})
	if err != nil {
		return nil, err
	}

	return results, nil
}

func (r *VStoreRepository) ListByWebsiteURL(ctx context.Context, businessID string, websiteURL string, startDate, endDate time.Time) (results []*model.AIOAuditResult, err error) {
	query := vstore.NewQuery(model.Kind).Index(model.BusinessIDAuditDateDeletedIndex)
	query.Limit(400)

	var days []string
	for d := startDate; d.Before(endDate); d = d.AddDate(0, 0, 1) {
		days = append(days, d.Format("2006-01-02"))
	}

	query.Where("business_id", businessID)
	query.Where("website_url", websiteURL)
	query.WhereColumnInList("audit_date", days)
	query.Where("deleted", nil)

	resultsIterator, err := r.client.Query(ctx, query)
	if err != nil {
		return nil, err
	}
	err = resultsIterator.Do(func(result vstore.Result) error {
		m := &model.AIOAuditResult{}
		err := result.ToStruct(m)
		if err != nil {
			return err
		}
		results = append(results, m)
		return nil
	})
	if err != nil {
		return nil, err
	}

	return results, nil
}

// Upsert will apply the given MutateFuncs to a AIOAuditResult specified by the given key.
//
// If the AIOAuditResult exists, then this function works similarly to Mutate, but if the AIOAuditResult doesn't exist,
// then this function will create it and apply the mutations to a zero valued AIOAuditResult.
// This can be used when the distinction between inserting a new entity and updating an existing entity is unimportant.
// Upsert sets the Created time on the AIOAuditResult automatically.
// Upsert sets the Updated time on the AIOAuditResult automatically.
// Mutations are applied in order inside a transaction that can automatically retry on transient failures.
func (r *VStoreRepository) Upsert(ctx context.Context, key model.Key, mutations ...MutateFunc) error {
	return r.client.ReadWriteTransaction(ctx, func(ctx context.Context, t vstore.Transaction) error {
		e := &model.AIOAuditResult{}
		err := t.Get(ctx, key.KeySet(), e)
		if err != nil && err != vstore.ErrNoSuchEntity {
			return err
		}

		now := time.Now().UTC()

		if err == vstore.ErrNoSuchEntity {
			e.Created = now
		}

		// Upsert should not mutate a AIOAuditResult that is marked deleted
		// Instead we mutate a zero value struct, resetting the values of the deleted row
		if !e.Deleted.IsZero() {
			e = &model.AIOAuditResult{}
			e.Created = now
		}

		e.Updated = now
		e.BusinessID = key.BusinessID
		e.WebsiteURL = key.WebsiteURL
		e.AuditDate = key.AuditDate
		for _, m := range mutations {
			err = m(ctx, e)
			if err != nil {
				return err
			}
		}

		err = validation.NewValidator().
			Rule(e).
			Validate()
		if err != nil {
			logging.Debugf(ctx, "Validation error during creation of AIOAuditResult:%s", err.Error())
			return err
		}

		return t.Replace(model.Kind, e)
	})
}

// Mutate will apply the given MutateFuncs to an existing AIOAuditResult specified by the given key.
// Mutations are applied in order inside a transaction that can automatically retry on transient failures.
// Mutate sets the Updated time on the AIOAuditResult automatically.
// Mutate will return a NotFound error if the entity is marked deleted.
func (r *VStoreRepository) Mutate(ctx context.Context, key model.Key, mutations ...MutateFunc) error {
	return r.client.ReadWriteTransaction(ctx, func(ctx context.Context, t vstore.Transaction) error {
		e := &model.AIOAuditResult{}
		err := t.Get(ctx, key.KeySet(), e)
		if err == vstore.ErrNoSuchEntity {
			return verrors.New(verrors.NotFound, "AIOAuditResult not found")
		}
		if err != nil {
			return err
		}

		// mutating a AIOAuditResult that is marked deleted is not allowed
		if !e.Deleted.IsZero() {
			return verrors.New(verrors.NotFound, "AIOAuditResult not found")
		}

		e.Updated = time.Now().UTC()
		for _, m := range mutations {
			err = m(ctx, e)
			if err != nil {
				return err
			}
		}

		err = validation.NewValidator().
			Rule(e).
			Validate()
		if err != nil {
			logging.Debugf(ctx, "Validation error during creation of AIOAuditResult:%s", err.Error())
			return err
		}

		return t.Update(model.Kind, e)
	})
}
