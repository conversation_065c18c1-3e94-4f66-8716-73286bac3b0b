package seoworkflow

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/vendasta/gosdks/openai"
	"github.com/vendasta/gosdks/openai/jsonschema"
	"go.temporal.io/sdk/activity"
	"go.temporal.io/sdk/temporal"
	"go.temporal.io/sdk/workflow"

	"github.com/vendasta/listing-products/internal/dataforseo"
	"github.com/vendasta/listing-products/internal/seo/aioauditresult"
)

// AIOAudit workflow types and functions
type AIOAuditWorkflowParams struct {
	BusinessID   string
	Website      string
	BusinessName string
	AuditDate    string
}

func AIOAuditWorkflow(ctx workflow.Context, params *AIOAuditWorkflowParams) error {

	activityCtx := getAIOActivityCtx(ctx)

	buildAuditRequestParam := &buildAuditRequestParams{
		BusinessID:   params.BusinessID,
		Website:      params.Website,
		BusinessName: params.BusinessName,
		AuditDate:    params.AuditDate,
	}
	var buildAuditResponse *buildAuditRequestResponse
	err := workflow.ExecuteActivity(activityCtx, buildAuditRequestActivity, buildAuditRequestParam).Get(ctx, &buildAuditResponse)
	if err != nil {
		return handleWorkflowError(ctx, params, err)
	}

	startSiteCrawlParam := &startSiteCrawlParams{
		BusinessID:   params.BusinessID,
		Website:      params.Website,
		BusinessName: params.BusinessName,
		AuditDate:    params.AuditDate,
		Request:      buildAuditResponse.Request,
	}
	var startSiteCrawlResponse *startSiteCrawlResponse
	err = workflow.ExecuteActivity(activityCtx, startSiteCrawlActivity, startSiteCrawlParam).Get(ctx, &startSiteCrawlResponse)
	if err != nil {
		return handleWorkflowError(ctx, params, err)
	}

	// Sleep 5 minutes to allow tasks to complete before checking status
	workflow.Sleep(ctx, 5*time.Minute)

	checkCrawlStatusParam := &checkCrawlStatusParams{
		BusinessID:   params.BusinessID,
		Website:      params.Website,
		BusinessName: params.BusinessName,
		AuditDate:    params.AuditDate,
		TaskID:       startSiteCrawlResponse.TaskID,
	}
	err = workflow.ExecuteActivity(getRetryCtx(ctx), checkCrawlStatusActivity, checkCrawlStatusParam).Get(ctx, nil)
	if err != nil {
		return handleWorkflowError(ctx, params, err)
	}

	getCrawlResultsParam := &getCrawlResultsParams{
		BusinessID:   params.BusinessID,
		Website:      params.Website,
		BusinessName: params.BusinessName,
		AuditDate:    params.AuditDate,
		TaskID:       startSiteCrawlResponse.TaskID,
	}
	var getCrawlResultsResponse *getCrawlResultsResponse
	err = workflow.ExecuteActivity(activityCtx, getCrawlResultsActivity, getCrawlResultsParam).Get(ctx, &getCrawlResultsResponse)
	if err != nil {
		return handleWorkflowError(ctx, params, err)
	}

	generateAiInsightsParam := &generateAiInsightsParams{
		BusinessID:   params.BusinessID,
		Website:      params.Website,
		BusinessName: params.BusinessName,
		TaskID:       startSiteCrawlResponse.TaskID,
		URLs:         getCrawlResultsResponse.URLs,
		Summary:      getCrawlResultsResponse.Summary,
		Pages:        getCrawlResultsResponse.Pages,
		AuditDate:    params.AuditDate,
	}

	var generateAiInsightsResponse *generateAiInsightsResponse
	err = workflow.ExecuteActivity(activityCtx, generateAiInsightsActivity, generateAiInsightsParam).Get(ctx, &generateAiInsightsResponse)
	if err != nil {
		return handleWorkflowError(ctx, params, err)
	}

	return nil
}

// AIOAudit activity parameter types
type buildAuditRequestParams struct {
	BusinessID   string
	Website      string
	BusinessName string
	AuditDate    string
}

type buildAuditRequestResponse struct {
	Request *dataforseo.OnPageTaskPostRequest
}

type startSiteCrawlParams struct {
	BusinessID   string
	Website      string
	BusinessName string
	AuditDate    string
	Request      *dataforseo.OnPageTaskPostRequest
}

type startSiteCrawlResponse struct {
	TaskID string
}

type checkCrawlStatusParams struct {
	BusinessID   string
	Website      string
	BusinessName string
	AuditDate    string
	TaskID       string
}

type getCrawlResultsParams struct {
	BusinessID   string
	Website      string
	BusinessName string
	AuditDate    string
	TaskID       string
}

type getCrawlResultsResponse struct {
	Summary *dataforseo.OnPageSummaryResponse
	Pages   *dataforseo.OnPagePagesResponse
	URLs    []string
}

type generateAiInsightsParams struct {
	BusinessID   string
	Website      string
	BusinessName string
	TaskID       string
	URLs         []string
	Summary      *dataforseo.OnPageSummaryResponse
	Pages        *dataforseo.OnPagePagesResponse
	AuditDate    string
}

type generateAiInsightsResponse struct {
	AuditResponse *AIOAuditResponse
}

// AIOAudit activity functions
func buildAuditRequestActivity(ctx context.Context, params *buildAuditRequestParams) (*buildAuditRequestResponse, error) {
	logger := activity.GetLogger(ctx)

	// Prepare DataForSEO on_page request for AIO audit
	request := &dataforseo.OnPageTaskPostRequest{
		Target:                  params.Website,
		MaxCrawlPages:           50,
		CrawlMode:               "recursive",
		LoadResources:           true,
		StoreRawHTML:            true,
		EnableAIContentAnalysis: true,
		EnableJavascript:        false,
		Tag:                     dataforseo.MakeTag(params.BusinessID, "", "aio_audit"),
		CustomUserAgent:         "GPTBot/1.0 (+https://openai.com/gptbot)",
	}

	// Validate required parameters
	if params.Website == "" {
		logger.Error("[AIO_AUDIT] Website URL is empty", "business_id", params.BusinessID)
		storeFailedAuditResult(ctx, params.BusinessID, params.Website, params.AuditDate, "Website URL is empty", map[string]interface{}{
			"business_id":   params.BusinessID,
			"business_name": params.BusinessName,
		})
		return nil, temporal.NewApplicationError("Website URL is empty", "invalid_parameters")
	}

	if params.BusinessID == "" {
		logger.Error("[AIO_AUDIT] Business ID is empty", "website", params.Website)
		storeFailedAuditResult(ctx, params.BusinessID, params.Website, params.AuditDate, "Business ID is empty", map[string]interface{}{
			"website":       params.Website,
			"business_name": params.BusinessName,
		})
		return nil, temporal.NewApplicationError("Business ID is empty", "invalid_parameters")
	}

	response := &buildAuditRequestResponse{
		Request: request,
	}

	return response, nil
}

func startSiteCrawlActivity(ctx context.Context, params *startSiteCrawlParams) (*startSiteCrawlResponse, error) {
	logger := activity.GetLogger(ctx)

	// Get DataForSEO client from activity services
	services := getActivityServices(ctx)

	// Call DataForSEO on_page API to start the site crawl
	response, err := services.dataForSEOClient.OnPageTaskPost(ctx, []*dataforseo.OnPageTaskPostRequest{params.Request}, dataforseo.RequestSourcePaidKeywordTracking)
	if err != nil {
		logger.Error("[AIO_AUDIT] Failed to send DataForSEO on_page request", "business_id", params.BusinessID, "website", params.Website, "error", err.Error())
		storeFailedAuditResult(ctx, params.BusinessID, params.Website, params.AuditDate, "Failed to send DataForSEO on_page request", map[string]interface{}{
			"error":         err.Error(),
			"business_name": params.BusinessName,
		})
		return nil, temporal.NewApplicationError("Failed to send DataForSEO on_page request", "failed_task_post", err.Error())
	}
	if len(response.Tasks) < 1 {
		logger.Error("[AIO_AUDIT] No tasks returned from DataForSEO on_page", "business_id", params.BusinessID, "website", params.Website)
		storeFailedAuditResult(ctx, params.BusinessID, params.Website, params.AuditDate, "No tasks returned from DataForSEO on_page", map[string]interface{}{
			"business_name": params.BusinessName,
		})
		return nil, temporal.NewApplicationError("no tasks returned from DataForSEO on_page", "failed_task_post")
	}
	task := response.Tasks[0]
	if task.StatusCode >= 40000 {
		logger.Error("[AIO_AUDIT] Task failed to post", "business_id", params.BusinessID, "website", params.Website, "task_id", task.ID, "status_code", task.StatusCode, "status_message", task.StatusMessage)
		storeFailedAuditResult(ctx, params.BusinessID, params.Website, params.AuditDate, "Task failed to post", map[string]interface{}{
			"task_id":        task.ID,
			"status_code":    task.StatusCode,
			"status_message": task.StatusMessage,
			"business_name":  params.BusinessName,
		})
		return nil, temporal.NewApplicationError("Task failed to post", "failed_task_post", task, task.StatusMessage)
	}

	crawlResponse := &startSiteCrawlResponse{
		TaskID: task.ID,
	}

	return crawlResponse, nil
}

func checkCrawlStatusActivity(ctx context.Context, params *checkCrawlStatusParams) error {
	logger := activity.GetLogger(ctx)

	// Get DataForSEO client from activity services
	services := getActivityServices(ctx)

	// Call DataForSEO on_page tasks_ready API
	response, err := services.dataForSEOClient.OnPageTasksReady(ctx, dataforseo.RequestSourcePaidKeywordTracking)
	if err != nil {
		logger.Error("[AIO_AUDIT] Failed to check DataForSEO on_page tasks status", "business_id", params.BusinessID, "task_id", params.TaskID, "error", err.Error())
		storeFailedAuditResult(ctx, params.BusinessID, params.Website, params.AuditDate, "Failed to check DataForSEO on_page tasks status", map[string]interface{}{
			"task_id":       params.TaskID,
			"error":         err.Error(),
			"business_name": params.BusinessName,
		})
		return temporal.NewApplicationError("Failed to check DataForSEO on_page tasks status", "failed_tasks_ready", err.Error())
	}

	if len(response.Tasks) < 1 {
		logger.Error("[AIO_AUDIT] No tasks returned from DataForSEO on_page tasks_ready", "business_id", params.BusinessID, "task_id", params.TaskID)
		storeFailedAuditResult(ctx, params.BusinessID, params.Website, params.AuditDate, "No tasks returned from DataForSEO on_page tasks_ready", map[string]interface{}{
			"task_id":       params.TaskID,
			"business_name": params.BusinessName,
		})
		return temporal.NewApplicationError("no tasks returned from DataForSEO on_page tasks_ready", "failed_tasks_ready")
	}

	task := response.Tasks[0]
	if task.StatusCode >= 40000 {
		logger.Error("[AIO_AUDIT] Task failed in tasks_ready", "business_id", params.BusinessID, "task_id", params.TaskID, "status_code", task.StatusCode, "status_message", task.StatusMessage)
		storeFailedAuditResult(ctx, params.BusinessID, params.Website, params.AuditDate, "Task failed in tasks_ready", map[string]interface{}{
			"task_id":        task.ID,
			"status_code":    task.StatusCode,
			"status_message": task.StatusMessage,
			"business_name":  params.BusinessName,
		})
		return temporal.NewApplicationError("Task failed in tasks_ready", "failed_tasks_ready", task, task.StatusMessage)
	}

	// Check if our specific task ID is in the ready results
	taskFound := false
	if task.Result != nil {
		// Parse the result to check for our task ID
		resultBytes, err := json.Marshal(task.Result)
		if err != nil {
			logger.Error("[AIO_AUDIT] Failed to marshal task result", "business_id", params.BusinessID, "task_id", params.TaskID, "error", err.Error())
			storeFailedAuditResult(ctx, params.BusinessID, params.Website, params.AuditDate, "Failed to marshal task result", map[string]interface{}{
				"task_id":       params.TaskID,
				"error":         err.Error(),
				"business_name": params.BusinessName,
			})
			return temporal.NewApplicationError("Failed to parse task results", "failed_parse_results", err.Error())
		}

		var taskResults []*dataforseo.OnPageTaskResult
		err = json.Unmarshal(resultBytes, &taskResults)
		if err != nil {
			logger.Error("[AIO_AUDIT] Failed to unmarshal task results", "business_id", params.BusinessID, "task_id", params.TaskID, "error", err.Error())
			storeFailedAuditResult(ctx, params.BusinessID, params.Website, params.AuditDate, "Failed to unmarshal task results", map[string]interface{}{
				"task_id":       params.TaskID,
				"error":         err.Error(),
				"business_name": params.BusinessName,
			})
			return temporal.NewApplicationError("Failed to parse task results", "failed_parse_results", err.Error())
		}

		for _, result := range taskResults {
			if result.ID == params.TaskID {
				taskFound = true
				break
			}
		}
	}

	if !taskFound {
		// Task is not ready yet, return a retryable error to trigger Temporal retry
		return temporal.NewApplicationError("Task not ready yet", "task_not_ready")
	}

	return nil
}

func getCrawlResultsActivity(ctx context.Context, params *getCrawlResultsParams) (*getCrawlResultsResponse, error) {
	logger := activity.GetLogger(ctx)

	// Get DataForSEO client from activity services
	services := getActivityServices(ctx)

	// Call DataForSEO on_page summary API to get crawl results
	summaryResponse, err := services.dataForSEOClient.OnPageSummary(ctx, params.TaskID, dataforseo.RequestSourcePaidKeywordTracking)
	if err != nil {
		logger.Error("[AIO_AUDIT] Failed to get DataForSEO on_page summary", "business_id", params.BusinessID, "task_id", params.TaskID, "error", err.Error())
		storeFailedAuditResult(ctx, params.BusinessID, params.Website, params.AuditDate, "Failed to get DataForSEO on_page summary", map[string]interface{}{
			"task_id":       params.TaskID,
			"error":         err.Error(),
			"business_name": params.BusinessName,
		})
		return nil, temporal.NewApplicationError("Failed to get DataForSEO on_page summary", "failed_summary", err.Error())
	}

	if len(summaryResponse.Tasks) < 1 {
		logger.Error("[AIO_AUDIT] No tasks returned from DataForSEO on_page summary", "business_id", params.BusinessID, "task_id", params.TaskID)
		storeFailedAuditResult(ctx, params.BusinessID, params.Website, params.AuditDate, "No tasks returned from DataForSEO on_page summary", map[string]interface{}{
			"task_id":       params.TaskID,
			"business_name": params.BusinessName,
		})
		return nil, temporal.NewApplicationError("no tasks returned from DataForSEO on_page summary", "failed_summary")
	}

	summaryTask := summaryResponse.Tasks[0]
	if summaryTask.StatusCode >= 40000 {
		logger.Error("[AIO_AUDIT] Task failed in summary", "business_id", params.BusinessID, "task_id", params.TaskID, "status_code", summaryTask.StatusCode, "status_message", summaryTask.StatusMessage)
		storeFailedAuditResult(ctx, params.BusinessID, params.Website, params.AuditDate, "Task failed in summary", map[string]interface{}{
			"task_id":        summaryTask.ID,
			"status_code":    summaryTask.StatusCode,
			"status_message": summaryTask.StatusMessage,
			"business_name":  params.BusinessName,
		})
		return nil, temporal.NewApplicationError("Task failed in summary", "failed_summary", summaryTask, summaryTask.StatusMessage)
	}

	// Call DataForSEO on_page pages API to get detailed page information
	pagesRequest := &dataforseo.OnPagePagesRequest{
		ID:    params.TaskID,
		Limit: 50,
	}

	pagesResponse, err := services.dataForSEOClient.OnPagePages(ctx, []*dataforseo.OnPagePagesRequest{pagesRequest}, dataforseo.RequestSourcePaidKeywordTracking)
	if err != nil {
		logger.Error("[AIO_AUDIT] OnPagePages API call failed", "business_id", params.BusinessID, "task_id", params.TaskID, "error", err.Error())
		storeFailedAuditResult(ctx, params.BusinessID, params.Website, params.AuditDate, "OnPagePages API call failed", map[string]interface{}{
			"task_id":       params.TaskID,
			"error":         err.Error(),
			"business_name": params.BusinessName,
		})
		return nil, temporal.NewApplicationError("Failed to get DataForSEO on_page pages", "failed_pages", err.Error())
	}

	if len(pagesResponse.Tasks) < 1 {
		logger.Error("[AIO_AUDIT] No tasks returned from OnPagePages API", "business_id", params.BusinessID, "task_id", params.TaskID)
		storeFailedAuditResult(ctx, params.BusinessID, params.Website, params.AuditDate, "No tasks returned from OnPagePages API", map[string]interface{}{
			"task_id":       params.TaskID,
			"business_name": params.BusinessName,
		})
		return nil, temporal.NewApplicationError("no tasks returned from DataForSEO on_page pages", "failed_pages")
	}

	pagesTask := pagesResponse.Tasks[0]
	if pagesTask.StatusCode >= 40000 {
		logger.Error("[AIO_AUDIT] OnPagePages task failed", "business_id", params.BusinessID, "task_id", params.TaskID, "status_code", pagesTask.StatusCode, "status_message", pagesTask.StatusMessage)
		storeFailedAuditResult(ctx, params.BusinessID, params.Website, params.AuditDate, "OnPagePages task failed", map[string]interface{}{
			"task_id":        pagesTask.ID,
			"status_code":    pagesTask.StatusCode,
			"status_message": pagesTask.StatusMessage,
			"business_name":  params.BusinessName,
		})
		return nil, temporal.NewApplicationError("Task failed in pages", "failed_pages", pagesTask, pagesTask.StatusMessage)
	}

	// Extract URLs from the pages response
	var urls []string

	if len(pagesTask.Result) > 0 {
		result := pagesTask.Result[0]

		if len(result.Items) > 0 {
			for _, item := range result.Items {
				if item.URL != "" {
					urls = append(urls, item.URL)
				}
			}
		} else {
			logger.Warn("[AIO_AUDIT] No items found in OnPagePages result", "business_id", params.BusinessID, "task_id", params.TaskID)
		}
	} else {
		logger.Warn("[AIO_AUDIT] No results found in OnPagePages response", "business_id", params.BusinessID, "task_id", params.TaskID)
	}

	return &getCrawlResultsResponse{
		Summary: summaryResponse,
		Pages:   pagesResponse,
		URLs:    urls,
	}, nil
}

// AIO Audit Response Schema
type AIOAuditProgress struct {
	ProcessedURLs int    `json:"processed_urls"`
	CurrentURL    string `json:"current_url"`
	TotalURLs     int    `json:"total_urls"`
}

type AIOAuditResponse struct {
	OverallScore      int                `json:"overall_score"`
	Summary           string             `json:"summary"`
	OverallHighlights []string           `json:"overall_highlights"`
	CategoryScores    map[string]int     `json:"category_scores"`
	Recommendations   []Recommendation   `json:"recommendations"`
	AuditScoreResults []AuditScoreResult `json:"audit_score_results"`
}

type Recommendation struct {
	Category string   `json:"category"`
	Items    []string `json:"items"`
}

type AuditScoreResult struct {
	AuditPageURL string       `json:"audit_page_url"`
	AuditScores  []AuditScore `json:"audit_scores"`
}

type AuditScore struct {
	AuditScoreScopeName       string   `json:"audit_score_scope_name"`
	AuditScoreScopeValue      int      `json:"audit_score_scope_value"`
	AuditScoreScopeHighlights []string `json:"audit_score_scope_highlights"`
	AuditScoreRecommendations []string `json:"audit_score_recommendations"`
}

// JSONSchemaDefinition represents a JSON schema for OpenAI responses
type JSONSchemaDefinition struct {
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Strict      bool                   `json:"strict"`
	Parameters  interface{}            `json:"parameters,omitempty"`
	Schema      map[string]interface{} `json:"schema"`
}

// System prompt for AIO audit analysis
const aioAuditSystemPrompt = `You are an expert SEO and website audit specialist with deep knowledge of technical SEO, performance optimization, and search engine best practices.

Your task is to analyze website data and provide a comprehensive AIO (All-In-One) audit with the following focus areas:

1. **Schema Markup**: Evaluate structured data implementation and JSON-LD usage
2. **Structured Data**: Assess overall structured data quality and compliance
3. **Robots.txt Enablement**: Check robots.txt configuration and crawlability
4. **Metadata**: Analyze title tags, meta descriptions, and keyword optimization
5. **Content Quality**: Evaluate content freshness, authority, and relevance
6. **Performance**: Assess page speed, mobile responsiveness, and Core Web Vitals

Provide scores from 0-100 for each category and an overall score. Include specific, actionable recommendations for improvement.

Focus on:
- Technical SEO best practices
- Search engine optimization opportunities
- Performance improvements
- Content quality enhancements
- Structured data optimization
- Mobile and accessibility considerations

Analyze the provided website data and return a comprehensive audit report.`

// User prompt template for AIO audit
const aioAuditUserPrompt = `Analyze the following website data and provide a comprehensive AIO audit:

**Website Information:**
- Business Name: %s
- Website URL: %s
- Audit Date: %s

**DataForSEO Summary:**
%s

**Raw HTML Analysis:**
%s

**Analysis Instructions:**
1. Evaluate the website across all audit categories
2. Provide specific, actionable recommendations
3. Score each category from 0-100
4. Calculate an overall score
5. Include detailed insights for each page analyzed
6. Focus on technical SEO, performance, and content quality

Please provide a comprehensive audit analysis following the specified JSON schema.`

// storeFailedAuditResult stores a failed audit result in vstore
func storeFailedAuditResult(ctx context.Context, businessID, website, auditDate, failureReason string, additionalData map[string]interface{}) {
	logger := activity.GetLogger(ctx)
	services := getActivityServices(ctx)

	// Extract business name from additional data if available
	brandName := "Unknown"
	if businessName, exists := additionalData["business_name"]; exists {
		if name, ok := businessName.(string); ok && name != "" {
			brandName = name
		}
	}

	// Create failed audit result
	failedAuditResult := aioauditresult.AIOAuditResult{
		BusinessID:        businessID,
		BrandName:         brandName,
		WebsiteURL:        website,
		AuditDate:         auditDate,
		TotalPages:        0,
		AuditStatus:       "failed",
		AuditSummary:      fmt.Sprintf(`{"failure_reason": "%s", "additional_data": %v}`, failureReason, additionalData),
		AuditPages:        []*aioauditresult.AuditPageData{},
		AuditScoreResults: []*aioauditresult.AuditScoreResults{},
	}

	// Store failed audit result if service is available
	if services.aioAuditResultService != nil {
		logger.Info("[AIO_AUDIT] Attempting to store failed audit result in vstore",
			"business_id", businessID,
			"website", website,
			"audit_date", auditDate,
			"failure_reason", failureReason,
			"brand_name", brandName,
			"audit_status", "failed")

		storeErr := services.aioAuditResultService.UpsertAIOAuditResult(ctx, businessID, website, auditDate, failedAuditResult)
		if storeErr != nil {
			logger.Error("[AIO_AUDIT] Failed to store failed audit result in vstore",
				"business_id", businessID,
				"website", website,
				"audit_date", auditDate,
				"failure_reason", failureReason,
				"error", storeErr.Error(),
				"brand_name", brandName,
				"error_type", fmt.Sprintf("%T", storeErr))
		} else {
			logger.Info("[AIO_AUDIT] Successfully stored failed audit result in vstore",
				"business_id", businessID,
				"website", website,
				"audit_date", auditDate,
				"audit_status", "failed",
				"failure_reason", failureReason,
				"brand_name", brandName)
		}
	} else {
		logger.Error("[AIO_AUDIT] aioAuditResultService is nil, cannot store failed audit result",
			"business_id", businessID,
			"website", website,
			"audit_date", auditDate,
			"failure_reason", failureReason,
			"brand_name", brandName)
	}
}

func generateAiInsightsActivity(ctx context.Context, params *generateAiInsightsParams) (*generateAiInsightsResponse, error) {
	logger := activity.GetLogger(ctx)
	services := getActivityServices(ctx)

	// Initialize progress tracking
	var progress AIOAuditProgress
	if activity.HasHeartbeatDetails(ctx) {
		if err := activity.GetHeartbeatDetails(ctx, &progress); err != nil {
			logger.Warn("[AIO_AUDIT] Failed to get heartbeat details, starting fresh", "business_id", params.BusinessID, "error", err.Error())
		}
	} else {
		progress = AIOAuditProgress{
			ProcessedURLs: 0,
			TotalURLs:     len(params.URLs),
		}
	}

	// Check if URLs are provided
	if len(params.URLs) == 0 {
		storeFailedAuditResult(ctx, params.BusinessID, params.Website, params.AuditDate, "No URLs provided for analysis", map[string]interface{}{
			"business_name": params.BusinessName,
			"task_id":       params.TaskID,
		})
		return &generateAiInsightsResponse{
			AuditResponse: createFallbackAuditResponse("No URLs provided for analysis", ""),
		}, nil
	}

	// Convert the DataForSEO OnPageSummaryResponse to a string for storage
	var summaryString string
	if params.Summary != nil && len(params.Summary.Tasks) > 0 && len(params.Summary.Tasks[0].Result) > 0 {
		// Convert the summary response to JSON string
		summaryJSON, err := json.Marshal(params.Summary)
		if err != nil {
			logger.Error("[AIO_AUDIT] Failed to marshal summary response", "business_id", params.BusinessID, "error", err.Error())
			summaryString = fmt.Sprintf("Website summary unavailable")
		} else {
			summaryString = string(summaryJSON)
		}
	} else {
		// Fallback to processing summary if no DataForSEO summary available
		summaryString = fmt.Sprintf("Website summary unavailable")
	}

	// Process each URL individually
	var allAuditScores []*aioauditresult.AuditScores
	var allAuditScoreResults []AuditScoreResult
	var overallSummary string
	var overallHighlights []string
	var overallCategoryScores map[string]int
	var overallRecommendations []Recommendation
	var allAuditPageData []*aioauditresult.AuditPageData

	// Process each URL one by one
	for i, url := range params.URLs {
		// Update progress and send heartbeat
		progress.CurrentURL = url
		progress.ProcessedURLs = i
		activity.RecordHeartbeat(ctx, progress)

		logger := activity.GetLogger(ctx)

		// Call DataForSEO OnPage Raw HTML API for each URL
		request := &dataforseo.OnPageRawHTMLRequest{
			ID:  params.TaskID,
			URL: url,
		}

		response, err := services.dataForSEOClient.OnPageRawHTML(ctx, []*dataforseo.OnPageRawHTMLRequest{request}, dataforseo.RequestSourceCategoryWorkflow)
		if err != nil {
			logger.Error("[AIO_AUDIT] Failed to retrieve HTML", "business_id", params.BusinessID, "url", url, "error", err.Error())
			continue
		}

		if len(response.Tasks) == 0 || len(response.Tasks[0].Result) == 0 || response.Tasks[0].Result[0].Items == nil {
			logger.Error("[AIO_AUDIT] No data returned from OnPageRawHTML", "business_id", params.BusinessID, "url", url)
			continue
		}

		task := response.Tasks[0]
		if task.StatusCode >= 40000 {
			logger.Error("[AIO_AUDIT] OnPageRawHTML task failed", "business_id", params.BusinessID, "url", url, "status_code", task.StatusCode, "status_message", task.StatusMessage)
			continue
		}

		result := task.Result[0]

		// For the first URL (homepage), send the website summary
		// For subsequent URLs, don't send the summary string
		var analysisSummaryString string
		if i == 0 {
			analysisSummaryString = summaryString
		} else {
			analysisSummaryString = "" // Don't send summary for subsequent URLs
		}

		// Generate AIO audit analysis using ChatGPT for each URL
		auditAnalysis, err := generateAIOAuditAnalysis(ctx, getActivityServices(ctx), analysisSummaryString, url, result.Items.HTML, params.Pages)
		if err != nil {
			logger.Error("[AIO_AUDIT] Failed to generate analysis", "business_id", params.BusinessID, "url", url, "error", err.Error())
			continue
		}

		logger.Info("[AIO_AUDIT] DataForSEO OnPageRawHTML API completed", "business_id", params.BusinessID, "url_index", i, "url", url, "task_id", params.TaskID)
		logger.Info("[AIO_AUDIT] ChatGPT API analysis completed", "business_id", params.BusinessID, "url_index", i, "url", url)

		// Send heartbeat after successful analysis
		progress.ProcessedURLs = i + 1
		activity.RecordHeartbeat(ctx, progress)

		// For the first URL, use its data as overall summary
		if i == 0 {
			overallSummary = auditAnalysis.Summary
			overallHighlights = auditAnalysis.OverallHighlights
			overallCategoryScores = auditAnalysis.CategoryScores
			overallRecommendations = auditAnalysis.Recommendations
		}

		// Note: auditAnalysisJSON is no longer used since we're storing OnPagePagesResponse data instead

		// Store the audit result using OnPagePagesResponse data
		// Find the corresponding page item from the OnPagePagesResponse
		var pageItem *dataforseo.OnPagePageItem
		if params.Pages != nil && len(params.Pages.Tasks) > 0 {
			task := params.Pages.Tasks[0]
			if len(task.Result) > 0 {
				result := task.Result[0]
				for _, item := range result.Items {
					if item.URL == url {
						pageItem = item
						break
					}
				}
			}
		}

		// Create AuditPageData with OnPagePagesResponse data
		var auditPageData aioauditresult.AuditPageData

		if pageItem != nil {
			// Use the URL from the page item
			auditPageData.PageURL = pageItem.URL

			// Marshal the entire OnPagePagesResponse as PageData
			pagesResponseJSON, err := json.Marshal(params.Pages)
			if err != nil {
				logger.Error("[AIO_AUDIT] Failed to marshal OnPagePagesResponse", "business_id", params.BusinessID, "url", url, "error", err.Error())
				// Fallback to just the page item if full response fails
				pageItemJSON, _ := json.Marshal(pageItem)
				auditPageData.PageData = string(pageItemJSON)
			} else {
				auditPageData.PageData = string(pagesResponseJSON)
			}
		} else {
			// Fallback if page item not found
			auditPageData.PageURL = url
			auditPageData.PageData = fmt.Sprintf(`{"fallback_url": "%s", "error": "Page item not found in OnPagePagesResponse"}`, url)
		}

		allAuditPageData = append(allAuditPageData, &auditPageData)

		// Add individual page audit scores to allAuditScores
		for _, auditScoreResult := range auditAnalysis.AuditScoreResults {
			for _, auditScore := range auditScoreResult.AuditScores {
				allAuditScores = append(allAuditScores, &aioauditresult.AuditScores{
					AuditScoreScopeName:       auditScore.AuditScoreScopeName,
					AuditScoreScopeValue:      int64(auditScore.AuditScoreScopeValue),
					AuditScoreScopeSummary:    auditScore.AuditScoreScopeHighlights,
					AuditScoreRecommendations: auditScore.AuditScoreRecommendations,
				})
			}
		}

		// Add to allAuditScoreResults for the response
		allAuditScoreResults = append(allAuditScoreResults, auditAnalysis.AuditScoreResults...)
	}

	// If no successful analysis, return fallback response
	if len(allAuditScores) == 0 {
		fallbackURL := ""
		if len(params.URLs) > 0 {
			fallbackURL = params.URLs[0]
		}

		storeFailedAuditResult(ctx, params.BusinessID, params.Website, params.AuditDate, "Failed to analyze any URLs", map[string]interface{}{
			"business_name":                 params.BusinessName,
			"task_id":                       params.TaskID,
			"total_urls":                    len(params.URLs),
			"fallback_url":                  fallbackURL,
			"all_audit_scores_count":        len(allAuditScores),
			"all_audit_score_results_count": len(allAuditScoreResults),
			"all_audit_page_data_count":     len(allAuditPageData),
		})

		return &generateAiInsightsResponse{
			AuditResponse: createFallbackAuditResponse("Failed to analyze any URLs", fallbackURL),
		}, nil
	}

	// Create the final audit analysis response
	auditAnalysis := &AIOAuditResponse{
		OverallScore:      85, // Default score, you might want to calculate this
		Summary:           overallSummary,
		OverallHighlights: overallHighlights,
		CategoryScores:    overallCategoryScores,
		Recommendations:   overallRecommendations,
		AuditScoreResults: allAuditScoreResults,
	}

	// Convert audit score results to the vstore format
	var auditScoreResults []*aioauditresult.AuditScoreResults

	// First, add the overall summary as an audit score result
	overallSummaryJSON, _ := json.Marshal(map[string]interface{}{
		"overall_score":      auditAnalysis.OverallScore,
		"summary":            auditAnalysis.Summary,
		"overall_highlights": auditAnalysis.OverallHighlights,
		"category_scores":    auditAnalysis.CategoryScores,
		"recommendations":    auditAnalysis.Recommendations,
	})

	// Combine all the data into a single AuditScoreScopeSummary
	combinedSummary := []string{
		auditAnalysis.Summary,
	}
	combinedSummary = append(combinedSummary, auditAnalysis.OverallHighlights...)
	combinedSummary = append(combinedSummary, string(overallSummaryJSON))

	overallAuditScores := []*aioauditresult.AuditScores{
		{
			AuditScoreScopeName:       "overall",
			AuditScoreScopeValue:      int64(auditAnalysis.OverallScore),
			AuditScoreScopeSummary:    combinedSummary,
			AuditScoreRecommendations: extractRecommendations(auditAnalysis.Recommendations),
		},
	}

	// Create AuditScoreResults - combine overall summary with first URL, rest as separate entries

	// Group audit scores by URL
	urlAuditScores := make(map[string][]*aioauditresult.AuditScores)

	// Collect all audit scores from the loop
	for i, url := range params.URLs {
		// Get the audit scores for this URL from the collected data
		if i < len(allAuditScoreResults) {
			for _, auditScoreResult := range allAuditScoreResults {
				if auditScoreResult.AuditPageURL == url {
					var urlScores []*aioauditresult.AuditScores
					for _, auditScore := range auditScoreResult.AuditScores {
						urlScores = append(urlScores, &aioauditresult.AuditScores{
							AuditScoreScopeName:       auditScore.AuditScoreScopeName,
							AuditScoreScopeValue:      int64(auditScore.AuditScoreScopeValue),
							AuditScoreScopeSummary:    auditScore.AuditScoreScopeHighlights,
							AuditScoreRecommendations: auditScore.AuditScoreRecommendations,
						})
					}
					urlAuditScores[url] = urlScores
					break
				}
			}
		}
	}

	// 1. Combine overall summary with first URL (if URLs exist)
	if len(params.URLs) > 0 {
		firstURL := params.URLs[0]
		firstURLScores := urlAuditScores[firstURL]

		// Combine overall summary + first URL scores
		combinedScores := append(overallAuditScores, firstURLScores...)

		auditScoreResults = append(auditScoreResults, &aioauditresult.AuditScoreResults{
			AuditPageURL: firstURL, // Use first URL as the page URL
			AuditScores:  combinedScores,
		})
	} else {
		// If no URLs, just add overall summary
		auditScoreResults = append(auditScoreResults, &aioauditresult.AuditScoreResults{
			AuditPageURL: params.Website,
			AuditScores:  overallAuditScores,
		})
	}

	// 2. Add remaining URLs as separate entries (starting from second URL)
	for i := 1; i < len(params.URLs); i++ {
		url := params.URLs[i]
		if scores, exists := urlAuditScores[url]; exists {
			auditScoreResults = append(auditScoreResults, &aioauditresult.AuditScoreResults{
				AuditPageURL: url,
				AuditScores:  scores,
			})
		}
	}

	// Create the AIO audit result
	aioAuditResult := aioauditresult.AIOAuditResult{
		BusinessID:        params.BusinessID,
		BrandName:         params.BusinessName,
		WebsiteURL:        params.Website,
		AuditDate:         params.AuditDate,
		TotalPages:        int64(len(params.URLs)), // Set total pages to number of URLs processed
		AuditStatus:       "completed",
		AuditSummary:      getSummaryJSON(params.Summary), // Store OnPageSummaryResponse as JSON
		AuditPages:        allAuditPageData,
		AuditScoreResults: auditScoreResults,
	}

	// Send final heartbeat before storing result
	progress.ProcessedURLs = len(params.URLs)
	progress.CurrentURL = "storing_result"
	activity.RecordHeartbeat(ctx, progress)

	// Store the audit result if service is available
	logger.Info("[AIO_AUDIT] Preparing to store audit result in vstore",
		"business_id", params.BusinessID,
		"website", params.Website,
		"audit_date", params.AuditDate,
		"total_pages", aioAuditResult.TotalPages,
		"audit_score_results_count", len(aioAuditResult.AuditScoreResults),
		"audit_pages_count", len(aioAuditResult.AuditPages),
		"service_available", services.aioAuditResultService != nil,
		"service_type", fmt.Sprintf("%T", services.aioAuditResultService))

	if services.aioAuditResultService != nil {
		logger.Info("[AIO_AUDIT] Attempting vstore insertion",
			"business_id", params.BusinessID,
			"website", params.Website,
			"audit_date", params.AuditDate,
			"audit_status", aioAuditResult.AuditStatus,
			"brand_name", aioAuditResult.BrandName,
			"website_url", aioAuditResult.WebsiteURL)

		storeErr := services.aioAuditResultService.UpsertAIOAuditResult(ctx, params.BusinessID, params.Website, params.AuditDate, aioAuditResult)
		if storeErr != nil {
			logger.Error("[AIO_AUDIT] Failed to store audit result in vstore",
				"business_id", params.BusinessID,
				"website", params.Website,
				"audit_date", params.AuditDate,
				"error", storeErr.Error(),
				"error_type", fmt.Sprintf("%T", storeErr),
				"total_pages", aioAuditResult.TotalPages,
				"audit_score_results_count", len(aioAuditResult.AuditScoreResults),
				"audit_pages_count", len(aioAuditResult.AuditPages),
				"audit_status", aioAuditResult.AuditStatus,
				"brand_name", aioAuditResult.BrandName)

			// Store failed audit result using common method
			storeFailedAuditResult(ctx, params.BusinessID, params.Website, params.AuditDate, "Failed to store audit result in vstore", map[string]interface{}{
				"original_error":            storeErr.Error(),
				"total_pages":               aioAuditResult.TotalPages,
				"audit_score_results_count": len(aioAuditResult.AuditScoreResults),
				"audit_pages_count":         len(aioAuditResult.AuditPages),
				"brand_name":                aioAuditResult.BrandName,
			})

			return nil, temporal.NewApplicationError("Failed to store audit result", "failed_store_audit_result", storeErr)
		}

		logger.Info("[AIO_AUDIT] Successfully stored audit result in vstore",
			"business_id", params.BusinessID,
			"website", params.Website,
			"audit_date", params.AuditDate,
			"total_pages", aioAuditResult.TotalPages,
			"audit_score_results_count", len(aioAuditResult.AuditScoreResults),
			"audit_pages_count", len(aioAuditResult.AuditPages))
	} else {
		logger.Error("[AIO_AUDIT] aioAuditResultService is nil, vstore insertion failed",
			"business_id", params.BusinessID,
			"website", params.Website,
			"audit_date", params.AuditDate,
			"service_type", fmt.Sprintf("%T", services.aioAuditResultService),
			"services_struct_type", fmt.Sprintf("%T", services),
			"total_pages", aioAuditResult.TotalPages,
			"audit_score_results_count", len(aioAuditResult.AuditScoreResults),
			"audit_pages_count", len(aioAuditResult.AuditPages),
			"audit_status", aioAuditResult.AuditStatus,
			"brand_name", aioAuditResult.BrandName)

		// Note: We can't store failed audit result when service is nil, but we log the error
	}

	return &generateAiInsightsResponse{
		AuditResponse: auditAnalysis, // Returning the ChatGPT-generated response
	}, nil
}

// extractRecommendations converts recommendations to a flat list of strings
func extractRecommendations(recommendations []Recommendation) []string {
	var result []string
	for _, rec := range recommendations {
		result = append(result, rec.Items...)
	}
	return result
}

// createFallbackAuditResponse creates a fallback audit response with default values
func createFallbackAuditResponse(message string, pageURL string) *AIOAuditResponse {
	return &AIOAuditResponse{
		OverallScore:      50,
		Summary:           message,
		OverallHighlights: []string{message},
		CategoryScores: map[string]int{
			"Schema Markup":         50,
			"Structured Data":       50,
			"Robots.txt Enablement": 50,
			"Metadata":              50,
			"Content Quality":       50,
			"Performance":           50,
		},
		Recommendations: []Recommendation{
			{
				Category: "General",
				Items:    []string{message},
			},
		},
		AuditScoreResults: []AuditScoreResult{
			{
				AuditPageURL: pageURL,
				AuditScores: []AuditScore{
					{
						AuditScoreScopeName:       "General",
						AuditScoreScopeValue:      50,
						AuditScoreScopeHighlights: []string{message},
						AuditScoreRecommendations: []string{message},
					},
				},
			},
		},
	}
}

// getSummaryJSON converts OnPageSummaryResponse to JSON string
func getSummaryJSON(summary *dataforseo.OnPageSummaryResponse) string {
	if summary == nil {
		return "{}"
	}

	summaryJSON, err := json.Marshal(summary)
	if err != nil {
		return fmt.Sprintf(`{"error": "Failed to marshal summary response: %v"}`, err)
	}

	return string(summaryJSON)
}

// createAIOAuditJSONSchema creates the JSON schema definition for the AIO audit response
func createAIOAuditJSONSchema() openai.JSONSchemaDefinition {
	return openai.JSONSchemaDefinition{
		Name:        "aio_audit_response",
		Description: "AI-powered audit response for website analysis",
		Strict:      true,
		SchemaParameters: &jsonschema.Schema{
			Type:                 jsonschema.DataTypeObject,
			Description:          "Complete AIO audit response with scores, recommendations, and detailed analysis",
			AdditionalProperties: false,
			Properties: map[string]jsonschema.Schema{
				"overall_score": {
					Type:        jsonschema.DataTypeInteger,
					Description: "Overall audit score from 0-100",
				},
				"summary": {
					Type:        jsonschema.DataTypeString,
					Description: "Comprehensive summary of the audit findings",
				},
				"overall_highlights": {
					Type:        jsonschema.DataTypeSlice,
					Description: "Key highlights from the audit",
					Items: &jsonschema.Schema{
						Type: jsonschema.DataTypeString,
					},
				},
				"category_scores": {
					Type:                 jsonschema.DataTypeObject,
					Description:          "Scores for different audit categories",
					AdditionalProperties: false,
					Properties: map[string]jsonschema.Schema{
						"Schema Markup": {
							Type: jsonschema.DataTypeInteger,
						},
						"Structured Data": {
							Type: jsonschema.DataTypeInteger,
						},
						"Robots.txt Enablement": {
							Type: jsonschema.DataTypeInteger,
						},
						"Metadata": {
							Type: jsonschema.DataTypeInteger,
						},
						"Content Quality": {
							Type: jsonschema.DataTypeInteger,
						},
						"Performance": {
							Type: jsonschema.DataTypeInteger,
						},
					},
					Required: []string{"Schema Markup", "Structured Data", "Robots.txt Enablement", "Metadata", "Content Quality", "Performance"},
				},
				"recommendations": {
					Type:        jsonschema.DataTypeSlice,
					Description: "Detailed recommendations by category",
					Items: &jsonschema.Schema{
						Type:                 jsonschema.DataTypeObject,
						AdditionalProperties: false,
						Properties: map[string]jsonschema.Schema{
							"category": {
								Type: jsonschema.DataTypeString,
							},
							"items": {
								Type: jsonschema.DataTypeSlice,
								Items: &jsonschema.Schema{
									Type: jsonschema.DataTypeString,
								},
							},
						},
						Required: []string{"category", "items"},
					},
				},
				"audit_score_results": {
					Type:        jsonschema.DataTypeSlice,
					Description: "Detailed audit results for each page",
					Items: &jsonschema.Schema{
						Type:                 jsonschema.DataTypeObject,
						AdditionalProperties: false,
						Properties: map[string]jsonschema.Schema{
							"audit_page_url": {
								Type: jsonschema.DataTypeString,
							},
							"audit_scores": {
								Type: jsonschema.DataTypeSlice,
								Items: &jsonschema.Schema{
									Type:                 jsonschema.DataTypeObject,
									AdditionalProperties: false,
									Properties: map[string]jsonschema.Schema{
										"audit_score_scope_name": {
											Type: jsonschema.DataTypeString,
										},
										"audit_score_scope_value": {
											Type: jsonschema.DataTypeInteger,
										},
										"audit_score_scope_highlights": {
											Type: jsonschema.DataTypeSlice,
											Items: &jsonschema.Schema{
												Type: jsonschema.DataTypeString,
											},
										},
										"audit_score_recommendations": {
											Type: jsonschema.DataTypeSlice,
											Items: &jsonschema.Schema{
												Type: jsonschema.DataTypeString,
											},
										},
									},
									Required: []string{"audit_score_scope_name", "audit_score_scope_value", "audit_score_scope_highlights", "audit_score_recommendations"},
								},
							},
						},
						Required: []string{"audit_page_url", "audit_scores"},
					},
				},
			},
			Required: []string{"overall_score", "summary", "overall_highlights", "category_scores", "recommendations", "audit_score_results"},
		},
	}
}

// generateAIOAuditAnalysis generates AI insights using ChatGPT with JSON schema
func generateAIOAuditAnalysis(ctx context.Context, services *activityServices, summaryString, url, htmlContent string, pageData *dataforseo.OnPagePagesResponse) (*AIOAuditResponse, error) {
	logger := activity.GetLogger(ctx)

	// Create the schema definition
	schemaDefinition := createAIOAuditJSONSchema()

	// Extract the first page data
	var firstPageData interface{}
	if pageData != nil && len(pageData.Tasks) > 0 && len(pageData.Tasks[0].Result) > 0 && len(pageData.Tasks[0].Result[0].Items) > 0 {
		firstPageData = pageData.Tasks[0].Result[0].Items[0]
	} else {
		logger.Warn("[AIO_AUDIT] No page data available for extraction")
	}

	// Prepare the input data for the first page
	firstPageDataJSON, err := json.Marshal(firstPageData)
	if err != nil {
		logger.Error("[AIO_AUDIT] Failed to marshal first page data", "url", url, "error", err)
		return nil, fmt.Errorf("failed to marshal first page data: %w", err)
	}

	// Create the structured input data
	type PageData struct {
		PageURL  string `json:"page_url"`
		PageData string `json:"page_data"`
		HTML     struct {
			RawHTML string `json:"raw_html"`
		} `json:"html"`
	}

	type ChatGPTInput struct {
		WebsiteURL     string     `json:"website_url"`
		WebsiteSummary string     `json:"website_summary"`
		Pages          []PageData `json:"pages"`
	}

	// Extract the URL from the first page data
	pageURL := url // fallback to passed URL
	if firstPageData != nil {
		if pageItem, ok := firstPageData.(*dataforseo.OnPagePageItem); ok {
			pageURL = pageItem.URL
		}
	}

	// Create the structured input with only the first page
	input := ChatGPTInput{
		WebsiteURL:     url,
		WebsiteSummary: summaryString,
		Pages: []PageData{
			{
				PageURL:  pageURL,
				PageData: string(firstPageDataJSON),
				HTML: struct {
					RawHTML string `json:"raw_html"`
				}{
					RawHTML: htmlContent,
				},
			},
		},
	}

	// Marshal the structured input to JSON
	inputJSON, err := json.MarshalIndent(input, "", "    ")
	if err != nil {
		logger.Error("[AIO_AUDIT] Failed to marshal input data", "url", url, "error", err)
		return nil, fmt.Errorf("failed to marshal input data: %w", err)
	}

	// Use the structured JSON directly as the user input
	userPrompt := string(inputJSON)

	// Create the messages
	messages := []openai.ChatCompletionMessageRequest{
		{
			Role: "system",
			Content: `You are an expert in SEO, semantic web, and AI search engine optimisation (GEO – Generative Engine Optimisation). Your task is to audit a webpage's HTML and evaluate how well it is optimised for visibility in generative AI engines like ChatGPT, Perplexity, Google Search AI Overviews, and others.

You will assess the page across six key categories. For each category, follow the listed checklist to perform detailed evaluation. Then, provide:

A score (0–100) for each category
A short summary of your findings for that category
Actionable recommendations for improvement (if needed)

Finally, compute an overall GEO optimisation score out of 100 based on the category-level results.

Return your output in structured JSON format.

### Categories and Checks:

1. **Schema Markup**
   - Is valid JSON-LD present?
   - Does it follow schema.org guidelines?
   - Are required entities like WebPage, Organisation, Article used?

2. **Structured Data**
   - Are structured data types implemented correctly?
   - Are there any schema validation errors?
   - Are they properly nested and present on key pages?

3. **Robots.txt Enablement**
   - Is the robots.txt file accessible?
   - Does it allow AI bots to crawl key sections?
   - Are disallowed or no index directives appropriate?

4. **Metadata**
   - Is the <title> tag optimised for each page?
   - Are meta description and keywords present?
   - Are OpenGraph tags included?
   - Is heading structure (H1–H6) properly used?
   - Are semantic tags like <article>, <section>, <nav> used?

5. **Content Quality**
   - Is there a visible publish or updated date?
   - Do <meta> tags indicate last modified or published?
   - Are citations, quotes, or statistics used?
   - Does the content reflect E-E-A-T principles (Experience, Expertise, Authoritativeness, Trust)?

6. **Performance**
   - Is the page speed acceptable (based on Core Web Vitals)?
   - Is the website mobile-optimised and responsive?`,
		},
		{
			Role:    "user",
			Content: userPrompt,
		},
	}

	// Call ChatGPT with JSON schema
	response, err := services.openAIClient.CreateChatCompletion(
		ctx,
		messages,
		openai.WithModel(openai.GPT4Dot1),
		openai.WithTemperature(0.7),
		openai.WithMaxTokens(10000),
		openai.WithMaxRetries(3),
		openai.WithTokenTrimming(openai.TrimFirstNonSystemMessage),
		openai.WithResponseFormat("json_schema"),
		openai.WithResponseFormatJSONSchema(schemaDefinition),
	)
	if err != nil {
		logger.Error("[AIO_AUDIT] ChatGPT API call failed", "url", url, "error", err)
		// Fallback to a basic response if ChatGPT fails
		logger.Warn("[AIO_AUDIT] Using fallback response due to ChatGPT failure", "url", url)
		return createFallbackAuditResponse("Analysis could not be completed due to technical issues. Please try again later.", url), nil
	}

	// Parse the response
	var auditResponse AIOAuditResponse
	if err := json.Unmarshal([]byte(response.Content), &auditResponse); err != nil {
		logger.Error("[AIO_AUDIT] Failed to unmarshal ChatGPT response", "url", url, "error", err, "response_content", response.Content)
		return nil, fmt.Errorf("failed to unmarshal ChatGPT response: %w", err)
	}

	return &auditResponse, nil
}
