package seoworkflow

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/vendasta/listing-products/internal/seo/workflow/seofailedworkflowinfo"
	seofailedworkflowinfoservice "github.com/vendasta/listing-products/internal/seo/workflow/seofailedworkflowinfo/service"

	adver "github.com/vendasta/advertising/sdks/go/v2"
	advertising "github.com/vendasta/generated-protos-go/advertising/v1"
	seogooglekeywordinfoservice "github.com/vendasta/listing-products/internal/seo/seogooglekeywordinfo/service"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"github.com/vendasta/listing-products/internal/dataforseo"
	listingprofilemodel "github.com/vendasta/listing-products/internal/listingprofile/model"
	listingprofileservice "github.com/vendasta/listing-products/internal/listingprofile/service"
	keywordinfoservice "github.com/vendasta/listing-products/internal/seo/keywordinfo/service"
	seodataservice "github.com/vendasta/listing-products/internal/seo/service"
	seosuggestedkeywordsservice "github.com/vendasta/listing-products/internal/seosuggestedkeywords/service"
	"go.temporal.io/sdk/testsuite"
	"go.temporal.io/sdk/worker"
)

type ExpectedUpsertKeywordInfo struct {
	BusinessID       string
	Keyword          string
	SearchVolume     int64
	CompetitionIndex int64
}

func Test_KeywordInfoWorkflow(t *testing.T) {
	type testCase struct {
		name                           string
		workflowParams                 *KeywordInfoWorkflowParams
		listingProfiles                []*listingprofilemodel.ListingProfile
		startDate                      time.Time
		endDate                        time.Time
		expectedUpsertKeywordInfoCalls []*ExpectedUpsertKeywordInfo
		expectedUpdateSeoFailed        []*seofailedworkflowinfo.SEOFailedWorkflowInfo
		expectedAdvertisingResp        *advertising.KeywordMetricsResponse
		expectedResponse               interface{}
		expectedWorkflowErr            error
		expectedSEOFailedWorkflowInfos []*seofailedworkflowinfo.SEOFailedWorkflowInfo
	}
	cases := []*testCase{
		{
			name:      "Test retries when results are not ready yet",
			startDate: time.Date(2019, 12, 1, 0, 0, 0, 0, time.UTC),
			endDate:   time.Date(2019, 12, 1, 0, 0, 0, 0, time.UTC),
			workflowParams: &KeywordInfoWorkflowParams{
				LocationID:             1,
				DataForSEOLocationName: "SEO Location Name",
				City:                   "City",
				State:                  "State",
				Country:                "Country",
				LocationCountryCode:    "Location Country Code",
				Businesses: []*KeywordSearchParams{
					{
						BusinessID: "AG-1",
						Keywords: []string{
							"test",
							"test1",
						},
					},
					{
						BusinessID: "AG-2",
						Keywords: []string{
							"test",
							"test1",
						},
					},
				},
				Date: "2020-02-10",
				SeoFailedWFInfo: []*seofailedworkflowinfo.SEOFailedWorkflowInfo{
					{
						BusinessID:        "AG-1",
						WorkflowId:        "default-test-workflow-id",
						WorkflowType:      "KeywordInfoWorkflow",
						StartDate:         "2020-02-10",
						Keywords:          []string{"Keyword1", "Keyword2"},
						ErrorMessage:      "error getting keyword metrics from google api: (type: unexpected_error, retryable: true)",
						ErrorType:         "unexpected_error",
						StatusCode:        "",
						RetryCount:        1,
						IsRetryableError:  true,
						RetriedWorkflowId: "SERP-AG1-NEW",
						RetriedDate:       "",
						IssueResolved:     true,
					},
					{
						BusinessID:        "AG-2",
						WorkflowId:        "default-test-workflow-id",
						WorkflowType:      "KeywordInfoWorkflow",
						StartDate:         "2020-02-10",
						Keywords:          []string{"Keyword3", "Keyword4"},
						ErrorMessage:      "error getting keyword metrics from google api: (type: unexpected_error, retryable: true)",
						ErrorType:         "unexpected_error",
						StatusCode:        "",
						RetryCount:        1,
						IsRetryableError:  true,
						RetriedWorkflowId: "SERP-AG2-NEW",
						RetriedDate:       "2020-02-10",
						IssueResolved:     true,
					},
				},
			},
			listingProfiles: []*listingprofilemodel.ListingProfile{
				{
					BusinessID: "AG-1",
					Website:    "www.website.com",
					RichData: &listingprofilemodel.RichData{
						SEOKeywords: []string{"Keyword1", "Keyword2"},
					},
				},
				{
					BusinessID: "AG-2",
					Website:    "www.website.com",
					RichData: &listingprofilemodel.RichData{
						SEOKeywords: []string{"Keyword3", "Keyword4"},
					},
				},
			},
			expectedUpsertKeywordInfoCalls: []*ExpectedUpsertKeywordInfo{
				{
					BusinessID:       "AG-1",
					Keyword:          "Keyword1",
					CompetitionIndex: 1,
					SearchVolume:     100,
				},
				{
					BusinessID:       "AG-1",
					Keyword:          "Keyword2",
					CompetitionIndex: 2,
					SearchVolume:     200,
				},
				{
					BusinessID:       "AG-2",
					Keyword:          "Keyword3",
					CompetitionIndex: 3,
					SearchVolume:     300,
				},
				{
					BusinessID:       "AG-2",
					Keyword:          "Keyword4",
					CompetitionIndex: 4,
					SearchVolume:     400,
				},
			},
			expectedAdvertisingResp: &advertising.KeywordMetricsResponse{

				Results: []*advertising.SearchResult{
					{
						SearchQuery: "test",
						HistoricalMetrics: &advertising.HistoricalMetrics{
							AvgMonthlySearches:          "1300",
							CompetitionLevel:            "LOW",
							CompetitionIndex:            "31",
							TopOfPageBidLowRangeMicros:  "303790",
							TopOfPageBidHighRangeMicros: "1300000",
							MonthlySearchVolumes: []*advertising.MonthlySearchVolume{
								{
									Month:           "JANUARY",
									Year:            "2025",
									MonthlySearches: "1000",
								},
							},
						},
					},
				},
			},
			expectedUpdateSeoFailed: []*seofailedworkflowinfo.SEOFailedWorkflowInfo{
				{
					BusinessID:        "AG-1",
					WorkflowId:        "SERP-AG1",
					WorkflowType:      "KeywordInfoWorkflow",
					StartDate:         "2019-12-01",
					Keywords:          []string{"Keyword1", "Keyword2"},
					ErrorMessage:      "error getting keyword metrics from google api: (type: unexpected_error, retryable: true)",
					ErrorType:         "unexpected_error",
					StatusCode:        "",
					RetryCount:        1,
					IsRetryableError:  false,
					RetriedWorkflowId: "SERP-AG1-NEW",
					RetriedDate:       "",
					IssueResolved:     false,
				},
				{
					BusinessID:        "AG-2",
					WorkflowId:        "SERP-AG2",
					WorkflowType:      "KeywordInfoWorkflow",
					StartDate:         "2019-12-01",
					Keywords:          []string{"Keyword3", "Keyword4"},
					ErrorMessage:      "error getting keyword metrics from google api: (type: unexpected_error, retryable: true)",
					ErrorType:         "unexpected_error",
					StatusCode:        "",
					RetryCount:        0,
					IsRetryableError:  false,
					RetriedWorkflowId: "SERP-AG2-NEW",
					RetriedDate:       "",
					IssueResolved:     false,
				},
			},
			expectedSEOFailedWorkflowInfos: []*seofailedworkflowinfo.SEOFailedWorkflowInfo{
				{
					BusinessID:        "AG-1",
					WorkflowId:        "SERP-AG1",
					WorkflowType:      "KeywordInfoWorkflow",
					StartDate:         "2019-12-01",
					Keywords:          []string{"Keyword1", "Keyword2"},
					ErrorMessage:      "error getting keyword metrics from google api: (type: unexpected_error, retryable: true)",
					ErrorType:         "unexpected_error",
					StatusCode:        "",
					RetryCount:        0,
					IsRetryableError:  false,
					RetriedWorkflowId: "",
					RetriedDate:       "",
					IssueResolved:     false,
				},
				{
					BusinessID:        "AG-2",
					WorkflowId:        "SERP-AG2",
					WorkflowType:      "KeywordInfoWorkflow",
					StartDate:         "2019-12-01",
					Keywords:          []string{"Keyword3", "Keyword4"},
					ErrorMessage:      "error getting keyword metrics from google api: (type: unexpected_error, retryable: true)",
					ErrorType:         "unexpected_error",
					StatusCode:        "",
					RetryCount:        0,
					IsRetryableError:  false,
					RetriedWorkflowId: "",
					RetriedDate:       "",
					IssueResolved:     false,
				},
			},
			expectedResponse:    nil,
			expectedWorkflowErr: nil,
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			ctx := context.Background()
			ctrl := gomock.NewController(t)
			mockLpService := listingprofileservice.NewMockInterface(ctrl)
			mockSeoFailedWorkflowInfoService := seofailedworkflowinfoservice.NewMockService(ctrl)

			for _, lp := range c.listingProfiles {
				mockLpService.EXPECT().Get(gomock.Any(), lp.BusinessID, false).Return(lp, nil).AnyTimes()
			}

			mockDataForSEO := dataforseo.NewMockSERPClient(ctrl)

			mockSuggestedKeywords := seosuggestedkeywordsservice.NewMockService(ctrl)

			mockSEODataService := seodataservice.NewMockService(ctrl)
			mockAdwordsClient := adver.NewMockAdwordsClientInterface(ctrl)
			mockAdwordsClient.EXPECT().GetKeywordHistoricalMetricsAPI(gomock.Any(), gomock.Any(), gomock.Any()).Return(c.expectedAdvertisingResp, nil).Times(2)
			mockKeywordInfoService := keywordinfoservice.NewMockService(ctrl)
			mockKeywordInfoService.EXPECT().Upsert(gomock.Any(), gomock.Any()).Return(nil).Times(2)
			mockSEOGoogleKeywordInfoService := seogooglekeywordinfoservice.NewMockService(ctrl)
			for _, lv := range c.expectedSEOFailedWorkflowInfos {
				mockSeoFailedWorkflowInfoService.EXPECT().Upsert(gomock.Any(), lv).Return(nil).AnyTimes()
			}
			for _, v := range c.workflowParams.SeoFailedWFInfo {
				mockSeoFailedWorkflowInfoService.EXPECT().UpdateFailedWorkflowinfo(gomock.Any(), v).Return(nil).AnyTimes()
			}

			testSuite := &testsuite.WorkflowTestSuite{}
			env := testSuite.NewTestWorkflowEnvironment()
			env.RegisterWorkflow(KeywordInfoWorkflow)
			env.RegisterActivity(getGoogleDataLastUpdateDate)
			env.RegisterActivity(buildKeywordInfoRequest)
			env.RegisterActivity(postKeywordInfoRequest)
			env.RegisterActivity(getAndStoreKeywordInfo)
			env.RegisterActivity(getAndStoreKeywordInfoUsingGoogleAPI)
			env.RegisterActivity(getAndStoreKeywordInfoUsingGoogleAPIV2)
			env.RegisterActivity(storeFailedWorkflowInfo)
			ctx = NewActivityContext(ctx, nil, mockLpService, nil, mockSEODataService, mockDataForSEO, nil, nil, nil, nil, nil, mockSuggestedKeywords, nil, mockKeywordInfoService, nil, nil, nil, mockAdwordsClient, mockSEOGoogleKeywordInfoService, mockSeoFailedWorkflowInfoService, nil, nil)

			env.SetWorkerOptions(worker.Options{
				BackgroundActivityContext: ctx,
			})

			env.ExecuteWorkflow(KeywordInfoWorkflow, c.workflowParams)

			if !env.IsWorkflowCompleted() {
				t.Errorf("Workflow failed to complete. Error (%s)", env.GetWorkflowError())
			}
			assert.Equal(t, c.expectedWorkflowErr, env.GetWorkflowError())
		})
	}
}

func Test_KeywordInfoWorkflowError(t *testing.T) {
	type testCase struct {
		name                           string
		workflowParams                 *KeywordInfoWorkflowParams
		listingProfiles                []*listingprofilemodel.ListingProfile
		startDate                      time.Time
		endDate                        time.Time
		expectedUpsertKeywordInfoCalls []*ExpectedUpsertKeywordInfo
		expectedAdvertisingResp        *advertising.KeywordMetricsResponse
		expectedResponse               interface{}
		expectedWorkflowErr            error
		expectedUpdateSeoFailed        []*seofailedworkflowinfo.SEOFailedWorkflowInfo
		expectedSEOFailedWorkflowInfos []*seofailedworkflowinfo.SEOFailedWorkflowInfo
	}
	cases := []*testCase{
		{
			name:      "Test fails when GoogleKeywordVolumeTaskPost returns error",
			startDate: time.Date(2019, 12, 1, 0, 0, 0, 0, time.UTC),
			endDate:   time.Date(2019, 12, 1, 0, 0, 0, 0, time.UTC),
			workflowParams: &KeywordInfoWorkflowParams{
				LocationID:             1,
				DataForSEOLocationName: "SEO Location Name",
				LocationCountryCode:    "Location Country Code",
				City:                   "",
				Businesses: []*KeywordSearchParams{
					{
						BusinessID: "AG-1",
						Keywords:   []string{"Keyword1", "Keyword2"},
					},
					{
						BusinessID: "AG-2",
						Keywords:   []string{"Keyword3", "Keyword4"},
					},
				},
				InitialSleepDuration: 0,
				Date:                 "2020-02-10",
			},
			listingProfiles: []*listingprofilemodel.ListingProfile{
				{
					BusinessID: "AG-1",
					Website:    "www.website.com",
					RichData: &listingprofilemodel.RichData{
						SEOKeywords: []string{"Keyword1", "Keyword2"},
					},
				},
				{
					BusinessID: "AG-2",
					Website:    "www.website.com",
					RichData: &listingprofilemodel.RichData{
						SEOKeywords: []string{"Keyword3", "Keyword4"},
					},
				},
			},
			expectedAdvertisingResp: &advertising.KeywordMetricsResponse{

				Results: []*advertising.SearchResult{
					{
						SearchQuery: "test",
						HistoricalMetrics: &advertising.HistoricalMetrics{
							AvgMonthlySearches:          "1300",
							CompetitionLevel:            "LOW",
							CompetitionIndex:            "31",
							TopOfPageBidLowRangeMicros:  "303790",
							TopOfPageBidHighRangeMicros: "1300000",
							MonthlySearchVolumes: []*advertising.MonthlySearchVolume{
								{
									Month:           "JANUARY",
									Year:            "2025",
									MonthlySearches: "1000",
								},
							},
						},
					},
				},
			},
			expectedUpdateSeoFailed: []*seofailedworkflowinfo.SEOFailedWorkflowInfo{
				{
					BusinessID:        "AG-1",
					WorkflowId:        "default-test-workflow-id",
					WorkflowType:      "KeywordInfoWorkflow",
					StartDate:         "2020-02-10",
					Keywords:          []string{"Keyword1", "Keyword2"},
					ErrorMessage:      "error getting keyword metrics from google api: (type: unexpected_error, retryable: true)",
					ErrorType:         "unexpected_error",
					StatusCode:        "",
					RetryCount:        1,
					IsRetryableError:  false,
					RetriedWorkflowId: "SERP-AG1-NEW",
					RetriedDate:       "",
					IssueResolved:     false,
				},
				{
					BusinessID:        "AG-2",
					WorkflowId:        "default-test-workflow-id",
					WorkflowType:      "KeywordInfoWorkflow",
					StartDate:         "2020-02-10",
					Keywords:          []string{"Keyword3", "Keyword4"},
					ErrorMessage:      "error getting keyword metrics from google api: (type: unexpected_error, retryable: true)",
					ErrorType:         "unexpected_error",
					StatusCode:        "",
					RetryCount:        1,
					IsRetryableError:  false,
					RetriedWorkflowId: "SERP-AG2-NEW",
					RetriedDate:       "",
					IssueResolved:     false,
				},
			},
			expectedSEOFailedWorkflowInfos: []*seofailedworkflowinfo.SEOFailedWorkflowInfo{
				{
					BusinessID:        "AG-1",
					WorkflowId:        "default-test-workflow-id",
					WorkflowType:      "KeywordInfoWorkflow",
					StartDate:         "2020-02-10",
					Keywords:          []string{"Keyword1", "Keyword2"},
					ErrorMessage:      "error getting keyword metrics from google api: (type: unexpected_error, retryable: true)",
					ErrorType:         "unexpected_error",
					StatusCode:        "",
					RetryCount:        0,
					IsRetryableError:  true,
					RetriedWorkflowId: "",
					RetriedDate:       "",
					IssueResolved:     false,
				},
				{
					BusinessID:        "AG-2",
					WorkflowId:        "default-test-workflow-id",
					WorkflowType:      "KeywordInfoWorkflow",
					StartDate:         "2020-02-10",
					Keywords:          []string{"Keyword3", "Keyword4"},
					ErrorMessage:      "error getting keyword metrics from google api: (type: unexpected_error, retryable: true)",
					ErrorType:         "unexpected_error",
					StatusCode:        "",
					RetryCount:        0,
					IsRetryableError:  true,
					RetriedWorkflowId: "",
					RetriedDate:       "",
					IssueResolved:     false,
				},
			},
			expectedWorkflowErr: errors.New("activity error (type: getAndStoreKeywordInfoUsingGoogleAPIV2, scheduledEventID: 0, startedEventID: 0, identity: ): error getting keyword metrics from google api: (type: unexpected_error, retryable: true)"),
		}}
	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			ctx := context.Background()
			ctrl := gomock.NewController(t)
			mockLpService := listingprofileservice.NewMockInterface(ctrl)
			mockSeoFailedWorkflowInfoService := seofailedworkflowinfoservice.NewMockService(ctrl)

			for _, lp := range c.listingProfiles {
				mockLpService.EXPECT().Get(gomock.Any(), lp.BusinessID, false).Return(lp, nil).AnyTimes()
			}

			mockDataForSEO := dataforseo.NewMockSERPClient(ctrl)
			mockSuggestedKeywords := seosuggestedkeywordsservice.NewMockService(ctrl)

			mockSEODataService := seodataservice.NewMockService(ctrl)
			mockAdwordsClient := adver.NewMockAdwordsClientInterface(ctrl)
			mockAdwordsClient.EXPECT().GetKeywordHistoricalMetricsAPI(gomock.Any(), gomock.Any(), gomock.Any()).Return(c.expectedAdvertisingResp, errors.New("Failed to get response")).AnyTimes()
			mockKeywordInfoService := keywordinfoservice.NewMockService(ctrl)
			mockKeywordInfoService.EXPECT().Upsert(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
			mockSEOGoogleKeywordInfoService := seogooglekeywordinfoservice.NewMockService(ctrl)
			for _, lv := range c.expectedSEOFailedWorkflowInfos {
				mockSeoFailedWorkflowInfoService.EXPECT().Upsert(gomock.Any(), lv).Return(nil).Times(len(c.expectedSEOFailedWorkflowInfos))
			}
			for _, v := range c.expectedUpdateSeoFailed {
				if v.ErrorType != "" {
					mockSeoFailedWorkflowInfoService.EXPECT().UpdateFailedWorkflowinfo(gomock.Any(), v).Return(nil).AnyTimes()
				}
			}
			testSuite := &testsuite.WorkflowTestSuite{}
			env := testSuite.NewTestWorkflowEnvironment()
			env.RegisterWorkflow(KeywordInfoWorkflow)
			env.RegisterActivity(getGoogleDataLastUpdateDate)
			env.RegisterActivity(buildKeywordInfoRequest)
			env.RegisterActivity(postKeywordInfoRequest)
			env.RegisterActivity(getAndStoreKeywordInfo)
			env.RegisterActivity(getAndStoreKeywordInfoUsingGoogleAPI)
			env.RegisterActivity(getAndStoreKeywordInfoUsingGoogleAPIV2)
			env.RegisterActivity(storeFailedWorkflowInfo)
			ctx = NewActivityContext(ctx, nil, mockLpService, nil, mockSEODataService, mockDataForSEO, nil, nil, nil, nil, nil, mockSuggestedKeywords, nil, mockKeywordInfoService, nil, nil, nil, mockAdwordsClient, mockSEOGoogleKeywordInfoService, mockSeoFailedWorkflowInfoService, nil, nil)

			env.SetWorkerOptions(worker.Options{
				BackgroundActivityContext: ctx,
			})

			env.ExecuteWorkflow(KeywordInfoWorkflow, c.workflowParams)

			if !env.IsWorkflowCompleted() {
				t.Errorf("Workflow failed to complete. Error (%s)", env.GetWorkflowError())
			}
			errorValue := env.GetWorkflowError()
			assert.ErrorContains(t, errorValue, c.expectedWorkflowErr.Error())
		})
	}
}
func Test_KeywordInfoWorkflowErrorAfterRetryAttemps(t *testing.T) {
	type testCase struct {
		name                           string
		workflowParams                 *KeywordInfoWorkflowParams
		listingProfiles                []*listingprofilemodel.ListingProfile
		startDate                      time.Time
		endDate                        time.Time
		expectedUpsertKeywordInfoCalls []*ExpectedUpsertKeywordInfo
		expectedAdvertisingResp        *advertising.KeywordMetricsResponse
		ExpectedKeywordSearchParam     []*KeywordSearchParams
		expectedResponse               interface{}
		expectedWorkflowErr            error
		expectedUpdateSeoFailed        []*seofailedworkflowinfo.SEOFailedWorkflowInfo
		expectedSEOFailedWorkflowInfos []*seofailedworkflowinfo.SEOFailedWorkflowInfo
		expectedAdvertisingErr         error
		expectedAdwordsCallCount       int
	}
	cases := []*testCase{
		{
			name:      "Test fails when GoogleAds returns error",
			startDate: time.Date(2019, 12, 1, 0, 0, 0, 0, time.UTC),
			endDate:   time.Date(2019, 12, 1, 0, 0, 0, 0, time.UTC),
			workflowParams: &KeywordInfoWorkflowParams{
				LocationID:             1,
				DataForSEOLocationName: "SEO Location Name",
				LocationCountryCode:    "Location Country Code",
				City:                   "",
				Businesses: []*KeywordSearchParams{
					{
						BusinessID: "AG-1",
						Keywords:   []string{"Keyword1", "Keyword2"},
					},
					{
						BusinessID: "AG-2",
						Keywords:   []string{"Keyword3", "Keyword4"},
					},
				},
				InitialSleepDuration: 0,
				Date:                 "2020-02-10",
			},
			listingProfiles: []*listingprofilemodel.ListingProfile{
				{
					BusinessID: "AG-1",
					Website:    "www.website.com",
					RichData: &listingprofilemodel.RichData{
						SEOKeywords: []string{"Keyword1", "Keyword2"},
					},
				},
				{
					BusinessID: "AG-2",
					Website:    "www.website.com",
					RichData: &listingprofilemodel.RichData{
						SEOKeywords: []string{"Keyword3", "Keyword4"},
					},
				},
			},
			expectedAdvertisingResp: &advertising.KeywordMetricsResponse{

				Results: []*advertising.SearchResult{
					{
						SearchQuery: "test",
						HistoricalMetrics: &advertising.HistoricalMetrics{
							AvgMonthlySearches:          "1300",
							CompetitionLevel:            "LOW",
							CompetitionIndex:            "31",
							TopOfPageBidLowRangeMicros:  "303790",
							TopOfPageBidHighRangeMicros: "1300000",
							MonthlySearchVolumes: []*advertising.MonthlySearchVolume{
								{
									Month:           "JANUARY",
									Year:            "2025",
									MonthlySearches: "1000",
								},
							},
						},
					},
				},
			},
			expectedUpdateSeoFailed: []*seofailedworkflowinfo.SEOFailedWorkflowInfo{
				{
					BusinessID:        "AG-1",
					WorkflowId:        "default-test-workflow-id",
					WorkflowType:      "KeywordInfoWorkflow",
					StartDate:         "2020-02-10",
					Keywords:          []string{"Keyword1", "Keyword2"},
					ErrorMessage:      "error getting keyword metrics from google api: (type: unexpected_error, retryable: true)",
					ErrorType:         "unexpected_error",
					StatusCode:        "",
					RetryCount:        1,
					IsRetryableError:  false,
					RetriedWorkflowId: "SERP-AG1-NEW",
					RetriedDate:       "",
					IssueResolved:     false,
				},
				{
					BusinessID:        "AG-2",
					WorkflowId:        "default-test-workflow-id",
					WorkflowType:      "KeywordInfoWorkflow",
					StartDate:         "2020-02-10",
					Keywords:          []string{"Keyword3", "Keyword4"},
					ErrorMessage:      "error getting keyword metrics from google api: (type: unexpected_error, retryable: true)",
					ErrorType:         "unexpected_error",
					StatusCode:        "",
					RetryCount:        1,
					IsRetryableError:  false,
					RetriedWorkflowId: "SERP-AG2-NEW",
					RetriedDate:       "",
					IssueResolved:     false,
				},
			},
			expectedSEOFailedWorkflowInfos: []*seofailedworkflowinfo.SEOFailedWorkflowInfo{
				{
					BusinessID:        "AG-1",
					WorkflowId:        "default-test-workflow-id",
					WorkflowType:      "KeywordInfoWorkflow",
					StartDate:         "2020-02-10",
					Keywords:          []string{"Keyword1", "Keyword2"},
					ErrorMessage:      "error getting keyword metrics from google api: (type: unexpected_error, retryable: true)",
					ErrorType:         "unexpected_error",
					StatusCode:        "",
					RetryCount:        0,
					IsRetryableError:  true,
					RetriedWorkflowId: "",
					RetriedDate:       "",
					IssueResolved:     false,
				},
				{
					BusinessID:        "AG-2",
					WorkflowId:        "default-test-workflow-id",
					WorkflowType:      "KeywordInfoWorkflow",
					StartDate:         "2020-02-10",
					Keywords:          []string{"Keyword3", "Keyword4"},
					ErrorMessage:      "error getting keyword metrics from google api: (type: unexpected_error, retryable: true)",
					ErrorType:         "unexpected_error",
					StatusCode:        "",
					RetryCount:        0,
					IsRetryableError:  true,
					RetriedWorkflowId: "",
					RetriedDate:       "",
					IssueResolved:     false,
				},
			},
			expectedAdvertisingErr:   errors.New("Google Ads API failure"),
			expectedAdwordsCallCount: 10,
			expectedWorkflowErr:      errors.New("activity error (type: getAndStoreKeywordInfoUsingGoogleAPIV2, scheduledEventID: 0, startedEventID: 0, identity: ): error getting keyword metrics from google api: (type: unexpected_error, retryable: true)"),
		}}
	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			ctx := context.Background()
			ctrl := gomock.NewController(t)
			mockLpService := listingprofileservice.NewMockInterface(ctrl)
			mockSeoFailedWorkflowInfoService := seofailedworkflowinfoservice.NewMockService(ctrl)

			for _, lp := range c.listingProfiles {
				mockLpService.EXPECT().Get(gomock.Any(), lp.BusinessID, false).Return(lp, nil).AnyTimes()
			}

			mockDataForSEO := dataforseo.NewMockSERPClient(ctrl)
			mockSuggestedKeywords := seosuggestedkeywordsservice.NewMockService(ctrl)

			mockSEODataService := seodataservice.NewMockService(ctrl)
			mockAdwordsClient := adver.NewMockAdwordsClientInterface(ctrl)
			mockAdwordsClient.EXPECT().GetKeywordHistoricalMetricsAPI(gomock.Any(), gomock.Any()).Return(nil, c.expectedAdvertisingErr).AnyTimes()
			mockKeywordInfoService := keywordinfoservice.NewMockService(ctrl)
			mockKeywordInfoService.EXPECT().Upsert(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
			mockSEOGoogleKeywordInfoService := seogooglekeywordinfoservice.NewMockService(ctrl)
			for _, lv := range c.expectedSEOFailedWorkflowInfos {
				mockSeoFailedWorkflowInfoService.EXPECT().Upsert(gomock.Any(), lv).Return(nil).Times(len(c.expectedSEOFailedWorkflowInfos))
			}
			for _, v := range c.expectedUpdateSeoFailed {
				if v.ErrorType != "" {
					mockSeoFailedWorkflowInfoService.EXPECT().UpdateFailedWorkflowinfo(gomock.Any(), v).Return(nil).AnyTimes()
				}
			}
			testSuite := &testsuite.WorkflowTestSuite{}
			env := testSuite.NewTestWorkflowEnvironment()
			env.RegisterWorkflow(KeywordInfoWorkflow)
			env.RegisterActivity(getGoogleDataLastUpdateDate)
			env.RegisterActivity(buildKeywordInfoRequest)
			env.RegisterActivity(postKeywordInfoRequest)
			env.RegisterActivity(getAndStoreKeywordInfo)
			env.RegisterActivity(getAndStoreKeywordInfoUsingGoogleAPI)
			env.RegisterActivity(getAndStoreKeywordInfoUsingGoogleAPIV2)
			env.RegisterActivity(storeFailedWorkflowInfo)
			ctx = NewActivityContext(ctx, nil, mockLpService, nil, mockSEODataService, mockDataForSEO, nil, nil, nil, nil, nil, mockSuggestedKeywords, nil, mockKeywordInfoService, nil, nil, nil, mockAdwordsClient, mockSEOGoogleKeywordInfoService, mockSeoFailedWorkflowInfoService, nil, nil)

			env.SetWorkerOptions(worker.Options{
				BackgroundActivityContext: ctx,
			})

			env.ExecuteWorkflow(KeywordInfoWorkflow, c.workflowParams)

			if !env.IsWorkflowCompleted() {
				t.Errorf("Workflow failed to complete. Error (%s)", env.GetWorkflowError())
			}
			errorValue := env.GetWorkflowError()
			assert.ErrorContains(t, errorValue, c.expectedWorkflowErr.Error())
		})
	}
}
