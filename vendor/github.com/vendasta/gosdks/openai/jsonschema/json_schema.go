package jsonschema

import (
	"encoding/json"
	"fmt"
	"reflect"
)

const (
	DescriptionTag = "description"
	RequiredTag    = "required"
)

type DataType string

const (
	DataTypeObject  DataType = "object"
	DataTypeNumber  DataType = "number"
	DataTypeInteger DataType = "integer"
	DataTypeString  DataType = "string"
	DataTypeBoolean DataType = "boolean"
	DataTypeSlice   DataType = "array"
)

// Schema represents information about the top-level object or nested properties.
type Schema struct {
	Description string   `json:"description,omitempty"`
	Type        DataType `json:"type"`
	// TODO: https://github.com/sashabaranov/go-openai/blob/master/jsonschema/json.go#L30
	// Enum []string `json:"enum,omitempty"`
	Properties           map[string]Schema `json:"properties,omitempty"`
	Required             []string          `json:"required,omitempty"`
	Items                *Schema           `json:"items,omitempty"`
	AdditionalProperties any               `json:"additionalProperties,omitempty"`
}

// MarshalJSON is a custom JSON marshaller that allows the Properties and Required fields to be set to empty value
func (s *Schema) MarshalJSON() ([]byte, error) {
	data := make(map[string]interface{})
	if s.Description != "" {
		data["description"] = s.Description
	}
	if s.Type != "" {
		data["type"] = s.Type
	}
	if s.Properties != nil {
		data["properties"] = s.Properties
	}
	if s.Required != nil {
		data["required"] = s.Required
	}
	if s.Items != nil {
		data["items"] = s.Items
	}
	if s.AdditionalProperties != nil {
		data["additionalProperties"] = s.AdditionalProperties
	}
	return json.Marshal(data)
}

// ParametersToJSONSchema generates JSON based on the provided parameters
func ParametersToJSONSchema(obj interface{}, strict bool) ([]byte, error) {
	return marshalJSON(reflect.ValueOf(obj), strict)
}

func marshalJSON(value reflect.Value, strict bool) ([]byte, error) {
	if value.Kind() != reflect.Struct {
		return nil, fmt.Errorf("input must be a struct")
	}

	definition := Schema{
		Type:       "object",
		Properties: make(map[string]Schema),
	}

	allFieldNames := make([]string, value.NumField())
	for i := 0; i < value.NumField(); i++ {
		field := value.Type().Field(i)
		description := field.Tag.Get(DescriptionTag)
		propertyType, err := getSchemaType(field.Type)
		if err != nil {
			return nil, err
		}
		propertyDefinition := Schema{
			Description: description,
			Type:        propertyType,
		}

		if propertyType == DataTypeObject {
			fieldType := field.Type
			if field.Type.Kind() == reflect.Ptr {
				fieldType = field.Type.Elem()
			}
			if err := handleObjectType(fieldType, &propertyDefinition, strict); err != nil {
				return nil, err
			}
		} else if propertyType == DataTypeSlice {
			elemType := field.Type.Elem()
			elemSchemaType, err := getSchemaType(elemType)
			if err != nil {
				return nil, err
			}

			if elemSchemaType == DataTypeObject {
				// Handle slice of objects and pointers to objects
				elemIsPtr := elemType.Kind() == reflect.Ptr
				if elemIsPtr {
					elemType = elemType.Elem()
				}
				if err := handleObjectType(elemType, &propertyDefinition.Items, strict); err != nil {
					return nil, err
				}
			} else {
				propertyDefinition.Items = &Schema{
					Type: elemSchemaType,
				}
			}
		}

		definition.Properties[field.Name] = propertyDefinition
		allFieldNames[i] = field.Name

		if field.Tag.Get(RequiredTag) == "true" {
			definition.Required = append(definition.Required, field.Name)
		}
	}

	if strict {
		definition.AdditionalProperties = false
		definition.Required = allFieldNames
	}

	return json.MarshalIndent(definition, "", "    ")
}

func handleObjectType(t reflect.Type, v any, strict bool) error {
	if t.Kind() == reflect.Ptr {
		t = t.Elem()
	}
	// Recursively evaluate nested struct properties
	nestedValue := reflect.New(t).Elem()
	nestedDefinition, err := marshalJSON(nestedValue, strict)
	if err != nil {
		return err
	}
	return json.Unmarshal(nestedDefinition, v)
}

func getSchemaType(propertyType reflect.Type) (DataType, error) {
	switch propertyType.Kind() {
	case reflect.Struct:
		return DataTypeObject, nil
	case reflect.Ptr:
		return getSchemaType(propertyType.Elem())
	case reflect.String:
		return DataTypeString, nil
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		return DataTypeInteger, nil
	case reflect.Float32, reflect.Float64:
		return DataTypeNumber, nil
	case reflect.Bool:
		return DataTypeBoolean, nil
	case reflect.Slice:
		return DataTypeSlice, nil
	default:
		return "", fmt.Errorf("unsupported type %s", propertyType.Kind())
	}
}
