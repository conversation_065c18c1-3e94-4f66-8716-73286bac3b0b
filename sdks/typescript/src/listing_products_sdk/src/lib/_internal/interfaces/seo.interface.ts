// *********************************
// Code generated by sdkgen
// DO NOT EDIT!.
//
// Interfaces.
// *********************************
import { FieldMaskInterface } from './field-mask.interface';
import { GeoInterface } from './listing-profile.interface';
import * as e from '../enums';

export interface AIOAuditResultsInterface {
    businessId?: string;
    brandName?: string;
    websiteUrl?: string;
    auditDate?: string;
    startDate?: Date;
    totalPages?: number;
    auditStatus?: string;
    auditSummary?: string;
    auditPages?: AuditPageDataInterface[];
    auditScoreResults?: AuditScoreResultsInterface[];
    keyword?: string;
    auditId?: string;
    auditUrl?: string;
}

export interface AddonActivationInterface {
    businessId?: string;
    appId?: string;
    addonId?: string;
    activated?: Date;
    deactivated?: Date;
    status?: e.AddonActivationAddonActivationStatus;
    isTrial?: boolean;
    count?: number;
}

export interface AuditPageDataInterface {
    pageUrl?: string;
    pageData?: string;
}

export interface AuditScoreResultsInterface {
    auditPageUrl?: string;
    auditScores?: AuditScoresInterface[];
}

export interface AuditScoresInterface {
    auditScoreScopeName?: string;
    auditScoreScopeValue?: number;
    auditScoreScopeSummary?: string[];
    auditScoreRecommendations?: string[];
}

export interface BusinessKeywordsInterface {
    businessId?: string;
    keywords?: string[];
}

export interface GetAIOAuditRequestInterface {
    businessId?: string;
    brandName?: string;
    websiteUrl?: string;
}

export interface GetAIOAuditResponseInterface {
    audit?: AIOAuditResultsInterface;
}

export interface GetAIOAuditStatusRequestInterface {
    businessId?: string;
    brandName?: string;
    websiteUrl?: string;
}

export interface GetAIOAuditStatusResponseInterface {
    auditStatus?: string;
}

export interface GetActiveSEOAddonsRequestInterface {
    businessId?: string;
}

export interface GetActiveSEOAddonsResponseInterface {
    activeAddons?: AddonActivationInterface[];
}

export interface GetAllAIOAuditRequestInterface {
    businessId?: string;
}

export interface GetAllAIOAuditResponseInterface {
    audit?: AIOAuditResultsInterface[];
}

export interface GetAllAIOAuditScoreResultsRequestInterface {
    businessId?: string;
    brandName?: string;
    websiteUrl?: string;
}

export interface GetAllAIOAuditScoreResultsResponseInterface {
    auditScoreResults?: AuditScoreResultsInterface[];
}

export interface GetDataForSEOCategoryRequestInterface {
    businessId?: string;
}

export interface GetDataForSEOCategoryResponseInterface {
    businessId?: string;
    primaryCategoryId?: string;
    categoryIds?: string[];
    taskId?: string;
    rawResponse?: string;
    created?: Date;
    updated?: Date;
    deleted?: Date;
}

export interface GetLocalSearchSEODataRequestInterface {
    businessId?: string;
    keyword?: string;
    startDate?: Date;
    endDate?: Date;
}

export interface GetLocalSearchSEODataResponseInterface {
    keyword?: string;
    localSearchData?: LocalSearchDataInterface[];
}

export interface GetSEODataRequestInterface {
    businessId?: string;
    keywords?: string[];
    startDate?: Date;
    endDate?: Date;
}

export interface GetSEODataResponseInterface {
    data?: SEODataInterface[];
    previousData?: SEODataInterface[];
}

export interface GetSEODataSummaryRequestInterface {
    businessId?: string;
    keywords?: string[];
    startDate?: Date;
    endDate?: Date;
}

export interface GetSEODataSummaryResponseInterface {
    data?: SEODataSummaryInterface[];
    previousData?: SEODataSummaryInterface[];
}

export interface GetSEOSettingsRequestInterface {
    businessId?: string;
}

export interface LocalSearchDataInterface {
    keyword?: string;
    vicinity?: e.Vicinity;
    searchLocation?: GeoInterface;
    results?: LocalSearchResultInterface[];
}

export interface LocalSearchResultInterface {
    businessName?: string;
    address?: string;
    url?: string;
    rank?: string;
    isMainBusiness?: boolean;
    reviews?: LocalSearchReviewsInterface;
    phoneNumber?: string;
    claimStatus?: e.GBPClaimStatus;
}

export interface LocalSearchReviewsInterface {
    rating?: number;
    count?: string;
}

export interface SEODataInterface {
    keyword?: string;
    date?: Date;
    localRank?: number;
    organicRank?: number;
    difficulty?: number;
    searchVolume?: number;
    localSearches?: LocalSearchDataInterface[];
    searchRadius?: number;
    workflowUrl?: string;
}

export interface SEODataSummaryInterface {
    keyword?: string;
    date?: Date;
    localRank?: number;
    organicRank?: number;
    difficulty?: number;
    searchVolume?: number;
    searchRadius?: number;
    workflowUrl?: string;
}

export interface SEOSettingsResponseInterface {
    businessId?: string;
    localSearchRadius?: number;
    favoriteKeywords?: string[];
    isFullSearchEnabled?: boolean;
}

export interface SaveSEOSettingsRequestInterface {
    businessId?: string;
    localSearchRadius?: number;
    favoriteKeywords?: string[];
    fieldMask?: FieldMaskInterface;
    isFullSearchEnabled?: boolean;
}

export interface StartLocalSEODataWorkflowRequestInterface {
    businesses?: BusinessKeywordsInterface[];
    forceSerpWorkflow?: boolean;
    date?: Date;
    forceKeywordInfoWorkflow?: boolean;
    forceSuggestedKeywordsWorkflow?: boolean;
    ignoreDataLakeResults?: boolean;
}

export interface StartSEOCategoryWorkflowRequestInterface {
    businessIds?: string[];
}

export interface TriggerAIOAuditRequestInterface {
    businessId?: string;
    brandName?: string;
    websiteUrl?: string;
}

export interface TriggerAIOAuditResponseInterface {
    businessId?: string;
    brandName?: string;
    websiteUrl?: string;
    message?: string;
    auditStatus?: string;
}

