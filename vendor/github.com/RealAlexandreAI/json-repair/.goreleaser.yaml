# This is an example .goreleaser.yml file with some sensible defaults.
# Make sure to check the documentation at https://goreleaser.com

# The lines below are called `modelines`. See `:help modeline`
# Feel free to remove those if you don't want/need to use them.
# yaml-language-server: $schema=https://goreleaser.com/static/schema.json
# vim: set ts=2 sw=2 tw=0 fo=cnqoj

version: 1
project_name: jsonrepair

before:
  hooks:
    # You may remove this if you don't use go modules.
    - go mod tidy
    # you may remove this if you don't need go generate
    - go generate ./...

builds:
  - id: "cli"
    binary: jsonrepair
    env:
      - CGO_ENABLED=0
    goos:
      - linux
      - windows
      - darwin
    ldflags:
      - -s
      - -w
    main: ./cli
archives:
  - format: tar.gz
    # this name template makes the OS and Arch compatible with the results of `uname`.
    name_template: >-
      {{ .ProjectName }}_
      {{- title .Os }}_
      {{- if eq .Arch "amd64" }}x86_64
      {{- else if eq .Arch "386" }}i386
      {{- else }}{{ .Arch }}{{ end }}
      {{- if .Arm }}v{{ .Arm }}{{ end }}
    # use zip for windows archives
    format_overrides:
      - goos: windows
        format: zip
checksum:
  name_template: 'checksums.txt'
changelog:
  use: github
  sort: asc
  filters:
    exclude:
      - "^docs:"
      - "^test:"

release:
  github:
    owner: RealAlexandreAI
    name: json-repair
  draft: false

brews:
  - name: jsonrepair
    repository:
      owner: RealAlexandreAI
      name: homebrew-tap-jsonrepair
      branch: main
      token: "{{ .Env.TAP_GITHUB_TOKEN }}"
    url_template: "https://github.com/RealAlexandreAI/json-repair/releases/download/{{ .Tag }}/{{ .ArtifactName }}"
    download_strategy: CurlDownloadStrategy
    # Git author used to commit to the repository.
    # Defaults are shown.
    commit_author:
      name: goreleaserbot
      email: <EMAIL>
    directory: Formula
    homepage: "https://github.com/RealAlexandreAI/json-repair"
    description: "🔧 Repair JSON! Solution for JSON Anomalies from LLMs"
    license: "GPLv3"
    skip_upload: false
    test: |
      system "#{bin}/jsonrepair -v"