package aioauditresult

import (
	"time"

	vstore "github.com/vendasta/vstore/vstore/sdks/go/v1"
)

const (
	AIOAuditResultKind   = "AIOAuditResult"
	AIOAuditResultPubsub = "aio-audit-result-pubsub"

	// Composite index names - following SEOData pattern
	BusinessIDDeletedIndex          = "business_id_deleted_index"
	BusinessIDAuditDateDeletedIndex = "business_id_audit_date_deleted_index"
)

// PageAuditData stores the complete JSON audit data as a string
type AuditPageData struct {
	PageURL  string `vstore:"page_url"`
	PageData string `vstore:"page_data"`
}

type AuditScores struct {
	AuditScoreScopeName       string   `vstore:"audit_score_scope_name"`
	AuditScoreScopeValue      int64    `vstore:"audit_score_scope_value"`
	AuditScoreScopeSummary    []string `vstore:"audit_score_scope_summary"`
	AuditScoreRecommendations []string `vstore:"audit_score_recommendations"`
}

type AuditScoreResults struct {
	AuditPageURL string         `vstore:"audit_page_url"`
	AuditScores  []*AuditScores `vstore:"audit_scores"`
}

// AuditStatus represents the status of an audit
type AuditStatus string

const (
	AuditStatusRunning   AuditStatus = "running"
	AuditStatusCompleted AuditStatus = "completed"
	AuditStatusFailed    AuditStatus = "failed"
)

type AIOAuditResult struct {
	BusinessID        string               `vstore:"business_id"`
	BrandName         string               `vstore:"brand_name"`
	WebsiteURL        string               `vstore:"website_url"`
	AuditDate         string               `vstore:"audit_date"`
	StartDate         time.Time            `vstore:"start_date"`
	TotalPages        int64                `vstore:"total_pages"`
	AuditStatus       string               `vstore:"audit_status"`
	AuditSummary      string               `vstore:"audit_summary"`
	AuditPages        []*AuditPageData     `vstore:"audit_pages"`
	AuditScoreResults []*AuditScoreResults `vstore:"audit_score_results"`
	Created           time.Time            `vstore:"created"`
	Updated           time.Time            `vstore:"updated"`
	Deleted           time.Time            `vstore:"deleted"`
}

func Schema() *vstore.Schema {
	props := vstore.NewPropertyBuilder().PropertiesFromStruct((*AIOAuditResult)(nil)).
		PropertyConfig("business_id", vstore.Required()).
		PropertyConfig("brand_name", vstore.Required()).
		PropertyConfig("website_url", vstore.Required()).
		PropertyConfig("audit_date", vstore.Required()).
		Build()

	PrimaryKey := []string{"business_id", "website_url", "audit_date"}

	// Create composite indexes following SEOData pattern
	compositeIndexes := vstore.NewCompositeIndexBuilder().
		Index(
			BusinessIDDeletedIndex,
			vstore.Column("business_id", vstore.ASC),
			vstore.Column("deleted", vstore.ASC),
		).
		Index(
			BusinessIDAuditDateDeletedIndex,
			vstore.Column("business_id", vstore.ASC),
			vstore.Column("audit_date", vstore.DESC),
			vstore.Column("deleted", vstore.ASC),
		).
		Build()

	secondaryIndexes := vstore.NewSecondaryIndexBuilder().
		BigQuery("bigquery").
		PubSub(AIOAuditResultPubsub).
		Build()

	backupConfig := vstore.NewBackupConfigBuilder().PeriodicBackup(vstore.WeeklyBackup).Build()

	return vstore.NewSchema(AIOAuditResultKind, PrimaryKey, props, secondaryIndexes, backupConfig, vstore.CompositeIndexes(compositeIndexes))
}
