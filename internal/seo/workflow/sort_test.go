package seoworkflow

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/vendasta/listing-products/internal/dataforseo"
)

func Test_sort(t *testing.T) {
	type testCase struct {
		name               string
		tasks              []*PostedTask
		expectedBatches    []*PostedTaskGroup
		dataForSeoResponse *dataforseo.GoogleMapsSERPResponse
		expectedTotal      int
		expectedErr        error
	}
	cases := []*testCase{
		{
			name: "Test sorts rows into groups by keyword",
			tasks: []*PostedTask{
				{
					ID:         "1",
					BusinessID: "AG-1",
					Keyword:    "keyword1",
					Vicinity:   "VICINITY_A1",
					Lat:        0,
					Lng:        0,
				},
				{
					ID:         "2",
					BusinessID: "AG-1",
					Keyword:    "keyword1",
					Vicinity:   "VICINITY_A2",
					Lat:        0,
					Lng:        0,
				},
				{
					ID:         "3",
					BusinessID: "AG-1",
					Keyword:    "keyword2",
					Vicinity:   "VICINITY_A3",
					Lat:        0,
					Lng:        0,
				},
				{
					ID:         "4",
					BusinessID: "AG-1",
					Keyword:    "keyword2",
					Vicinity:   "VICINITY_A4",
					Lat:        0,
					Lng:        0,
				},
			},
			expectedBatches: []*PostedTaskGroup{
				{
					BusinessID: "AG-1", Keyword: "keyword1", Tasks: []*PostedTask{
						{
							ID:         "1",
							BusinessID: "AG-1",
							Keyword:    "keyword1",
							Vicinity:   "VICINITY_A1",
							Lat:        0,
							Lng:        0,
						},
						{
							ID:         "2",
							BusinessID: "AG-1",
							Keyword:    "keyword1",
							Vicinity:   "VICINITY_A2",
							Lat:        0,
							Lng:        0,
						},
					},
				},

				{
					BusinessID: "AG-1", Keyword: "keyword2", Tasks: []*PostedTask{
						{
							ID:         "3",
							BusinessID: "AG-1",
							Keyword:    "keyword2",
							Vicinity:   "VICINITY_A3", Lat: 0,
							Lng: 0,
						},
						{
							ID:         "4",
							BusinessID: "AG-1",
							Keyword:    "keyword2",
							Vicinity:   "VICINITY_A4",
							Lat:        0,
							Lng:        0,
						},
					},
				},
			},
			expectedTotal: 4,
			expectedErr:   nil,
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			ctx := context.Background()

			ctx = NewActivityContext(ctx, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil)

			resp, err := sortTasks(ctx, c.tasks)

			assert.Equal(t, c.expectedBatches, resp)
			assert.Equal(t, c.expectedErr, err)
		})
	}
}
