# 4.88.1
- Add `TriggerAIOAudit` endpoint
- Add `GetAIOAuditStatus` endpoint
- Add `GetAllAIOAuditScoreResults` endpoint
- Add `GetAllAIOAudit` endpoint
- Add `GetAIOAudit` endpoint

# 4.88.0
Add `GetMultiAccountGroup` endpoint

# 4.87.0
Add `GetCitationSummary` endpoint

# 4.86.0
- Add `field_mask` to endpoints for fetching Listing Sources
- Add `ListCitationDomains` endpoint

# 4.85.0
- Add new endpoint `GetSEODataSummary` and `GetLocalSearchData` to replace `GetSEOData` to avoid grpc memory limitations

# 4.84.0
- Add `submitted` status for `GetSyncData` endpoint

# 4.83.0
- Add `YEXT_STANDALONE` addon type

# 4.82.0
- Add `GetNeustarPublictions` endpoint

# 4.81.0
- Add `SyncRequirement` enum to source configuration

# 4.80.0
- Add endpoint to BackFillBingInsights `TriggerBingInsightsBackFill`

# 4.79.0
- Add specific fields to the `suggestion` message

# 4.78.0
- Add `GetSyncData` endpoint

# 4.77.0
- Add `hidden` field to `DirectSyncSourceInfo`

# 4.76.0
- Add `last_scraped` timestamp to `SourceAccuracy`

# 4.75.0
- Add Suggestions `Upsert` endpoint

# 4.74.3
- Adding new param `isListingVerified` to directSyncSourceInfo

# 4.74.2
- Add `activation` time to LD endpoint

# 4.74.1
- Add `GetLDData` endpoint

# 4.73.0
- Remove unused and replaced `ListVendorListings` and `ListSocialServices` endpoints

# 4.72.0
- Add `GetDirectSubmitSourceInfo` RPC to `ListingProductsService`

# 4.71.0
- Add `GetConfiguration` endpoint to `PartnerSettingsApiService`

# 4.70.0
- Add `IsPartnerUser` endpoint

# 4.69.0
- Include `totalCount` to `ListSuggestionsResponse`

# 4.68.0
- Include `share_of_voice_service` to `legacy_product_details`

# 4.67.0
- Add new attribute value type

# 4.66.0
- Add `Suggestions` Endpoints

# 4.65.0
- Add `GetBingPlacesInfo` endpoint

# 4.64.0
- Add `UpdateOrigin` to `ExternalIdentifiers`

# 4.63.0
- Rename `Date` to `GoogleDate` to fix typescript SDK generation
- Add `inferred_attributes`, `create_operations`, `competitor` and `marketing_info` to `ListingProfile`

# 4.62.0
- Add `syncing_seo_keywords` to `RichData` of the Listing Profile
- Add `StartSEOCategoryWorkflow` endpoint
- Add `StartBusinessDataScoreWorkflow` endpoint

# 4.61.0
- Add `LegacyProductDetails` to Listing Profile projection filter

# 4.60.0
- Add more account group fields to `ExternalIdentifiers`

# 4.59.9
- Add `LegacyProductDetails` to `ListingProfile`
- Add several account group fields to `ExternalIdentifiers`

# 4.58.0
- Add `LegacyAPICreate` and `LegacyAPIUpdate` endpoints to handle legacy Account Group calls

# 4.57.0
- Add `refresh` flag to `ListSocialServicesRequest`

# 4.56.0
- Add `GetAppleBusinessConnectInfo` endpoint

# 4.55.0
- Update validation for Doctor.com fields to `HealthCareProfessionalInformation`
- Remove `practice_name` from `HealthCareProfessionalInformation`

# 4.54.0
- Add Doctor.com fields to `HealthCareProfessionalInformation`

# 4.53.0
- Allow npm 10

# 4.52.0
- Add Doctor.com categories to `HealthCareProfessionalInformation`

# 4.51.0
- Add

# 4.50.0
- Add `is_favorite_new_keywords_disabled` to `PartnerSettings`

# 4.49.0
- Add `favorite_keywords` to `SEOSettings` endpoints

# 4.48.0
- Add `GetSEOSuggestedKeywords` endpoint to `SEOSuggestedKeywords` service

# 4.47.0
- Add `DeleteCitations` endpoint to `Citations` service

# 4.46.0
- Add `force` flag to `SubmitTurboListerRequest`

# 4.45.0
- Add `languageCode` to `GetMultiListingProfileRequest`

# 4.44.0
- Add missing `languageCode` to the ApiJson method to `GetMoreHoursTypesRequest`

# 4.43.0
- Add `languageCode` to `GetAttributeMetadataRequest`
- Add `languageCode` to `GetMoreHoursTypesRequest`

# 4.42.0
- Add `GetOrGenerateSEOSuggestedKeywords` endpoint

# 4.40.0
- Add `GetCitationData` endpoint

# 4.39.0
- Add `GetProductSettings` endpoint

# 4.38.0
- Add `GetAttributeMetadata` endpoint

# 4.37.0
- Add `date` parameter to `StartLocalSEODataWorkflowRequest`

# 4.36.0
- Add `PartnerSettings` endpoints

# 4.35.0
- Add `BingAttributeMetaData` to interfaces

# 4.34.0
- Add `BingAttributeMetaData` to `ListingProfile`

# 4.33.0
- Add `workflow_url` to `SEOData`

# 4.32.0
- Refactor `UpdateBingSyncFlag` endpoint to `UpdateVendorSyncFlag`
- Rename `ListDataListingsVendor` to `ListVendorListings`
- Adds `search_radius` field to `SEOData`

## 4.31.0
- Add `GetActiveSEOAddons` endpoint

## 4.30.0
- Add `ListDataVendorListing` and `UpdateBingSyncFlag` endpoints

## 4.29.0
- Add `previous_data` field to `GetSEOData` endpoint

## 4.28.0
- Add `GetFacebookPageInfo` endpoint

## 4.27.0
- Add `GetSEOSettings` and `SaveSEOSettings` endpoint

## 4.26.0
- Add `Citations` service and `StartCitationWorkflow` endpoint

## 4.25.0
- Add `GetMoreHoursTypes` endpoint

## 4.24.0
- Add `GetSyndicationInfo` endpoint

## 4.23.0
- Add `StartLocalSEODataWorkflow` endpoint to SEO service
- Add additional filtering parameters to `GetSEOData` endpoint

## 4.22.1
- Add `SEOService` RPC and `GetSEOData` endpoint to ListingProducts
- Add `seo_keywords` field to `ListingProfile`'s `RichData` object

## 4.20.0
- Add Listing ID SocialService object

## 4.19.1
- Add Accuracy, and score data, URL, and Source ID fields to SocialService object

## 4.18.0
- Add `GetInfoGroupId` endpoint

## 4.17.0
- Add `ListSocialServices` endpoint

## 4.16.0
- Remove redundant More Hours field from Listing Profile

## 4.15.0
- Update to Angular 15

## 4.14.0
- Add `isGmbVerified`, `hasGmbLocation`, `reconnectLink`, `socialTokenBroken` fields to `GetGoogleMyBusinessInsightsDataBucketedResponseInterface` and `GetGoogleMyBusinessInsightsDataRequestInterface`

## 4.13.0
- Add more hours fields to `ListingProfile` object

## 4.12.0
- Add admin scope to `CreateListingSource` endpoint

## 4.11.0
- Add `ServiceArea` object to the `ListingProfile` `Location` object

## 4.10.0
- Add `email` to the `RichData` object of the `ListingProfile`

## 4.9.0
- Add `values` to GoogleAttributeMetaData for GetMulti response

## 4.8.0
- Google Attribute removed Value Type from definition

## 4.7.0
- Google Attribute added Value Type to definition
- Removed ListListingProfiles

## 4.6.0
- remove contact details from listing profile
- add back social urls to listing profile
- add cellnumber and faxnumber to rich data of listing profile

## 4.5.1
- actually add the metadata to the listing profile

## 4.5.0
- add Google Attributes to Listing Profile

## 4.4.0
- removed social URLs from syndication (this was incorrect on my part and will need to be reverted, Uberall uses them)

## 4.3.0
- update sdk with listing profile's special hours

## 4.2.3
- align listing profile enums with the account group enums

## 4.2.2
- regenerate listing profile objects (which removes market id from listing profile)

## 4.2.1
- regenerate listing profile objects

## 4.2.0
- add listing profile functionality to sdk

## 4.1.0
- added endpoint to get text dump of Google business information for an account group

## 4.0.2
- add admin scope to `UpdateListingSource` endpoint

## 4.0.0
- Update to Angular 13
- BREAKING CHANGE: Removed the module as it is no longer needed, and removed the unused getListingAccuracy function

## 3.0.3
- Add module

## 3.0.2
- Add GetListingAccuracy endpoint
- Publish to NPM

## 3.0.1
- Generate NPM package

## 3.0.0
- Change name of fields in `GetGoogleMyBusinessInsightsDataBucketedResponse`
    - `actionsDrivingDirectionsSum` is now `actionsDrivingDirections`
    - `actionsPhoneSum` is now `actionsPhone`
- `InsightBucketInterface` contains `value` (number) and `bucketLabel` (string)
- Remove getDayOfWeek helper function

## 2.1.0
- Export getDayOfWeek helper function

## 2.0.0
- Change response interface of `getGoogleMyBusinessInsightsDataBucketed`.
    - `InsightBucketedInterface.value` is now `InsightBucketedInterface.totalAcrossBuckets`
    - `InsightBucketedInterface.change` is now `InsightBucketedInterface.totalChangeAcrossBuckets`
    - `InsightBucketedInterface.buckets` is an `InsightBucketInterface`
        - This contains a value (string) and dayOfWeek (enum).
        - Formerly contained value and date.
- Export the `InsightBucketInterface`.
- Add helper function to convert the `dayOfWeek` enum to string.

## 1.2.0
- Added wrappers which handle the request interface and http errors for getGoogleMyBusinessInsightsData and
  getGoogleMyBusinessInsightsDataBucketed. Http errors throw an error observable.
- Exported interfaces that describe the data returned from the backend for these functions.

## 1.1.0
- Add listing-products.service.ts wrapper

## 1.0.0
- Initial generated sdk
