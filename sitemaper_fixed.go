package main

import (
	"bufio"
	"encoding/xml"
	"flag"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"sync"
)

// -- Structures --
type SitemapIndex struct {
	Sitemaps []struct {
		Loc string `xml:"loc"`
	} `xml:"sitemap"`
}
type URLSet struct {
	URLs []struct {
		Loc string `xml:"loc"`
	} `xml:"url"`
}

// -- Globals --
var (
	wg       sync.WaitGroup
	mu       sync.Mutex
	visited  = make(map[string]bool)
	results  []PageResult
	rootHost string
)

// PageResult holds a URL and its raw HTML size in bytes
type PageResult struct {
	URL      string
	HTMLSize int
}

// -- Fetch functions --
func fetchAndParse(url string) ([]byte, error) {
	resp, err := http.Get(url)
	if err != nil {
		return nil, fmt.Errorf("error fetching %s: %v", url, err)
	}
	defer resp.Body.Close()

	// Check for HTTP error status codes
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("HTTP %d error fetching %s", resp.StatusCode, url)
	}

	return io.ReadAll(resp.Body)
}

func fetchRobotsTxt(domain string) ([]string, error) {
	robotsURL := domain + "/robots.txt"
	resp, err := http.Get(robotsURL)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch robots.txt: %v", err)
	}
	defer resp.Body.Close()

	fmt.Printf("\n📄 robots.txt content from %s:\n\n", robotsURL)
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("error reading robots.txt: %v", err)
	}
	fmt.Println(string(body)) // 👈 Print full robots.txt

	// Parse lines for sitemap URLs (case-insensitive)
	var sitemaps []string
	scanner := bufio.NewScanner(strings.NewReader(string(body)))
	for scanner.Scan() {
		line := scanner.Text()
		lineLower := strings.ToLower(strings.TrimSpace(line))
		if strings.HasPrefix(lineLower, "sitemap:") {
			sitemap := strings.TrimSpace(line[8:]) // Remove "sitemap:" prefix
			if sitemap != "" {
				sitemaps = append(sitemaps, sitemap)
			}
		}
	}
	return sitemaps, nil
}

// tryFallbackSitemaps attempts common WordPress sitemap URLs when robots.txt fails
func tryFallbackSitemaps(domain string) []string {
	fallbackURLs := []string{
		domain + "/sitemap.xml",
		domain + "/sitemap_index.xml",
		domain + "/wp-sitemap.xml",
		domain + "/sitemap-index.xml",
	}

	var validSitemaps []string
	for _, sitemapURL := range fallbackURLs {
		fmt.Printf("🔍 Trying fallback sitemap: %s\n", sitemapURL)
		resp, err := http.Get(sitemapURL)
		if err != nil {
			fmt.Printf("❌ Failed to fetch %s: %v\n", sitemapURL, err)
			continue
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			fmt.Printf("❌ HTTP %d for %s\n", resp.StatusCode, sitemapURL)
			continue
		}

		// Read and validate XML content
		body, err := io.ReadAll(resp.Body)
		if err != nil {
			fmt.Printf("❌ Failed to read body for %s: %v\n", sitemapURL, err)
			continue
		}

		bodyStr := strings.TrimSpace(string(body))
		if !strings.HasPrefix(bodyStr, "<?xml") && !strings.HasPrefix(bodyStr, "<urlset") && !strings.HasPrefix(bodyStr, "<sitemapindex") {
			fmt.Printf("❌ %s doesn't contain valid XML (starts with: %.50s...)\n", sitemapURL, bodyStr)
			continue
		}

		fmt.Printf("✅ Found valid sitemap: %s\n", sitemapURL)
		validSitemaps = append(validSitemaps, sitemapURL)
		break // Use the first valid sitemap found
	}

	return validSitemaps
}

// -- Crawler --
func parseSitemap(sitemapURL string) {
	defer wg.Done()

	mu.Lock()
	if visited[sitemapURL] {
		mu.Unlock()
		return
	}
	visited[sitemapURL] = true
	mu.Unlock()

	fmt.Printf("🔍 Parsing sitemap: %s\n", sitemapURL)
	data, err := fetchAndParse(sitemapURL)
	if err != nil {
		fmt.Printf("❌ %v\n", err)
		return
	}

	// Try sitemap index first
	var index SitemapIndex
	if err := xml.Unmarshal(data, &index); err == nil && len(index.Sitemaps) > 0 {
		fmt.Printf("📋 Found sitemap index with %d sitemaps\n", len(index.Sitemaps))
		for _, s := range index.Sitemaps {
			wg.Add(1)
			go parseSitemap(strings.TrimSpace(s.Loc))
		}
		return
	}

	// Try flat URL set
	var urlSet URLSet
	if err := xml.Unmarshal(data, &urlSet); err == nil && len(urlSet.URLs) > 0 {
		fmt.Printf("📄 Found URL set with %d URLs\n", len(urlSet.URLs))
		for _, u := range urlSet.URLs {
			link := strings.TrimSpace(u.Loc)
			if strings.Contains(link, rootHost) {
				wg.Add(1)
				go fetchPageSize(link)
			}
		}
		return
	}

	// Try to extract URLs from any XML format
	fmt.Printf("🔧 Attempting manual URL extraction from XML\n")
	bodyStr := string(data)
	urls := extractURLsFromXML(bodyStr)
	fmt.Printf("🔗 Extracted %d URLs manually\n", len(urls))
	for _, link := range urls {
		if strings.Contains(link, rootHost) {
			wg.Add(1)
			go fetchPageSize(link)
		}
	}
}

func fetchPageSize(pageURL string) {
	defer wg.Done()

	resp, err := http.Get(pageURL)
	if err != nil {
		fmt.Printf("❌ Error fetching %s: %v\n", pageURL, err)
		return
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		fmt.Printf("❌ HTTP %d for %s\n", resp.StatusCode, pageURL)
		return
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("❌ Error reading body %s: %v\n", pageURL, err)
		return
	}

	mu.Lock()
	results = append(results, PageResult{URL: pageURL, HTMLSize: len(body)})
	fmt.Printf("✅ Fetched %s (%d bytes)\n", pageURL, len(body))
	mu.Unlock()
}

func extractURLsFromXML(xmlContent string) []string {
	var urls []string
	lines := strings.Split(xmlContent, "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if strings.HasPrefix(line, "<loc>") && strings.HasSuffix(line, "</loc>") {
			url := strings.TrimPrefix(line, "<loc>")
			url = strings.TrimSuffix(url, "</loc>")
			url = strings.TrimSpace(url)
			if url != "" {
				urls = append(urls, url)
			}
		}
	}
	return urls
}

// -- Main --
func main() {
	input := flag.String("url", "", "Website URL (e.g., https://example.com)")
	flag.Parse()

	if *input == "" {
		fmt.Println("❌ Please provide a website URL using the -url flag.")
		return
	}

	parsed, err := url.Parse(*input)
	if err != nil || parsed.Host == "" {
		fmt.Println("❌ Invalid URL")
		return
	}
	rootHost = parsed.Host
	domain := parsed.Scheme + "://" + rootHost

	fmt.Printf("🚀 Starting sitemap crawl for: %s\n", domain)

	// Try to get sitemaps from robots.txt first
	sitemaps, err := fetchRobotsTxt(domain)
	if err != nil || len(sitemaps) == 0 {
		fmt.Printf("⚠️  No sitemap found in robots.txt or robots.txt failed. Trying fallback URLs...\n")
		sitemaps = tryFallbackSitemaps(domain)

		if len(sitemaps) == 0 {
			fmt.Println("❌ No valid sitemaps found")
			return
		}
	} else {
		// Validate that the robots.txt sitemaps are actually accessible and contain XML
		var validSitemaps []string
		for _, sitemap := range sitemaps {
			fmt.Printf("🔍 Validating sitemap from robots.txt: %s\n", sitemap)
			resp, err := http.Get(sitemap)
			if err != nil {
				fmt.Printf("❌ Failed to validate %s: %v\n", sitemap, err)
				continue
			}
			defer resp.Body.Close()

			if resp.StatusCode != http.StatusOK {
				fmt.Printf("❌ HTTP %d for %s\n", resp.StatusCode, sitemap)
				continue
			}

			// Read a small portion to check if it's actually XML
			body, err := io.ReadAll(resp.Body)
			if err != nil {
				fmt.Printf("❌ Failed to read body for %s: %v\n", sitemap, err)
				continue
			}

			bodyStr := strings.TrimSpace(string(body))
			if !strings.HasPrefix(bodyStr, "<?xml") && !strings.HasPrefix(bodyStr, "<urlset") && !strings.HasPrefix(bodyStr, "<sitemapindex") {
				fmt.Printf("❌ %s doesn't contain valid XML (starts with: %.50s...)\n", sitemap, bodyStr)
				continue
			}

			fmt.Printf("✅ Validated sitemap: %s\n", sitemap)
			validSitemaps = append(validSitemaps, sitemap)
		}

		// If no valid sitemaps from robots.txt, try fallbacks
		if len(validSitemaps) == 0 {
			fmt.Printf("⚠️  All robots.txt sitemaps failed validation. Trying fallback URLs...\n")
			sitemaps = tryFallbackSitemaps(domain)

			if len(sitemaps) == 0 {
				fmt.Println("❌ No valid sitemaps found")
				return
			}
		} else {
			sitemaps = validSitemaps
		}
	}

	fmt.Printf("📍 Found %d valid sitemap(s) to process\n", len(sitemaps))
	for _, sitemap := range sitemaps {
		fmt.Printf("  - %s\n", sitemap)
		wg.Add(1)
		go parseSitemap(sitemap)
	}

	wg.Wait()

	fmt.Printf("\n🎉 Crawl completed!\n")
	fmt.Printf("✅ Total Pages: %d\n", len(results))
	totalSize := 0
	for _, r := range results {
		fmt.Printf(" - %s (%d bytes)\n", r.URL, r.HTMLSize)
		totalSize += r.HTMLSize
	}
	fmt.Printf("\n📦 Total HTML size: %.2f KB\n", float64(totalSize)/1024)
}
