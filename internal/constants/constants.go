package constants

import "time"

/* Project wide constants */

const (
	ListingBuilderMarketplaceAppID = "MS"

	AppID   = "listing-products"
	AppName = "Listing Products Microservice"

	// Default Language for API call when language is not needed / available
	DefaultLanguageCode = "en"

	// Name of the temporal task list for Data Health Check related workflows and activities
	DataHealthTaskList            = "data-health-task-list"
	ActivationTaskList            = "activation-task-list"
	TurboListerTaskList           = "turbolister-task-list"
	SFTPSubmissionTaskList        = "sftp-submission-task-list"
	FBMigrationTaskList           = "fb-migration-task-list"
	UberallMigrationTaskList      = "uberall-migration-task-list"
	NeustarVerificationTaskList   = "neustar-verification-task-list"
	NeustarClaimTaskList          = "neustar-claim-task-list"
	DataAxleVerificationTaskList  = "dataaxle-verification-task-list"
	GoogleBulkConnectTaskList     = "google-bulk-connect-task-list"
	SEOTaskList                   = "seo-task-list"
	SEOTaskListKeyword            = "seo-task-list-keyword"
	SEOTaskListRateLimited        = "seo-task-list-rate-limited"
	AIOAuditTaskList              = "aioaudit-task-list"
	CitationTaskList              = "citation-task-list"
	AppleDQETaskList              = "apple-dqe-task-list"
	AppleUpdateCategoryTaskList   = "apple-update-category-task-list"
	BingBusinessLifecycleTaskList = "bing-business-lifecycle-task-list"
	AppleOpenAICategoriesTaskList = "apple-openai-categories-task-list"
	BusinessDataScoreTaskList     = "business-data-score-task-list"

	// Prepend for datadog metrics. It is important to limit the # of metrics we use since we are charged on a per
	//   metric basis
	TurboListerAPIMetricPrepend       = "turbo-lister-api"
	ActivationAPIMetricPrepend        = "activation-api"
	NeustarVerificationMetricPrepend  = "neustar-verification"
	NeustarClaimMetricPrepend         = "neustar-claim"
	DataAxleVerificationMetricPrepend = "dataaxle-verification"
	NeustarDeprovisionMetric          = "neustar-deprovision"
	DataAxleActivationMetric          = "dataaxle-activation"
	NeustarActivationMetric           = "neustar-activation"
	FourSquareWeeklySyndication       = "foursquare-weekly-syndication"
	AddressValidationMetricPrepend    = "address-validation"
	GBPValidationErrorMetricPrepend   = "gmb-validation-error"
	UberallDeprovisionMetric          = "uberall-deprovision"
	YextDeprovisionMetric             = "yext-deprovision"
	BingDeprovisionMetric             = "bing-deprovision"
	AppleDeprovisionMetric            = "apple-deprovision"
	CategoryServiceMetric             = "category-service"
	DataForSEOTaskPostMetric          = "dataforseo-task-post"
	DataForSEOTaskGetMetric           = "dataforseo-task-get"
	DataForSEOCostMetric              = "dataforseo-task-cost"
	DataForSEORateLimitErrorMetric    = "dataforseo-rate-limit-error"
	DataForSEOUnexpectedErrorMetric   = "dataforseo-unexpected-error"
	CustomSEOWorkflowMetric           = "custom-seo-workflow"
	SEOWorkflowFailedMetric           = "seo-workflow-failed"
	SkippingSubmissionToCoreMetric    = "skipping-submission-to-core"
	SubmitListingToCoreMetric         = "submit-listing-to-core"
	LegacyAccountUpdateErrorMetric    = "legacy-account-update-error"
	LegacyAccountUpdateMetric         = "legacy-account-update"
	LegacyAccountCreateErrorMetric    = "legacy-account-create-error"
	LegacyAccountCreateMetric         = "legacy-account-create"
	SyncToSourcesErrMetric            = "sync-to-sources-err"
	BingPlacesWorkflowFailedMetric    = "bing-places-workflow-failed"

	// Duration that an activation can remain pending before it fails
	MaxPendingActivationDuration = time.Hour * 24 * 7
	MaxActivityRetries           = 5

	YearMonthDayHourMinuteSecond = "2006-01-02T15-04-05"
	YearMonthDayHourMinute       = "2006-01-02T15-04"
	YearMonthDayNoSpaces         = "********"
	HourMinuteSecond             = "15:04:05"
	HourMinute                   = "15:04"

	ListingProductsApiDemo        = "https://listing-products-api-demo.apigateway.co"
	ListingProductsApiProd        = "https://listing-products-api-prod.apigateway.co"
	ListingProfileSyncFeatureFlag = "lb_display_listing_profile_tab"

	ListingBuilderDemoAndProdFreeEditionID = ""
	ListingBuilderDemoPaidEditionID        = "EDITION-MXWLTQPN"
	ListingBuilderProdPaidEditionID        = "EDITION-CFH5CKHC"

	// ListingBuilderDemoKeywordAddonID is the keyword add on id for demo
	ListingBuilderDemoKeywordAddonID = "A-V5BVRPHMVM"
	// ListingBuilderProdKeywordAddonID is the keyword add on id for prod
	// TODO: change this once we get the addon in prod setup
	ListingBuilderProdKeywordAddonID = "A-K7DDJR48BQ"

	AppleBusinessConnectNoBusinessFoundMetric = "apple-business-connect-no-business-found-for-location"
	MigrateSyncSeoKeywordsTaskList            = "migrate-sync-seo-keywords-task-list"

	CategoryIDOther    = "other"
	CategoryIDNotFound = "not_found"
)
