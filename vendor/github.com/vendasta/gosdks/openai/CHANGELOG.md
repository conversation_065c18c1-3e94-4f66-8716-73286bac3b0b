# Change Log

This file includes all the change logs for logging's module.
All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](http://keepachangelog.com/)
and this project adheres to [Semantic Versioning](http://semver.org/).

**When increasing the version in this file, please remember to also update it in the [VERSION.md](VERSION.md)**

---

# 1.36.0

- Add support for GPT-image-1 model

# 1.35.3

- Remove duplicated openai name from datadog stats for embeddings

# 1.35.2

- Update how json schema is marshalled

# 1.35.1

- Add model tag and stats for embeddings and images as well

# 1.35.0

- Add model tag to datadog stats

# 1.34.0

- Add support for the GPT 4.1 model family (normal, mini and nano)

# 1.33.0

- Return original error from `CreateChatCompletion` call if it fails

# 1.32.0

- Return token usage information from `CreateChatCompletion`

# 1.31.1

- Fix for initializing client options and setting defaults

# 1.31.0

- Add support for JSON Schema response format with `WithResponseFormatJSONSchema` option

## 1.30.0

- Add `WithProjectID` option to allow for the project ID to be set

## 1.29.0

- Add `SchemaParameters` to `FunctionDefinition`

## 1.28.0

- Add support for GPT-4o-mini

## 1.27.2

- Fix `parallelToolCalls` to not be included by default

## 1.27.1

- Fix `parallelToolCalls` default on functionality

## 1.27.0

- Add `WithParallelToolCalls` option to `CreateChatCompletion`

## 1.26.1

- FIX when function is strict, also marshal its object params as strict
- FIX token counting for object params, arrays, and pointers

## 1.26.0

- Add `Strict` to `FunctionDefinition`

## 1.25.1

- Add error logging to `CreateChatCompletion` requests

## 1.25.0

- Add `WithAttributionID` option to allow attribution of usage when calling on behalf of another application

## 1.24.0

- Add support to images in `ChatCompletionMessageRequest`

## 1.23.0

- Add JSON repair step on `getToolCallsFromChatCompletionMessage`'
- Retry on failures up to 3 times on `CreateChatCompletion`
- Add `WithMaxRetries` option to `CreateChatCompletion` to allow for the number of retries to be set

## 1.22.2

- Fix module replace

## 1.22.1

- Force update tiktoken-go-loader library to get latest files

## 1.22.0

- Update tiktoken-go version to support GPT 4o

## 1.21.2

- Add logging to capture why `CreateChatCompletion` may not generate any choices

## 1.21.1

- Adds some extra logging to capture why `getToolCallsFromChatCompletionMessage` fails

## 1.21.0

- Update latest version (`v1.24.0`) of go-openai library to support the latest models
- Refactor code to tackle breakingchanges from the go-openai library

## 1.20.0

- Modifies `GetNumberOfTokens` so that it also counts tokens for tools
- Adds `ToolDefinition` type so that the OpenAI client type doesn't need to be used by consumers

## 1.19.0

- Add support for `gpt-4o` model: `WithModel(GPT4Omni)`
  - Cheaper than GPT-4 and faster with equal performance

## 1.18.0

- Add `WithToolChoice` option to `CreateChatCompletion` to allow for the tool choice to be set
-

## 1.17.0

- Add `WithSecretKey` options to allow the Secret Manager to use a specific secret key and version
- Made the StatsD client to have tags based on the secret key to allow better management.

## 1.16.2

- Add `WithResponseFormat` option to `CreateChatCompletion` to allow for the response format to be set to `json_object` or `plain_text` (Default)

## 1.16.1

- Update trimming functionality to correctly utilize the `MaxTokens` option and subtract it from the model's token limit when determining the input limit of the request.

## 1.16.0

- Update the implementation of `GetNumberOfTokens` to count number of tokens in role fields
  - Change the library from `github.com/samber/go-gpt-3-encoder` to the more correct `github.com/pkoukk/tiktoken-go` which is a port of the official Python library
  - `GetNumberOfTokens` supports the `WithModel` option, otherwise it defaults to `gpt-3.5-turbo` like the rest of the library
- Add `WithTokenTrimming` option for `CreateChatCompletion`
  - This can be used to create a sliding window of messages staying under the token limit
  - The token limit is the lesser of the `MaxTokens` option and the `MaxTokens` of the model

## 1.15.0

- Support slices in functions.

## 1.14.0

- Allow functions to be set for chat completion via the sdk options

## 1.13.0

- dalle-3 request

## 1.12.0

- Metrics are ticked using only an `openai` prefix on the metric name.
- Previously metrics were namespaced like `{service_name}.openai.{metric_name}` which made it difficult to analyze usage across the org.
- You can still look at usage for a specific service because the `namespace` tag will be added by our datadog agents

## 1.11.0

- Add support for AdaSimilarity embedding model

## 1.10.0

- Add `CreateEmbeddings` to the client interface
- Bump `go-openai` to v1.16.0

## 1.9.0

- Add support for Embeddings

## 1.8.3

- Removing gpt4-32k model as it is not currently available from open ai

## 1.8.2

- Fix Model Validation

## 1.8.1

- Update the gpt client to use the latest version of the go-gpt3 library

## 1.8.0

- Add support for GPT4-32k model
- Update Token Max validators

## 1.7.0

- Add `GetNumberOfTokens` to the client interface

## 1.6.0

- Allow the model to be set via the sdk options

## 1.5.0

- Add Images Variations

## 1.4.3

- Revert to using the 3.5 turbo model

## 1.4.2

- Add latency tracking to openai

## 1.4.1

- Max tokens should be less than 8193

## 1.4.0

- Update `CreateChatCompletion` to use the GPT-4 model
- Increase the number of tokens used by default
- Remove the `CreateCompletion` interface as it has been replaced by `CreateChatCompletion`

### 1.3.0

- Trimspace of completions api response

### 1.2.5

- Fix response of the create images container

### 1.2.4

- Add Role type to make it easier to compose a chat completions request

## 1.2.3

- The latest models are not available for the completions api
- To use the new model one would need to use the chat completions api

## 1.2.2

- Compile time check that implementation of the interface is correct

## 1.2.1

- Update the interface of create images

## 1.2.0

- Update go-gpt3 to latest version
- Implement chat completion api

## 1.1.1

- Adding Image interface

## 1.1.0

- Get generated images from DALLE

## 1.0.2

- Allow temperature to be changed via provided sdk options

## 1.0.1

- Update datadog metrics

## 1.0.0

- Release open ai sdk to expose the completions api
- Control access to the API via secret manager
