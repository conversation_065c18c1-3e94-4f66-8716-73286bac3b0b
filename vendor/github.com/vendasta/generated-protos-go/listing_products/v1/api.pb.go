// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.30.0
// source: listing_products/v1/api.proto

package listing_products_v1

import (
	_ "github.com/vendasta/generated-protos-go/vendasta_types"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ManuallyFixYextProvisioningRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// business ID (aka account group ID)
	BusinessId    string `protobuf:"bytes,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	AddonId       string `protobuf:"bytes,2,opt,name=addon_id,json=addonId,proto3" json:"addon_id,omitempty"`
	ActivationId  string `protobuf:"bytes,3,opt,name=activation_id,json=activationId,proto3" json:"activation_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ManuallyFixYextProvisioningRequest) Reset() {
	*x = ManuallyFixYextProvisioningRequest{}
	mi := &file_listing_products_v1_api_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ManuallyFixYextProvisioningRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ManuallyFixYextProvisioningRequest) ProtoMessage() {}

func (x *ManuallyFixYextProvisioningRequest) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_api_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ManuallyFixYextProvisioningRequest.ProtoReflect.Descriptor instead.
func (*ManuallyFixYextProvisioningRequest) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_api_proto_rawDescGZIP(), []int{0}
}

func (x *ManuallyFixYextProvisioningRequest) GetBusinessId() string {
	if x != nil {
		return x.BusinessId
	}
	return ""
}

func (x *ManuallyFixYextProvisioningRequest) GetAddonId() string {
	if x != nil {
		return x.AddonId
	}
	return ""
}

func (x *ManuallyFixYextProvisioningRequest) GetActivationId() string {
	if x != nil {
		return x.ActivationId
	}
	return ""
}

type ManuallyDeactivateUberallProvisioningRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// business ID (aka account group ID)
	BusinessId    string `protobuf:"bytes,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ManuallyDeactivateUberallProvisioningRequest) Reset() {
	*x = ManuallyDeactivateUberallProvisioningRequest{}
	mi := &file_listing_products_v1_api_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ManuallyDeactivateUberallProvisioningRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ManuallyDeactivateUberallProvisioningRequest) ProtoMessage() {}

func (x *ManuallyDeactivateUberallProvisioningRequest) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_api_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ManuallyDeactivateUberallProvisioningRequest.ProtoReflect.Descriptor instead.
func (*ManuallyDeactivateUberallProvisioningRequest) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_api_proto_rawDescGZIP(), []int{1}
}

func (x *ManuallyDeactivateUberallProvisioningRequest) GetBusinessId() string {
	if x != nil {
		return x.BusinessId
	}
	return ""
}

type ManuallyProvisionUberallRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// business ID (aka account group ID)
	BusinessId    string `protobuf:"bytes,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	AddonId       string `protobuf:"bytes,2,opt,name=addon_id,json=addonId,proto3" json:"addon_id,omitempty"`
	ActivationId  string `protobuf:"bytes,3,opt,name=activation_id,json=activationId,proto3" json:"activation_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ManuallyProvisionUberallRequest) Reset() {
	*x = ManuallyProvisionUberallRequest{}
	mi := &file_listing_products_v1_api_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ManuallyProvisionUberallRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ManuallyProvisionUberallRequest) ProtoMessage() {}

func (x *ManuallyProvisionUberallRequest) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_api_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ManuallyProvisionUberallRequest.ProtoReflect.Descriptor instead.
func (*ManuallyProvisionUberallRequest) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_api_proto_rawDescGZIP(), []int{2}
}

func (x *ManuallyProvisionUberallRequest) GetBusinessId() string {
	if x != nil {
		return x.BusinessId
	}
	return ""
}

func (x *ManuallyProvisionUberallRequest) GetAddonId() string {
	if x != nil {
		return x.AddonId
	}
	return ""
}

func (x *ManuallyProvisionUberallRequest) GetActivationId() string {
	if x != nil {
		return x.ActivationId
	}
	return ""
}

type GetGoogleBusinessInfoRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// business ID (aka account group ID)
	BusinessId    string `protobuf:"bytes,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetGoogleBusinessInfoRequest) Reset() {
	*x = GetGoogleBusinessInfoRequest{}
	mi := &file_listing_products_v1_api_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetGoogleBusinessInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGoogleBusinessInfoRequest) ProtoMessage() {}

func (x *GetGoogleBusinessInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_api_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGoogleBusinessInfoRequest.ProtoReflect.Descriptor instead.
func (*GetGoogleBusinessInfoRequest) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_api_proto_rawDescGZIP(), []int{3}
}

func (x *GetGoogleBusinessInfoRequest) GetBusinessId() string {
	if x != nil {
		return x.BusinessId
	}
	return ""
}

type GetGoogleBusinessInfoResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Dump the json response we get from Google into this field. It will contain all the data we request from Google.
	TextDump      string `protobuf:"bytes,1,opt,name=text_dump,json=textDump,proto3" json:"text_dump,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetGoogleBusinessInfoResponse) Reset() {
	*x = GetGoogleBusinessInfoResponse{}
	mi := &file_listing_products_v1_api_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetGoogleBusinessInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGoogleBusinessInfoResponse) ProtoMessage() {}

func (x *GetGoogleBusinessInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_api_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGoogleBusinessInfoResponse.ProtoReflect.Descriptor instead.
func (*GetGoogleBusinessInfoResponse) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_api_proto_rawDescGZIP(), []int{4}
}

func (x *GetGoogleBusinessInfoResponse) GetTextDump() string {
	if x != nil {
		return x.TextDump
	}
	return ""
}

type GetFacebookPageInfoRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// business ID (aka account group ID)
	BusinessId    string `protobuf:"bytes,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetFacebookPageInfoRequest) Reset() {
	*x = GetFacebookPageInfoRequest{}
	mi := &file_listing_products_v1_api_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetFacebookPageInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFacebookPageInfoRequest) ProtoMessage() {}

func (x *GetFacebookPageInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_api_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFacebookPageInfoRequest.ProtoReflect.Descriptor instead.
func (*GetFacebookPageInfoRequest) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_api_proto_rawDescGZIP(), []int{5}
}

func (x *GetFacebookPageInfoRequest) GetBusinessId() string {
	if x != nil {
		return x.BusinessId
	}
	return ""
}

type GetFacebookPageInfoResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Dump the json response we get from Facebook into this field. It will contain all the data we request from Facebook.
	TextDump      string `protobuf:"bytes,1,opt,name=text_dump,json=textDump,proto3" json:"text_dump,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetFacebookPageInfoResponse) Reset() {
	*x = GetFacebookPageInfoResponse{}
	mi := &file_listing_products_v1_api_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetFacebookPageInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFacebookPageInfoResponse) ProtoMessage() {}

func (x *GetFacebookPageInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_api_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFacebookPageInfoResponse.ProtoReflect.Descriptor instead.
func (*GetFacebookPageInfoResponse) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_api_proto_rawDescGZIP(), []int{6}
}

func (x *GetFacebookPageInfoResponse) GetTextDump() string {
	if x != nil {
		return x.TextDump
	}
	return ""
}

type GetAppleBusinessConnectInfoRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// business ID
	BusinessId    string `protobuf:"bytes,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAppleBusinessConnectInfoRequest) Reset() {
	*x = GetAppleBusinessConnectInfoRequest{}
	mi := &file_listing_products_v1_api_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAppleBusinessConnectInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAppleBusinessConnectInfoRequest) ProtoMessage() {}

func (x *GetAppleBusinessConnectInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_api_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAppleBusinessConnectInfoRequest.ProtoReflect.Descriptor instead.
func (*GetAppleBusinessConnectInfoRequest) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_api_proto_rawDescGZIP(), []int{7}
}

func (x *GetAppleBusinessConnectInfoRequest) GetBusinessId() string {
	if x != nil {
		return x.BusinessId
	}
	return ""
}

type GetAppleBusinessConnectInfoResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Dump the json response we get from Apple into this field. It will contain all the data we request from Apple.
	TextDump      string `protobuf:"bytes,1,opt,name=text_dump,json=textDump,proto3" json:"text_dump,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAppleBusinessConnectInfoResponse) Reset() {
	*x = GetAppleBusinessConnectInfoResponse{}
	mi := &file_listing_products_v1_api_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAppleBusinessConnectInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAppleBusinessConnectInfoResponse) ProtoMessage() {}

func (x *GetAppleBusinessConnectInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_api_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAppleBusinessConnectInfoResponse.ProtoReflect.Descriptor instead.
func (*GetAppleBusinessConnectInfoResponse) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_api_proto_rawDescGZIP(), []int{8}
}

func (x *GetAppleBusinessConnectInfoResponse) GetTextDump() string {
	if x != nil {
		return x.TextDump
	}
	return ""
}

type GetBingPlacesInfoRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// business ID
	BusinessId    string `protobuf:"bytes,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetBingPlacesInfoRequest) Reset() {
	*x = GetBingPlacesInfoRequest{}
	mi := &file_listing_products_v1_api_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetBingPlacesInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBingPlacesInfoRequest) ProtoMessage() {}

func (x *GetBingPlacesInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_api_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBingPlacesInfoRequest.ProtoReflect.Descriptor instead.
func (*GetBingPlacesInfoRequest) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_api_proto_rawDescGZIP(), []int{9}
}

func (x *GetBingPlacesInfoRequest) GetBusinessId() string {
	if x != nil {
		return x.BusinessId
	}
	return ""
}

type GetBingPlacesInfoResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Dump the json response we get from Bing into this field. It will contain all the data we request from Bing.
	TextDump      string `protobuf:"bytes,1,opt,name=text_dump,json=textDump,proto3" json:"text_dump,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetBingPlacesInfoResponse) Reset() {
	*x = GetBingPlacesInfoResponse{}
	mi := &file_listing_products_v1_api_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetBingPlacesInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBingPlacesInfoResponse) ProtoMessage() {}

func (x *GetBingPlacesInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_api_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBingPlacesInfoResponse.ProtoReflect.Descriptor instead.
func (*GetBingPlacesInfoResponse) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_api_proto_rawDescGZIP(), []int{10}
}

func (x *GetBingPlacesInfoResponse) GetTextDump() string {
	if x != nil {
		return x.TextDump
	}
	return ""
}

type TriggerBingInsightsBackFillRequest struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	BusinessId          string                 `protobuf:"bytes,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	FirstSuccessfulSync bool                   `protobuf:"varint,2,opt,name=first_successful_sync,json=firstSuccessfulSync,proto3" json:"first_successful_sync,omitempty"`
	StartDate           string                 `protobuf:"bytes,3,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	EndDate             string                 `protobuf:"bytes,4,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *TriggerBingInsightsBackFillRequest) Reset() {
	*x = TriggerBingInsightsBackFillRequest{}
	mi := &file_listing_products_v1_api_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TriggerBingInsightsBackFillRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TriggerBingInsightsBackFillRequest) ProtoMessage() {}

func (x *TriggerBingInsightsBackFillRequest) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_api_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TriggerBingInsightsBackFillRequest.ProtoReflect.Descriptor instead.
func (*TriggerBingInsightsBackFillRequest) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_api_proto_rawDescGZIP(), []int{11}
}

func (x *TriggerBingInsightsBackFillRequest) GetBusinessId() string {
	if x != nil {
		return x.BusinessId
	}
	return ""
}

func (x *TriggerBingInsightsBackFillRequest) GetFirstSuccessfulSync() bool {
	if x != nil {
		return x.FirstSuccessfulSync
	}
	return false
}

func (x *TriggerBingInsightsBackFillRequest) GetStartDate() string {
	if x != nil {
		return x.StartDate
	}
	return ""
}

func (x *TriggerBingInsightsBackFillRequest) GetEndDate() string {
	if x != nil {
		return x.EndDate
	}
	return ""
}

type CheckSubmissionStatusRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BusinessId    string                 `protobuf:"bytes,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckSubmissionStatusRequest) Reset() {
	*x = CheckSubmissionStatusRequest{}
	mi := &file_listing_products_v1_api_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckSubmissionStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckSubmissionStatusRequest) ProtoMessage() {}

func (x *CheckSubmissionStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_api_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckSubmissionStatusRequest.ProtoReflect.Descriptor instead.
func (*CheckSubmissionStatusRequest) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_api_proto_rawDescGZIP(), []int{12}
}

func (x *CheckSubmissionStatusRequest) GetBusinessId() string {
	if x != nil {
		return x.BusinessId
	}
	return ""
}

type GetInfoGroupIdRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BusinessId    string                 `protobuf:"bytes,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetInfoGroupIdRequest) Reset() {
	*x = GetInfoGroupIdRequest{}
	mi := &file_listing_products_v1_api_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetInfoGroupIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInfoGroupIdRequest) ProtoMessage() {}

func (x *GetInfoGroupIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_api_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInfoGroupIdRequest.ProtoReflect.Descriptor instead.
func (*GetInfoGroupIdRequest) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_api_proto_rawDescGZIP(), []int{13}
}

func (x *GetInfoGroupIdRequest) GetBusinessId() string {
	if x != nil {
		return x.BusinessId
	}
	return ""
}

type GetInfoGroupIdResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	InfogroupId   string                 `protobuf:"bytes,1,opt,name=infogroup_id,json=infogroupId,proto3" json:"infogroup_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetInfoGroupIdResponse) Reset() {
	*x = GetInfoGroupIdResponse{}
	mi := &file_listing_products_v1_api_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetInfoGroupIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInfoGroupIdResponse) ProtoMessage() {}

func (x *GetInfoGroupIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_api_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInfoGroupIdResponse.ProtoReflect.Descriptor instead.
func (*GetInfoGroupIdResponse) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_api_proto_rawDescGZIP(), []int{14}
}

func (x *GetInfoGroupIdResponse) GetInfogroupId() string {
	if x != nil {
		return x.InfogroupId
	}
	return ""
}

type GetListingDistributionActivationStatusRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	AccountGroupId string                 `protobuf:"bytes,1,opt,name=account_group_id,json=accountGroupId,proto3" json:"account_group_id,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *GetListingDistributionActivationStatusRequest) Reset() {
	*x = GetListingDistributionActivationStatusRequest{}
	mi := &file_listing_products_v1_api_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetListingDistributionActivationStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetListingDistributionActivationStatusRequest) ProtoMessage() {}

func (x *GetListingDistributionActivationStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_api_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetListingDistributionActivationStatusRequest.ProtoReflect.Descriptor instead.
func (*GetListingDistributionActivationStatusRequest) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_api_proto_rawDescGZIP(), []int{15}
}

func (x *GetListingDistributionActivationStatusRequest) GetAccountGroupId() string {
	if x != nil {
		return x.AccountGroupId
	}
	return ""
}

type GetListingDistributionActivationStatusResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The account to retrieve LD activation status for
	AccountGroupId string `protobuf:"bytes,1,opt,name=account_group_id,json=accountGroupId,proto3" json:"account_group_id,omitempty"`
	// Whether LD is active or not
	IsActive bool `protobuf:"varint,2,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	// The LD addon that is currently active for the account
	AddonId string `protobuf:"bytes,3,opt,name=addon_id,json=addonId,proto3" json:"addon_id,omitempty"`
	// The date LD was activated
	Activation    *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=activation,proto3" json:"activation,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetListingDistributionActivationStatusResponse) Reset() {
	*x = GetListingDistributionActivationStatusResponse{}
	mi := &file_listing_products_v1_api_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetListingDistributionActivationStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetListingDistributionActivationStatusResponse) ProtoMessage() {}

func (x *GetListingDistributionActivationStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_api_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetListingDistributionActivationStatusResponse.ProtoReflect.Descriptor instead.
func (*GetListingDistributionActivationStatusResponse) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_api_proto_rawDescGZIP(), []int{16}
}

func (x *GetListingDistributionActivationStatusResponse) GetAccountGroupId() string {
	if x != nil {
		return x.AccountGroupId
	}
	return ""
}

func (x *GetListingDistributionActivationStatusResponse) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *GetListingDistributionActivationStatusResponse) GetAddonId() string {
	if x != nil {
		return x.AddonId
	}
	return ""
}

func (x *GetListingDistributionActivationStatusResponse) GetActivation() *timestamppb.Timestamp {
	if x != nil {
		return x.Activation
	}
	return nil
}

type BulkConnectLocationsForGoogleUserRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// In order for this endpoint to work the Google user must already have been used to connect an account in our system. This AG ID is then
	// used to get the google user ID and google user from the social profile in order to copy that to the other businesses, and then connect
	// a location to those businesses.
	BusinessId    []string `protobuf:"bytes,1,rep,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BulkConnectLocationsForGoogleUserRequest) Reset() {
	*x = BulkConnectLocationsForGoogleUserRequest{}
	mi := &file_listing_products_v1_api_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BulkConnectLocationsForGoogleUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BulkConnectLocationsForGoogleUserRequest) ProtoMessage() {}

func (x *BulkConnectLocationsForGoogleUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_api_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BulkConnectLocationsForGoogleUserRequest.ProtoReflect.Descriptor instead.
func (*BulkConnectLocationsForGoogleUserRequest) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_api_proto_rawDescGZIP(), []int{17}
}

func (x *BulkConnectLocationsForGoogleUserRequest) GetBusinessId() []string {
	if x != nil {
		return x.BusinessId
	}
	return nil
}

type BulkConnectLocationsForGoogleUserResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// connections is a map of the Google Business Profile locations that were connected, to the account group they were connected to.
	Connections map[string]string `protobuf:"bytes,1,rep,name=connections,proto3" json:"connections,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	// locations_not_connected is a list of the GBP locations on the input Google user that were not connected to a social profile.
	LocationsNotConnected string `protobuf:"bytes,2,opt,name=locations_not_connected,json=locationsNotConnected,proto3" json:"locations_not_connected,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *BulkConnectLocationsForGoogleUserResponse) Reset() {
	*x = BulkConnectLocationsForGoogleUserResponse{}
	mi := &file_listing_products_v1_api_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BulkConnectLocationsForGoogleUserResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BulkConnectLocationsForGoogleUserResponse) ProtoMessage() {}

func (x *BulkConnectLocationsForGoogleUserResponse) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_api_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BulkConnectLocationsForGoogleUserResponse.ProtoReflect.Descriptor instead.
func (*BulkConnectLocationsForGoogleUserResponse) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_api_proto_rawDescGZIP(), []int{18}
}

func (x *BulkConnectLocationsForGoogleUserResponse) GetConnections() map[string]string {
	if x != nil {
		return x.Connections
	}
	return nil
}

func (x *BulkConnectLocationsForGoogleUserResponse) GetLocationsNotConnected() string {
	if x != nil {
		return x.LocationsNotConnected
	}
	return ""
}

type GetProductSettingsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PartnerId     string                 `protobuf:"bytes,1,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	MarketId      string                 `protobuf:"bytes,2,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetProductSettingsRequest) Reset() {
	*x = GetProductSettingsRequest{}
	mi := &file_listing_products_v1_api_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetProductSettingsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetProductSettingsRequest) ProtoMessage() {}

func (x *GetProductSettingsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_api_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetProductSettingsRequest.ProtoReflect.Descriptor instead.
func (*GetProductSettingsRequest) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_api_proto_rawDescGZIP(), []int{19}
}

func (x *GetProductSettingsRequest) GetPartnerId() string {
	if x != nil {
		return x.PartnerId
	}
	return ""
}

func (x *GetProductSettingsRequest) GetMarketId() string {
	if x != nil {
		return x.MarketId
	}
	return ""
}

type GetProductSettingsResponse struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	PartnerId       string                 `protobuf:"bytes,1,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	MarketId        string                 `protobuf:"bytes,2,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	CanEditKeywords bool                   `protobuf:"varint,3,opt,name=can_edit_keywords,json=canEditKeywords,proto3" json:"can_edit_keywords,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *GetProductSettingsResponse) Reset() {
	*x = GetProductSettingsResponse{}
	mi := &file_listing_products_v1_api_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetProductSettingsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetProductSettingsResponse) ProtoMessage() {}

func (x *GetProductSettingsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_api_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetProductSettingsResponse.ProtoReflect.Descriptor instead.
func (*GetProductSettingsResponse) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_api_proto_rawDescGZIP(), []int{20}
}

func (x *GetProductSettingsResponse) GetPartnerId() string {
	if x != nil {
		return x.PartnerId
	}
	return ""
}

func (x *GetProductSettingsResponse) GetMarketId() string {
	if x != nil {
		return x.MarketId
	}
	return ""
}

func (x *GetProductSettingsResponse) GetCanEditKeywords() bool {
	if x != nil {
		return x.CanEditKeywords
	}
	return false
}

type GetDirectSyncSourceInfoRequest struct {
	state      protoimpl.MessageState `protogen:"open.v1"`
	BusinessId string                 `protobuf:"bytes,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// if set to true, connected account credentials will be refreshed
	Refresh       bool `protobuf:"varint,2,opt,name=refresh,proto3" json:"refresh,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetDirectSyncSourceInfoRequest) Reset() {
	*x = GetDirectSyncSourceInfoRequest{}
	mi := &file_listing_products_v1_api_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDirectSyncSourceInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDirectSyncSourceInfoRequest) ProtoMessage() {}

func (x *GetDirectSyncSourceInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_api_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDirectSyncSourceInfoRequest.ProtoReflect.Descriptor instead.
func (*GetDirectSyncSourceInfoRequest) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_api_proto_rawDescGZIP(), []int{21}
}

func (x *GetDirectSyncSourceInfoRequest) GetBusinessId() string {
	if x != nil {
		return x.BusinessId
	}
	return ""
}

func (x *GetDirectSyncSourceInfoRequest) GetRefresh() bool {
	if x != nil {
		return x.Refresh
	}
	return false
}

type DirectSyncSource struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// numeric ID of the source
	SourceId int64 `protobuf:"varint,1,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	// The name of the source
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// The connected account information for this direct submit source
	Account     *ConnectedDirectSyncAccount `protobuf:"bytes,3,opt,name=account,proto3" json:"account,omitempty"`
	ConnectLink string                      `protobuf:"bytes,4,opt,name=connect_link,json=connectLink,proto3" json:"connect_link,omitempty"`
	// The accuracy data for this direct submit source
	Accuracy *SourceAccuracy `protobuf:"bytes,5,opt,name=accuracy,proto3" json:"accuracy,omitempty"`
	// The current listing score for this direct submit source
	Score int64 `protobuf:"varint,6,opt,name=score,proto3" json:"score,omitempty"`
	// The maximum possible listing score for this direct submit source
	MaxScore int64 `protobuf:"varint,7,opt,name=max_score,json=maxScore,proto3" json:"max_score,omitempty"`
	// The URL of the listing for this direct submit source
	ListingUrl string `protobuf:"bytes,8,opt,name=listing_url,json=listingUrl,proto3" json:"listing_url,omitempty"`
	// The CS listing id of the direct submit listing
	ListingId string `protobuf:"bytes,9,opt,name=listing_id,json=listingId,proto3" json:"listing_id,omitempty"`
	// The syndication status for this direct submit source
	SyncStatus *SyndicationStatus `protobuf:"bytes,10,opt,name=sync_status,json=syncStatus,proto3" json:"sync_status,omitempty"`
	// Other accounts that are connected for this source, and could be synced to
	AdditionalAccounts []*ConnectedDirectSyncAccount `protobuf:"bytes,11,rep,name=additional_accounts,json=additionalAccounts,proto3" json:"additional_accounts,omitempty"`
	// Whether syncing to this source is enabled.  Usually this will correspond to the account's is_syncing_enabled flag,
	// but for sources without accounts, it will be populated by the models where we store direct sync setting for the business.
	IsSyncingEnabled bool `protobuf:"varint,12,opt,name=is_syncing_enabled,json=isSyncingEnabled,proto3" json:"is_syncing_enabled,omitempty"`
	// Whether the listing has been verified as accurate by the business
	IsListingVerified bool `protobuf:"varint,13,opt,name=is_listing_verified,json=isListingVerified,proto3" json:"is_listing_verified,omitempty"`
	// Whether the source is hidden from the UI
	Hidden        bool `protobuf:"varint,14,opt,name=hidden,proto3" json:"hidden,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DirectSyncSource) Reset() {
	*x = DirectSyncSource{}
	mi := &file_listing_products_v1_api_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DirectSyncSource) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DirectSyncSource) ProtoMessage() {}

func (x *DirectSyncSource) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_api_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DirectSyncSource.ProtoReflect.Descriptor instead.
func (*DirectSyncSource) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_api_proto_rawDescGZIP(), []int{22}
}

func (x *DirectSyncSource) GetSourceId() int64 {
	if x != nil {
		return x.SourceId
	}
	return 0
}

func (x *DirectSyncSource) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DirectSyncSource) GetAccount() *ConnectedDirectSyncAccount {
	if x != nil {
		return x.Account
	}
	return nil
}

func (x *DirectSyncSource) GetConnectLink() string {
	if x != nil {
		return x.ConnectLink
	}
	return ""
}

func (x *DirectSyncSource) GetAccuracy() *SourceAccuracy {
	if x != nil {
		return x.Accuracy
	}
	return nil
}

func (x *DirectSyncSource) GetScore() int64 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *DirectSyncSource) GetMaxScore() int64 {
	if x != nil {
		return x.MaxScore
	}
	return 0
}

func (x *DirectSyncSource) GetListingUrl() string {
	if x != nil {
		return x.ListingUrl
	}
	return ""
}

func (x *DirectSyncSource) GetListingId() string {
	if x != nil {
		return x.ListingId
	}
	return ""
}

func (x *DirectSyncSource) GetSyncStatus() *SyndicationStatus {
	if x != nil {
		return x.SyncStatus
	}
	return nil
}

func (x *DirectSyncSource) GetAdditionalAccounts() []*ConnectedDirectSyncAccount {
	if x != nil {
		return x.AdditionalAccounts
	}
	return nil
}

func (x *DirectSyncSource) GetIsSyncingEnabled() bool {
	if x != nil {
		return x.IsSyncingEnabled
	}
	return false
}

func (x *DirectSyncSource) GetIsListingVerified() bool {
	if x != nil {
		return x.IsListingVerified
	}
	return false
}

func (x *DirectSyncSource) GetHidden() bool {
	if x != nil {
		return x.Hidden
	}
	return false
}

type GetDirectSyncSourceInfoResponse struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	DirectSubmitSources []*DirectSyncSource    `protobuf:"bytes,1,rep,name=direct_submit_sources,json=directSubmitSources,proto3" json:"direct_submit_sources,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *GetDirectSyncSourceInfoResponse) Reset() {
	*x = GetDirectSyncSourceInfoResponse{}
	mi := &file_listing_products_v1_api_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDirectSyncSourceInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDirectSyncSourceInfoResponse) ProtoMessage() {}

func (x *GetDirectSyncSourceInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_api_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDirectSyncSourceInfoResponse.ProtoReflect.Descriptor instead.
func (*GetDirectSyncSourceInfoResponse) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_api_proto_rawDescGZIP(), []int{23}
}

func (x *GetDirectSyncSourceInfoResponse) GetDirectSubmitSources() []*DirectSyncSource {
	if x != nil {
		return x.DirectSubmitSources
	}
	return nil
}

type StartBusinessDataScoreWorkflowRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BusinessIds   []string               `protobuf:"bytes,1,rep,name=business_ids,json=businessIds,proto3" json:"business_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StartBusinessDataScoreWorkflowRequest) Reset() {
	*x = StartBusinessDataScoreWorkflowRequest{}
	mi := &file_listing_products_v1_api_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StartBusinessDataScoreWorkflowRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartBusinessDataScoreWorkflowRequest) ProtoMessage() {}

func (x *StartBusinessDataScoreWorkflowRequest) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_api_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartBusinessDataScoreWorkflowRequest.ProtoReflect.Descriptor instead.
func (*StartBusinessDataScoreWorkflowRequest) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_api_proto_rawDescGZIP(), []int{24}
}

func (x *StartBusinessDataScoreWorkflowRequest) GetBusinessIds() []string {
	if x != nil {
		return x.BusinessIds
	}
	return nil
}

type IsPartnerUserRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PartnerId     string                 `protobuf:"bytes,1,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *IsPartnerUserRequest) Reset() {
	*x = IsPartnerUserRequest{}
	mi := &file_listing_products_v1_api_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IsPartnerUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsPartnerUserRequest) ProtoMessage() {}

func (x *IsPartnerUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_api_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsPartnerUserRequest.ProtoReflect.Descriptor instead.
func (*IsPartnerUserRequest) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_api_proto_rawDescGZIP(), []int{25}
}

func (x *IsPartnerUserRequest) GetPartnerId() string {
	if x != nil {
		return x.PartnerId
	}
	return ""
}

type IsPartnerUserResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IsPartnerUser bool                   `protobuf:"varint,1,opt,name=is_partner_user,json=isPartnerUser,proto3" json:"is_partner_user,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *IsPartnerUserResponse) Reset() {
	*x = IsPartnerUserResponse{}
	mi := &file_listing_products_v1_api_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IsPartnerUserResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsPartnerUserResponse) ProtoMessage() {}

func (x *IsPartnerUserResponse) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_api_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsPartnerUserResponse.ProtoReflect.Descriptor instead.
func (*IsPartnerUserResponse) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_api_proto_rawDescGZIP(), []int{26}
}

func (x *IsPartnerUserResponse) GetIsPartnerUser() bool {
	if x != nil {
		return x.IsPartnerUser
	}
	return false
}

type GetLDDataRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BusinessId    string                 `protobuf:"bytes,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetLDDataRequest) Reset() {
	*x = GetLDDataRequest{}
	mi := &file_listing_products_v1_api_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetLDDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLDDataRequest) ProtoMessage() {}

func (x *GetLDDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_api_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLDDataRequest.ProtoReflect.Descriptor instead.
func (*GetLDDataRequest) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_api_proto_rawDescGZIP(), []int{27}
}

func (x *GetLDDataRequest) GetBusinessId() string {
	if x != nil {
		return x.BusinessId
	}
	return ""
}

type GetLDDataResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Neustar       *DirectSyncSource      `protobuf:"bytes,1,opt,name=neustar,proto3" json:"neustar,omitempty"`
	DataAxle      *DirectSyncSource      `protobuf:"bytes,2,opt,name=data_axle,json=dataAxle,proto3" json:"data_axle,omitempty"`
	Foursquare    *DirectSyncSource      `protobuf:"bytes,3,opt,name=foursquare,proto3" json:"foursquare,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetLDDataResponse) Reset() {
	*x = GetLDDataResponse{}
	mi := &file_listing_products_v1_api_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetLDDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLDDataResponse) ProtoMessage() {}

func (x *GetLDDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_api_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLDDataResponse.ProtoReflect.Descriptor instead.
func (*GetLDDataResponse) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_api_proto_rawDescGZIP(), []int{28}
}

func (x *GetLDDataResponse) GetNeustar() *DirectSyncSource {
	if x != nil {
		return x.Neustar
	}
	return nil
}

func (x *GetLDDataResponse) GetDataAxle() *DirectSyncSource {
	if x != nil {
		return x.DataAxle
	}
	return nil
}

func (x *GetLDDataResponse) GetFoursquare() *DirectSyncSource {
	if x != nil {
		return x.Foursquare
	}
	return nil
}

type SyncToSourcesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BusinessId    string                 `protobuf:"bytes,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	Trigger       string                 `protobuf:"bytes,2,opt,name=trigger,proto3" json:"trigger,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SyncToSourcesRequest) Reset() {
	*x = SyncToSourcesRequest{}
	mi := &file_listing_products_v1_api_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SyncToSourcesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncToSourcesRequest) ProtoMessage() {}

func (x *SyncToSourcesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_api_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncToSourcesRequest.ProtoReflect.Descriptor instead.
func (*SyncToSourcesRequest) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_api_proto_rawDescGZIP(), []int{29}
}

func (x *SyncToSourcesRequest) GetBusinessId() string {
	if x != nil {
		return x.BusinessId
	}
	return ""
}

func (x *SyncToSourcesRequest) GetTrigger() string {
	if x != nil {
		return x.Trigger
	}
	return ""
}

type GetNeustarPublicationsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BusinessId    string                 `protobuf:"bytes,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetNeustarPublicationsRequest) Reset() {
	*x = GetNeustarPublicationsRequest{}
	mi := &file_listing_products_v1_api_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetNeustarPublicationsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNeustarPublicationsRequest) ProtoMessage() {}

func (x *GetNeustarPublicationsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_api_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNeustarPublicationsRequest.ProtoReflect.Descriptor instead.
func (*GetNeustarPublicationsRequest) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_api_proto_rawDescGZIP(), []int{30}
}

func (x *GetNeustarPublicationsRequest) GetBusinessId() string {
	if x != nil {
		return x.BusinessId
	}
	return ""
}

type NeustarPublication struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	Id               int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	ParentId         int64                  `protobuf:"varint,2,opt,name=parent_id,json=parentId,proto3" json:"parent_id,omitempty"`
	Platform         string                 `protobuf:"bytes,3,opt,name=platform,proto3" json:"platform,omitempty"`
	Description      string                 `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	Message          string                 `protobuf:"bytes,5,opt,name=message,proto3" json:"message,omitempty"`
	LastDeliveryTime *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=last_delivery_time,json=lastDeliveryTime,proto3" json:"last_delivery_time,omitempty"`
	LastModified     *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=last_modified,json=lastModified,proto3" json:"last_modified,omitempty"`
	Disposition      string                 `protobuf:"bytes,8,opt,name=disposition,proto3" json:"disposition,omitempty"`
	Link             string                 `protobuf:"bytes,9,opt,name=link,proto3" json:"link,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *NeustarPublication) Reset() {
	*x = NeustarPublication{}
	mi := &file_listing_products_v1_api_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NeustarPublication) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NeustarPublication) ProtoMessage() {}

func (x *NeustarPublication) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_api_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NeustarPublication.ProtoReflect.Descriptor instead.
func (*NeustarPublication) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_api_proto_rawDescGZIP(), []int{31}
}

func (x *NeustarPublication) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *NeustarPublication) GetParentId() int64 {
	if x != nil {
		return x.ParentId
	}
	return 0
}

func (x *NeustarPublication) GetPlatform() string {
	if x != nil {
		return x.Platform
	}
	return ""
}

func (x *NeustarPublication) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *NeustarPublication) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *NeustarPublication) GetLastDeliveryTime() *timestamppb.Timestamp {
	if x != nil {
		return x.LastDeliveryTime
	}
	return nil
}

func (x *NeustarPublication) GetLastModified() *timestamppb.Timestamp {
	if x != nil {
		return x.LastModified
	}
	return nil
}

func (x *NeustarPublication) GetDisposition() string {
	if x != nil {
		return x.Disposition
	}
	return ""
}

func (x *NeustarPublication) GetLink() string {
	if x != nil {
		return x.Link
	}
	return ""
}

type GetNeustarPublicationsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Publications  []*NeustarPublication  `protobuf:"bytes,1,rep,name=publications,proto3" json:"publications,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetNeustarPublicationsResponse) Reset() {
	*x = GetNeustarPublicationsResponse{}
	mi := &file_listing_products_v1_api_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetNeustarPublicationsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNeustarPublicationsResponse) ProtoMessage() {}

func (x *GetNeustarPublicationsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_api_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNeustarPublicationsResponse.ProtoReflect.Descriptor instead.
func (*GetNeustarPublicationsResponse) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_api_proto_rawDescGZIP(), []int{32}
}

func (x *GetNeustarPublicationsResponse) GetPublications() []*NeustarPublication {
	if x != nil {
		return x.Publications
	}
	return nil
}

type GetDoctorDotComCategoriesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetDoctorDotComCategoriesRequest) Reset() {
	*x = GetDoctorDotComCategoriesRequest{}
	mi := &file_listing_products_v1_api_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDoctorDotComCategoriesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDoctorDotComCategoriesRequest) ProtoMessage() {}

func (x *GetDoctorDotComCategoriesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_api_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDoctorDotComCategoriesRequest.ProtoReflect.Descriptor instead.
func (*GetDoctorDotComCategoriesRequest) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_api_proto_rawDescGZIP(), []int{33}
}

type GetDoctorDotComCategoriesResponse struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Categories    []*DoctorDotComCategory `protobuf:"bytes,1,rep,name=categories,proto3" json:"categories,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetDoctorDotComCategoriesResponse) Reset() {
	*x = GetDoctorDotComCategoriesResponse{}
	mi := &file_listing_products_v1_api_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDoctorDotComCategoriesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDoctorDotComCategoriesResponse) ProtoMessage() {}

func (x *GetDoctorDotComCategoriesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_api_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDoctorDotComCategoriesResponse.ProtoReflect.Descriptor instead.
func (*GetDoctorDotComCategoriesResponse) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_api_proto_rawDescGZIP(), []int{34}
}

func (x *GetDoctorDotComCategoriesResponse) GetCategories() []*DoctorDotComCategory {
	if x != nil {
		return x.Categories
	}
	return nil
}

type GetBusinessProfileFieldStatusRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The business id
	BusinessId    string `protobuf:"bytes,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetBusinessProfileFieldStatusRequest) Reset() {
	*x = GetBusinessProfileFieldStatusRequest{}
	mi := &file_listing_products_v1_api_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetBusinessProfileFieldStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBusinessProfileFieldStatusRequest) ProtoMessage() {}

func (x *GetBusinessProfileFieldStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_api_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBusinessProfileFieldStatusRequest.ProtoReflect.Descriptor instead.
func (*GetBusinessProfileFieldStatusRequest) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_api_proto_rawDescGZIP(), []int{35}
}

func (x *GetBusinessProfileFieldStatusRequest) GetBusinessId() string {
	if x != nil {
		return x.BusinessId
	}
	return ""
}

type FieldStatus struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UpdateOrigin  string                 `protobuf:"bytes,1,opt,name=update_origin,json=updateOrigin,proto3" json:"update_origin,omitempty"`
	Updated       *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=updated,proto3" json:"updated,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FieldStatus) Reset() {
	*x = FieldStatus{}
	mi := &file_listing_products_v1_api_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FieldStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FieldStatus) ProtoMessage() {}

func (x *FieldStatus) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_api_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FieldStatus.ProtoReflect.Descriptor instead.
func (*FieldStatus) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_api_proto_rawDescGZIP(), []int{36}
}

func (x *FieldStatus) GetUpdateOrigin() string {
	if x != nil {
		return x.UpdateOrigin
	}
	return ""
}

func (x *FieldStatus) GetUpdated() *timestamppb.Timestamp {
	if x != nil {
		return x.Updated
	}
	return nil
}

type GetBusinessProfileFieldStatusResponse struct {
	state                   protoimpl.MessageState `protogen:"open.v1"`
	BusinessId              string                 `protobuf:"bytes,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	CompanyName             *FieldStatus           `protobuf:"bytes,2,opt,name=company_name,json=companyName,proto3" json:"company_name,omitempty"`
	Address                 *FieldStatus           `protobuf:"bytes,3,opt,name=address,proto3" json:"address,omitempty"`
	Address2                *FieldStatus           `protobuf:"bytes,4,opt,name=address2,proto3" json:"address2,omitempty"`
	City                    *FieldStatus           `protobuf:"bytes,5,opt,name=city,proto3" json:"city,omitempty"`
	State                   *FieldStatus           `protobuf:"bytes,6,opt,name=state,proto3" json:"state,omitempty"`
	Zip                     *FieldStatus           `protobuf:"bytes,7,opt,name=zip,proto3" json:"zip,omitempty"`
	Country                 *FieldStatus           `protobuf:"bytes,8,opt,name=country,proto3" json:"country,omitempty"`
	WorkNumber              *FieldStatus           `protobuf:"bytes,9,opt,name=work_number,json=workNumber,proto3" json:"work_number,omitempty"`
	CallTrackingNumber      *FieldStatus           `protobuf:"bytes,10,opt,name=call_tracking_number,json=callTrackingNumber,proto3" json:"call_tracking_number,omitempty"`
	Website                 *FieldStatus           `protobuf:"bytes,11,opt,name=website,proto3" json:"website,omitempty"`
	Location                *FieldStatus           `protobuf:"bytes,12,opt,name=location,proto3" json:"location,omitempty"`
	SalesPersonId           *FieldStatus           `protobuf:"bytes,13,opt,name=sales_person_id,json=salesPersonId,proto3" json:"sales_person_id,omitempty"`
	AdditionalSalesPersonId *FieldStatus           `protobuf:"bytes,14,opt,name=additional_sales_person_id,json=additionalSalesPersonId,proto3" json:"additional_sales_person_id,omitempty"`
	VcategoryIds            *FieldStatus           `protobuf:"bytes,15,opt,name=vcategory_ids,json=vcategoryIds,proto3" json:"vcategory_ids,omitempty"`
	CustomerIdentifier      *FieldStatus           `protobuf:"bytes,16,opt,name=customer_identifier,json=customerIdentifier,proto3" json:"customer_identifier,omitempty"`
	LifecycleStage          *FieldStatus           `protobuf:"bytes,17,opt,name=lifecycle_stage,json=lifecycleStage,proto3" json:"lifecycle_stage,omitempty"`
	Created                 *timestamppb.Timestamp `protobuf:"bytes,18,opt,name=created,proto3" json:"created,omitempty"`
	Updated                 *timestamppb.Timestamp `protobuf:"bytes,19,opt,name=updated,proto3" json:"updated,omitempty"`
	Deleted                 *timestamppb.Timestamp `protobuf:"bytes,20,opt,name=deleted,proto3" json:"deleted,omitempty"`
	unknownFields           protoimpl.UnknownFields
	sizeCache               protoimpl.SizeCache
}

func (x *GetBusinessProfileFieldStatusResponse) Reset() {
	*x = GetBusinessProfileFieldStatusResponse{}
	mi := &file_listing_products_v1_api_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetBusinessProfileFieldStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBusinessProfileFieldStatusResponse) ProtoMessage() {}

func (x *GetBusinessProfileFieldStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_api_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBusinessProfileFieldStatusResponse.ProtoReflect.Descriptor instead.
func (*GetBusinessProfileFieldStatusResponse) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_api_proto_rawDescGZIP(), []int{37}
}

func (x *GetBusinessProfileFieldStatusResponse) GetBusinessId() string {
	if x != nil {
		return x.BusinessId
	}
	return ""
}

func (x *GetBusinessProfileFieldStatusResponse) GetCompanyName() *FieldStatus {
	if x != nil {
		return x.CompanyName
	}
	return nil
}

func (x *GetBusinessProfileFieldStatusResponse) GetAddress() *FieldStatus {
	if x != nil {
		return x.Address
	}
	return nil
}

func (x *GetBusinessProfileFieldStatusResponse) GetAddress2() *FieldStatus {
	if x != nil {
		return x.Address2
	}
	return nil
}

func (x *GetBusinessProfileFieldStatusResponse) GetCity() *FieldStatus {
	if x != nil {
		return x.City
	}
	return nil
}

func (x *GetBusinessProfileFieldStatusResponse) GetState() *FieldStatus {
	if x != nil {
		return x.State
	}
	return nil
}

func (x *GetBusinessProfileFieldStatusResponse) GetZip() *FieldStatus {
	if x != nil {
		return x.Zip
	}
	return nil
}

func (x *GetBusinessProfileFieldStatusResponse) GetCountry() *FieldStatus {
	if x != nil {
		return x.Country
	}
	return nil
}

func (x *GetBusinessProfileFieldStatusResponse) GetWorkNumber() *FieldStatus {
	if x != nil {
		return x.WorkNumber
	}
	return nil
}

func (x *GetBusinessProfileFieldStatusResponse) GetCallTrackingNumber() *FieldStatus {
	if x != nil {
		return x.CallTrackingNumber
	}
	return nil
}

func (x *GetBusinessProfileFieldStatusResponse) GetWebsite() *FieldStatus {
	if x != nil {
		return x.Website
	}
	return nil
}

func (x *GetBusinessProfileFieldStatusResponse) GetLocation() *FieldStatus {
	if x != nil {
		return x.Location
	}
	return nil
}

func (x *GetBusinessProfileFieldStatusResponse) GetSalesPersonId() *FieldStatus {
	if x != nil {
		return x.SalesPersonId
	}
	return nil
}

func (x *GetBusinessProfileFieldStatusResponse) GetAdditionalSalesPersonId() *FieldStatus {
	if x != nil {
		return x.AdditionalSalesPersonId
	}
	return nil
}

func (x *GetBusinessProfileFieldStatusResponse) GetVcategoryIds() *FieldStatus {
	if x != nil {
		return x.VcategoryIds
	}
	return nil
}

func (x *GetBusinessProfileFieldStatusResponse) GetCustomerIdentifier() *FieldStatus {
	if x != nil {
		return x.CustomerIdentifier
	}
	return nil
}

func (x *GetBusinessProfileFieldStatusResponse) GetLifecycleStage() *FieldStatus {
	if x != nil {
		return x.LifecycleStage
	}
	return nil
}

func (x *GetBusinessProfileFieldStatusResponse) GetCreated() *timestamppb.Timestamp {
	if x != nil {
		return x.Created
	}
	return nil
}

func (x *GetBusinessProfileFieldStatusResponse) GetUpdated() *timestamppb.Timestamp {
	if x != nil {
		return x.Updated
	}
	return nil
}

func (x *GetBusinessProfileFieldStatusResponse) GetDeleted() *timestamppb.Timestamp {
	if x != nil {
		return x.Deleted
	}
	return nil
}

type UpdateVendorSyncFlagRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BusinessId    string                 `protobuf:"bytes,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	VendorId      string                 `protobuf:"bytes,2,opt,name=vendor_id,json=vendorId,proto3" json:"vendor_id,omitempty"`
	Syncing       bool                   `protobuf:"varint,3,opt,name=syncing,proto3" json:"syncing,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateVendorSyncFlagRequest) Reset() {
	*x = UpdateVendorSyncFlagRequest{}
	mi := &file_listing_products_v1_api_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateVendorSyncFlagRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateVendorSyncFlagRequest) ProtoMessage() {}

func (x *UpdateVendorSyncFlagRequest) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_api_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateVendorSyncFlagRequest.ProtoReflect.Descriptor instead.
func (*UpdateVendorSyncFlagRequest) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_api_proto_rawDescGZIP(), []int{38}
}

func (x *UpdateVendorSyncFlagRequest) GetBusinessId() string {
	if x != nil {
		return x.BusinessId
	}
	return ""
}

func (x *UpdateVendorSyncFlagRequest) GetVendorId() string {
	if x != nil {
		return x.VendorId
	}
	return ""
}

func (x *UpdateVendorSyncFlagRequest) GetSyncing() bool {
	if x != nil {
		return x.Syncing
	}
	return false
}

type IsLocalSEOProActiveForAccountRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	AccountGroupId string                 `protobuf:"bytes,1,opt,name=account_group_id,json=accountGroupId,proto3" json:"account_group_id,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *IsLocalSEOProActiveForAccountRequest) Reset() {
	*x = IsLocalSEOProActiveForAccountRequest{}
	mi := &file_listing_products_v1_api_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IsLocalSEOProActiveForAccountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsLocalSEOProActiveForAccountRequest) ProtoMessage() {}

func (x *IsLocalSEOProActiveForAccountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_api_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsLocalSEOProActiveForAccountRequest.ProtoReflect.Descriptor instead.
func (*IsLocalSEOProActiveForAccountRequest) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_api_proto_rawDescGZIP(), []int{39}
}

func (x *IsLocalSEOProActiveForAccountRequest) GetAccountGroupId() string {
	if x != nil {
		return x.AccountGroupId
	}
	return ""
}

type IsLocalSEOProActiveForAccountResponse struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	AccountGroupId string                 `protobuf:"bytes,1,opt,name=account_group_id,json=accountGroupId,proto3" json:"account_group_id,omitempty"`
	IsActive       bool                   `protobuf:"varint,2,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	EditionId      string                 `protobuf:"bytes,3,opt,name=edition_id,json=editionId,proto3" json:"edition_id,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *IsLocalSEOProActiveForAccountResponse) Reset() {
	*x = IsLocalSEOProActiveForAccountResponse{}
	mi := &file_listing_products_v1_api_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IsLocalSEOProActiveForAccountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsLocalSEOProActiveForAccountResponse) ProtoMessage() {}

func (x *IsLocalSEOProActiveForAccountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_api_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsLocalSEOProActiveForAccountResponse.ProtoReflect.Descriptor instead.
func (*IsLocalSEOProActiveForAccountResponse) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_api_proto_rawDescGZIP(), []int{40}
}

func (x *IsLocalSEOProActiveForAccountResponse) GetAccountGroupId() string {
	if x != nil {
		return x.AccountGroupId
	}
	return ""
}

func (x *IsLocalSEOProActiveForAccountResponse) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *IsLocalSEOProActiveForAccountResponse) GetEditionId() string {
	if x != nil {
		return x.EditionId
	}
	return ""
}

var File_listing_products_v1_api_proto protoreflect.FileDescriptor

const file_listing_products_v1_api_proto_rawDesc = "" +
	"\n" +
	"\x1dlisting_products/v1/api.proto\x12\x13listing_products.v1\x1a\x1bgoogle/protobuf/empty.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a*listing_products/v1/addon_attributes.proto\x1a vendasta_types/annotations.proto\x1a\"listing_products/v1/insights.proto\x1a%listing_products/v1/turbolister.proto\x1a)listing_products/v1/listing_sources.proto\x1a)listing_products/v1/listing_profile.proto\x1a%listing_products/v1/suggestions.proto\x1a\x1dlisting_products/v1/seo.proto\x1a#listing_products/v1/citations.proto\x1a)listing_products/v1/partnersettings.proto\x1a.listing_products/v1/seosuggestedkeywords.proto\x1a\"listing_products/v1/syncdata.proto\"\x85\x01\n" +
	"\"ManuallyFixYextProvisioningRequest\x12\x1f\n" +
	"\vbusiness_id\x18\x01 \x01(\tR\n" +
	"businessId\x12\x19\n" +
	"\baddon_id\x18\x02 \x01(\tR\aaddonId\x12#\n" +
	"\ractivation_id\x18\x03 \x01(\tR\factivationId\"O\n" +
	",ManuallyDeactivateUberallProvisioningRequest\x12\x1f\n" +
	"\vbusiness_id\x18\x01 \x01(\tR\n" +
	"businessId\"\x82\x01\n" +
	"\x1fManuallyProvisionUberallRequest\x12\x1f\n" +
	"\vbusiness_id\x18\x01 \x01(\tR\n" +
	"businessId\x12\x19\n" +
	"\baddon_id\x18\x02 \x01(\tR\aaddonId\x12#\n" +
	"\ractivation_id\x18\x03 \x01(\tR\factivationId\"?\n" +
	"\x1cGetGoogleBusinessInfoRequest\x12\x1f\n" +
	"\vbusiness_id\x18\x01 \x01(\tR\n" +
	"businessId\"<\n" +
	"\x1dGetGoogleBusinessInfoResponse\x12\x1b\n" +
	"\ttext_dump\x18\x01 \x01(\tR\btextDump\"=\n" +
	"\x1aGetFacebookPageInfoRequest\x12\x1f\n" +
	"\vbusiness_id\x18\x01 \x01(\tR\n" +
	"businessId\":\n" +
	"\x1bGetFacebookPageInfoResponse\x12\x1b\n" +
	"\ttext_dump\x18\x01 \x01(\tR\btextDump\"E\n" +
	"\"GetAppleBusinessConnectInfoRequest\x12\x1f\n" +
	"\vbusiness_id\x18\x01 \x01(\tR\n" +
	"businessId\"B\n" +
	"#GetAppleBusinessConnectInfoResponse\x12\x1b\n" +
	"\ttext_dump\x18\x01 \x01(\tR\btextDump\";\n" +
	"\x18GetBingPlacesInfoRequest\x12\x1f\n" +
	"\vbusiness_id\x18\x01 \x01(\tR\n" +
	"businessId\"8\n" +
	"\x19GetBingPlacesInfoResponse\x12\x1b\n" +
	"\ttext_dump\x18\x01 \x01(\tR\btextDump\"\xb3\x01\n" +
	"\"TriggerBingInsightsBackFillRequest\x12\x1f\n" +
	"\vbusiness_id\x18\x01 \x01(\tR\n" +
	"businessId\x122\n" +
	"\x15first_successful_sync\x18\x02 \x01(\bR\x13firstSuccessfulSync\x12\x1d\n" +
	"\n" +
	"start_date\x18\x03 \x01(\tR\tstartDate\x12\x19\n" +
	"\bend_date\x18\x04 \x01(\tR\aendDate\"?\n" +
	"\x1cCheckSubmissionStatusRequest\x12\x1f\n" +
	"\vbusiness_id\x18\x01 \x01(\tR\n" +
	"businessId\"8\n" +
	"\x15GetInfoGroupIdRequest\x12\x1f\n" +
	"\vbusiness_id\x18\x01 \x01(\tR\n" +
	"businessId\";\n" +
	"\x16GetInfoGroupIdResponse\x12!\n" +
	"\finfogroup_id\x18\x01 \x01(\tR\vinfogroupId\"Y\n" +
	"-GetListingDistributionActivationStatusRequest\x12(\n" +
	"\x10account_group_id\x18\x01 \x01(\tR\x0eaccountGroupId\"\xce\x01\n" +
	".GetListingDistributionActivationStatusResponse\x12(\n" +
	"\x10account_group_id\x18\x01 \x01(\tR\x0eaccountGroupId\x12\x1b\n" +
	"\tis_active\x18\x02 \x01(\bR\bisActive\x12\x19\n" +
	"\baddon_id\x18\x03 \x01(\tR\aaddonId\x12:\n" +
	"\n" +
	"activation\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"activation\"K\n" +
	"(BulkConnectLocationsForGoogleUserRequest\x12\x1f\n" +
	"\vbusiness_id\x18\x01 \x03(\tR\n" +
	"businessId\"\x96\x02\n" +
	")BulkConnectLocationsForGoogleUserResponse\x12q\n" +
	"\vconnections\x18\x01 \x03(\v2O.listing_products.v1.BulkConnectLocationsForGoogleUserResponse.ConnectionsEntryR\vconnections\x126\n" +
	"\x17locations_not_connected\x18\x02 \x01(\tR\x15locationsNotConnected\x1a>\n" +
	"\x10ConnectionsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"W\n" +
	"\x19GetProductSettingsRequest\x12\x1d\n" +
	"\n" +
	"partner_id\x18\x01 \x01(\tR\tpartnerId\x12\x1b\n" +
	"\tmarket_id\x18\x02 \x01(\tR\bmarketId\"\x84\x01\n" +
	"\x1aGetProductSettingsResponse\x12\x1d\n" +
	"\n" +
	"partner_id\x18\x01 \x01(\tR\tpartnerId\x12\x1b\n" +
	"\tmarket_id\x18\x02 \x01(\tR\bmarketId\x12*\n" +
	"\x11can_edit_keywords\x18\x03 \x01(\bR\x0fcanEditKeywords\"[\n" +
	"\x1eGetDirectSyncSourceInfoRequest\x12\x1f\n" +
	"\vbusiness_id\x18\x01 \x01(\tR\n" +
	"businessId\x12\x18\n" +
	"\arefresh\x18\x02 \x01(\bR\arefresh\"\x86\x05\n" +
	"\x10DirectSyncSource\x12\x1b\n" +
	"\tsource_id\x18\x01 \x01(\x03R\bsourceId\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12I\n" +
	"\aaccount\x18\x03 \x01(\v2/.listing_products.v1.ConnectedDirectSyncAccountR\aaccount\x12!\n" +
	"\fconnect_link\x18\x04 \x01(\tR\vconnectLink\x12?\n" +
	"\baccuracy\x18\x05 \x01(\v2#.listing_products.v1.SourceAccuracyR\baccuracy\x12\x14\n" +
	"\x05score\x18\x06 \x01(\x03R\x05score\x12\x1b\n" +
	"\tmax_score\x18\a \x01(\x03R\bmaxScore\x12\x1f\n" +
	"\vlisting_url\x18\b \x01(\tR\n" +
	"listingUrl\x12\x1d\n" +
	"\n" +
	"listing_id\x18\t \x01(\tR\tlistingId\x12G\n" +
	"\vsync_status\x18\n" +
	" \x01(\v2&.listing_products.v1.SyndicationStatusR\n" +
	"syncStatus\x12`\n" +
	"\x13additional_accounts\x18\v \x03(\v2/.listing_products.v1.ConnectedDirectSyncAccountR\x12additionalAccounts\x12,\n" +
	"\x12is_syncing_enabled\x18\f \x01(\bR\x10isSyncingEnabled\x12.\n" +
	"\x13is_listing_verified\x18\r \x01(\bR\x11isListingVerified\x12\x16\n" +
	"\x06hidden\x18\x0e \x01(\bR\x06hidden\"|\n" +
	"\x1fGetDirectSyncSourceInfoResponse\x12Y\n" +
	"\x15direct_submit_sources\x18\x01 \x03(\v2%.listing_products.v1.DirectSyncSourceR\x13directSubmitSources\"J\n" +
	"%StartBusinessDataScoreWorkflowRequest\x12!\n" +
	"\fbusiness_ids\x18\x01 \x03(\tR\vbusinessIds\"5\n" +
	"\x14IsPartnerUserRequest\x12\x1d\n" +
	"\n" +
	"partner_id\x18\x01 \x01(\tR\tpartnerId\"?\n" +
	"\x15IsPartnerUserResponse\x12&\n" +
	"\x0fis_partner_user\x18\x01 \x01(\bR\risPartnerUser\"3\n" +
	"\x10GetLDDataRequest\x12\x1f\n" +
	"\vbusiness_id\x18\x01 \x01(\tR\n" +
	"businessId\"\xdf\x01\n" +
	"\x11GetLDDataResponse\x12?\n" +
	"\aneustar\x18\x01 \x01(\v2%.listing_products.v1.DirectSyncSourceR\aneustar\x12B\n" +
	"\tdata_axle\x18\x02 \x01(\v2%.listing_products.v1.DirectSyncSourceR\bdataAxle\x12E\n" +
	"\n" +
	"foursquare\x18\x03 \x01(\v2%.listing_products.v1.DirectSyncSourceR\n" +
	"foursquare\"Q\n" +
	"\x14SyncToSourcesRequest\x12\x1f\n" +
	"\vbusiness_id\x18\x01 \x01(\tR\n" +
	"businessId\x12\x18\n" +
	"\atrigger\x18\x02 \x01(\tR\atrigger\"@\n" +
	"\x1dGetNeustarPublicationsRequest\x12\x1f\n" +
	"\vbusiness_id\x18\x01 \x01(\tR\n" +
	"businessId\"\xda\x02\n" +
	"\x12NeustarPublication\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1b\n" +
	"\tparent_id\x18\x02 \x01(\x03R\bparentId\x12\x1a\n" +
	"\bplatform\x18\x03 \x01(\tR\bplatform\x12 \n" +
	"\vdescription\x18\x04 \x01(\tR\vdescription\x12\x18\n" +
	"\amessage\x18\x05 \x01(\tR\amessage\x12H\n" +
	"\x12last_delivery_time\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\x10lastDeliveryTime\x12?\n" +
	"\rlast_modified\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\flastModified\x12 \n" +
	"\vdisposition\x18\b \x01(\tR\vdisposition\x12\x12\n" +
	"\x04link\x18\t \x01(\tR\x04link\"m\n" +
	"\x1eGetNeustarPublicationsResponse\x12K\n" +
	"\fpublications\x18\x01 \x03(\v2'.listing_products.v1.NeustarPublicationR\fpublications\"\"\n" +
	" GetDoctorDotComCategoriesRequest\"n\n" +
	"!GetDoctorDotComCategoriesResponse\x12I\n" +
	"\n" +
	"categories\x18\x01 \x03(\v2).listing_products.v1.DoctorDotComCategoryR\n" +
	"categories\"G\n" +
	"$GetBusinessProfileFieldStatusRequest\x12\x1f\n" +
	"\vbusiness_id\x18\x01 \x01(\tR\n" +
	"businessId\"h\n" +
	"\vFieldStatus\x12#\n" +
	"\rupdate_origin\x18\x01 \x01(\tR\fupdateOrigin\x124\n" +
	"\aupdated\x18\x02 \x01(\v2\x1a.google.protobuf.TimestampR\aupdated\"\xa6\n" +
	"\n" +
	"%GetBusinessProfileFieldStatusResponse\x12\x1f\n" +
	"\vbusiness_id\x18\x01 \x01(\tR\n" +
	"businessId\x12C\n" +
	"\fcompany_name\x18\x02 \x01(\v2 .listing_products.v1.FieldStatusR\vcompanyName\x12:\n" +
	"\aaddress\x18\x03 \x01(\v2 .listing_products.v1.FieldStatusR\aaddress\x12<\n" +
	"\baddress2\x18\x04 \x01(\v2 .listing_products.v1.FieldStatusR\baddress2\x124\n" +
	"\x04city\x18\x05 \x01(\v2 .listing_products.v1.FieldStatusR\x04city\x126\n" +
	"\x05state\x18\x06 \x01(\v2 .listing_products.v1.FieldStatusR\x05state\x122\n" +
	"\x03zip\x18\a \x01(\v2 .listing_products.v1.FieldStatusR\x03zip\x12:\n" +
	"\acountry\x18\b \x01(\v2 .listing_products.v1.FieldStatusR\acountry\x12A\n" +
	"\vwork_number\x18\t \x01(\v2 .listing_products.v1.FieldStatusR\n" +
	"workNumber\x12R\n" +
	"\x14call_tracking_number\x18\n" +
	" \x01(\v2 .listing_products.v1.FieldStatusR\x12callTrackingNumber\x12:\n" +
	"\awebsite\x18\v \x01(\v2 .listing_products.v1.FieldStatusR\awebsite\x12<\n" +
	"\blocation\x18\f \x01(\v2 .listing_products.v1.FieldStatusR\blocation\x12H\n" +
	"\x0fsales_person_id\x18\r \x01(\v2 .listing_products.v1.FieldStatusR\rsalesPersonId\x12]\n" +
	"\x1aadditional_sales_person_id\x18\x0e \x01(\v2 .listing_products.v1.FieldStatusR\x17additionalSalesPersonId\x12E\n" +
	"\rvcategory_ids\x18\x0f \x01(\v2 .listing_products.v1.FieldStatusR\fvcategoryIds\x12Q\n" +
	"\x13customer_identifier\x18\x10 \x01(\v2 .listing_products.v1.FieldStatusR\x12customerIdentifier\x12I\n" +
	"\x0flifecycle_stage\x18\x11 \x01(\v2 .listing_products.v1.FieldStatusR\x0elifecycleStage\x124\n" +
	"\acreated\x18\x12 \x01(\v2\x1a.google.protobuf.TimestampR\acreated\x124\n" +
	"\aupdated\x18\x13 \x01(\v2\x1a.google.protobuf.TimestampR\aupdated\x124\n" +
	"\adeleted\x18\x14 \x01(\v2\x1a.google.protobuf.TimestampR\adeleted\"u\n" +
	"\x1bUpdateVendorSyncFlagRequest\x12\x1f\n" +
	"\vbusiness_id\x18\x01 \x01(\tR\n" +
	"businessId\x12\x1b\n" +
	"\tvendor_id\x18\x02 \x01(\tR\bvendorId\x12\x18\n" +
	"\asyncing\x18\x03 \x01(\bR\asyncing\"P\n" +
	"$IsLocalSEOProActiveForAccountRequest\x12(\n" +
	"\x10account_group_id\x18\x01 \x01(\tR\x0eaccountGroupId\"\x8d\x01\n" +
	"%IsLocalSEOProActiveForAccountResponse\x12(\n" +
	"\x10account_group_id\x18\x01 \x01(\tR\x0eaccountGroupId\x12\x1b\n" +
	"\tis_active\x18\x02 \x01(\bR\bisActive\x12\x1d\n" +
	"\n" +
	"edition_id\x18\x03 \x01(\tR\teditionId2\xda \n" +
	"\x16ListingProductsService\x12\x9c\x01\n" +
	"\x1fGetGoogleMyBusinessInsightsData\x12;.listing_products.v1.GetGoogleMyBusinessInsightsDataRequest\x1a<.listing_products.v1.GetGoogleMyBusinessInsightsDataResponse\x12\xb4\x01\n" +
	"'GetGoogleMyBusinessInsightsDataBucketed\x12C.listing_products.v1.GetGoogleMyBusinessInsightsDataBucketedRequest\x1aD.listing_products.v1.GetGoogleMyBusinessInsightsDataBucketedResponse\x12`\n" +
	"\x14UpdateVendorSyncFlag\x120.listing_products.v1.UpdateVendorSyncFlagRequest\x1a\x16.google.protobuf.Empty\x12r\n" +
	"\x11SubmitTurboLister\x12-.listing_products.v1.SubmitTurboListerRequest\x1a..listing_products.v1.SubmitTurboListerResponse\x12u\n" +
	"\x12GetAddonAttributes\x12..listing_products.v1.GetAddonAttributesRequest\x1a/.listing_products.v1.GetAddonAttributesResponse\x12b\n" +
	"\x15CreateAddonAttributes\x121.listing_products.v1.CreateAddonAttributesRequest\x1a\x16.google.protobuf.Empty\x12b\n" +
	"\x15UpdateAddonAttributes\x121.listing_products.v1.UpdateAddonAttributesRequest\x1a\x16.google.protobuf.Empty\x12b\n" +
	"\x15DeleteAddonAttributes\x121.listing_products.v1.DeleteAddonAttributesRequest\x1a\x16.google.protobuf.Empty\x12\x84\x01\n" +
	"\x17GetMultiAddonAttributes\x123.listing_products.v1.GetMultiAddonAttributesRequest\x1a4.listing_products.v1.GetMultiAddonAttributesResponse\x12\xa2\x01\n" +
	"!BulkConnectLocationsForGoogleUser\x12=.listing_products.v1.BulkConnectLocationsForGoogleUserRequest\x1a>.listing_products.v1.BulkConnectLocationsForGoogleUserResponse\x12n\n" +
	"\x1bManuallyFixYextProvisioning\x127.listing_products.v1.ManuallyFixYextProvisioningRequest\x1a\x16.google.protobuf.Empty\x12h\n" +
	"\x18ManuallyProvisionUberall\x124.listing_products.v1.ManuallyProvisionUberallRequest\x1a\x16.google.protobuf.Empty\x12\x82\x01\n" +
	"%ManuallyDeactivateUberallProvisioning\x12A.listing_products.v1.ManuallyDeactivateUberallProvisioningRequest\x1a\x16.google.protobuf.Empty\x12\x8b\x01\n" +
	"\x15GetGoogleBusinessInfo\x121.listing_products.v1.GetGoogleBusinessInfoRequest\x1a2.listing_products.v1.GetGoogleBusinessInfoResponse\"\v\x82\xb5\x18\a\n" +
	"\x05admin\x12\x85\x01\n" +
	"\x13GetFacebookPageInfo\x12/.listing_products.v1.GetFacebookPageInfoRequest\x1a0.listing_products.v1.GetFacebookPageInfoResponse\"\v\x82\xb5\x18\a\n" +
	"\x05admin\x12\x9d\x01\n" +
	"\x1bGetAppleBusinessConnectInfo\x127.listing_products.v1.GetAppleBusinessConnectInfoRequest\x1a8.listing_products.v1.GetAppleBusinessConnectInfoResponse\"\v\x82\xb5\x18\a\n" +
	"\x05admin\x12\x7f\n" +
	"\x11GetBingPlacesInfo\x12-.listing_products.v1.GetBingPlacesInfoRequest\x1a..listing_products.v1.GetBingPlacesInfoResponse\"\v\x82\xb5\x18\a\n" +
	"\x05admin\x12n\n" +
	"\x1bTriggerBingInsightsBackFill\x127.listing_products.v1.TriggerBingInsightsBackFillRequest\x1a\x16.google.protobuf.Empty\x12b\n" +
	"\x15CheckSubmissionStatus\x121.listing_products.v1.CheckSubmissionStatusRequest\x1a\x16.google.protobuf.Empty\x12i\n" +
	"\x0eGetInfoGroupId\x12*.listing_products.v1.GetInfoGroupIdRequest\x1a+.listing_products.v1.GetInfoGroupIdResponse\x12\xb1\x01\n" +
	"&GetListingDistributionActivationStatus\x12B.listing_products.v1.GetListingDistributionActivationStatusRequest\x1aC.listing_products.v1.GetListingDistributionActivationStatusResponse\x12\x98\x01\n" +
	"\x17GetDirectSyncSourceInfo\x123.listing_products.v1.GetDirectSyncSourceInfoRequest\x1a4.listing_products.v1.GetDirectSyncSourceInfoResponse\"\x12\x82\xb5\x18\x0e\n" +
	"\fbusiness-app\x12t\n" +
	"\vGetSyncData\x12'.listing_products.v1.GetSyncDataRequest\x1a(.listing_products.v1.GetSyncDataResponse\"\x12\x82\xb5\x18\x0e\n" +
	"\fbusiness-app\x12u\n" +
	"\x12GetSyndicationInfo\x12..listing_products.v1.GetSyndicationInfoRequest\x1a/.listing_products.v1.GetSyndicationInfoResponse\x12\x89\x01\n" +
	"\x12GetProductSettings\x12..listing_products.v1.GetProductSettingsRequest\x1a/.listing_products.v1.GetProductSettingsResponse\"\x12\x82\xb5\x18\x0e\n" +
	"\fbusiness-app\x12\x8a\x01\n" +
	"\x19GetDoctorDotComCategories\x125.listing_products.v1.GetDoctorDotComCategoriesRequest\x1a6.listing_products.v1.GetDoctorDotComCategoriesResponse\x12p\n" +
	"\x1cFetchAndSubmitSourceURLsToCS\x128.listing_products.v1.FetchAndSubmitSourceURLsToCSRequest\x1a\x16.google.protobuf.Empty\x12t\n" +
	"\x1eStartBusinessDataScoreWorkflow\x12:.listing_products.v1.StartBusinessDataScoreWorkflowRequest\x1a\x16.google.protobuf.Empty\x12f\n" +
	"\rIsPartnerUser\x12).listing_products.v1.IsPartnerUserRequest\x1a*.listing_products.v1.IsPartnerUserResponse\x12\x96\x01\n" +
	"\x1dIsLocalSEOProActiveForAccount\x129.listing_products.v1.IsLocalSEOProActiveForAccountRequest\x1a:.listing_products.v1.IsLocalSEOProActiveForAccountResponse\x12Z\n" +
	"\tGetLDData\x12%.listing_products.v1.GetLDDataRequest\x1a&.listing_products.v1.GetLDDataResponse\x12R\n" +
	"\rSyncToSources\x12).listing_products.v1.SyncToSourcesRequest\x1a\x16.google.protobuf.Empty\x12\x81\x01\n" +
	"\x16GetNeustarPublications\x122.listing_products.v1.GetNeustarPublicationsRequest\x1a3.listing_products.v1.GetNeustarPublicationsResponse2\xff\a\n" +
	"\x14ListingSourceService\x12\x7f\n" +
	"\x11GetListingSources\x12-.listing_products.v1.GetListingSourcesRequest\x1a..listing_products.v1.GetListingSourcesResponse\"\v\x82\xb5\x18\a\n" +
	"\x05admin\x12\x88\x01\n" +
	"\x14GetListingSourceById\x120.listing_products.v1.GetListingSourceByIdRequest\x1a1.listing_products.v1.GetListingSourceByIdResponse\"\v\x82\xb5\x18\a\n" +
	"\x05admin\x12\x93\x01\n" +
	"\x1cGetListingSourceByProviderId\x128.listing_products.v1.GetListingSourceByProviderIdRequest\x1a9.listing_products.v1.GetListingSourceByProviderIdResponse\x12r\n" +
	"\x11GetPartnerSources\x12-.listing_products.v1.GetPartnerSourcesRequest\x1a..listing_products.v1.GetPartnerSourcesResponse\x12k\n" +
	"\x13CreateListingSource\x12/.listing_products.v1.CreateListingSourceRequest\x1a\x16.google.protobuf.Empty\"\v\x82\xb5\x18\a\n" +
	"\x05admin\x12k\n" +
	"\x13DeleteListingSource\x12/.listing_products.v1.DeleteListingSourceRequest\x1a\x16.google.protobuf.Empty\"\v\x82\xb5\x18\a\n" +
	"\x05admin\x12o\n" +
	"\x15UndeleteListingSource\x121.listing_products.v1.UndeleteListingSourceRequest\x1a\x16.google.protobuf.Empty\"\v\x82\xb5\x18\a\n" +
	"\x05admin\x12\x85\x01\n" +
	"\x13UpdateListingSource\x12/.listing_products.v1.UpdateListingSourceRequest\x1a0.listing_products.v1.UpdateListingSourceResponse\"\v\x82\xb5\x18\a\n" +
	"\x05admin2\xba\n" +
	"\n" +
	"\x15ListingProfileService\x12m\n" +
	"\x06Create\x120.listing_products.v1.CreateListingProfileRequest\x1a1.listing_products.v1.CreateListingProfileResponse\x12\x87\x01\n" +
	"\bGetMulti\x122.listing_products.v1.GetMultiListingProfileRequest\x1a3.listing_products.v1.GetMultiListingProfileResponse\"\x12\x82\xb5\x18\x0e\n" +
	"\fbusiness-app\x12f\n" +
	"\x06Update\x120.listing_products.v1.UpdateListingProfileRequest\x1a\x16.google.protobuf.Empty\"\x12\x82\xb5\x18\x0e\n" +
	"\fbusiness-app\x12R\n" +
	"\x06Delete\x120.listing_products.v1.DeleteListingProfileRequest\x1a\x16.google.protobuf.Empty\x12\x86\x01\n" +
	"\x11GetMoreHoursTypes\x12-.listing_products.v1.GetMoreHoursTypesRequest\x1a..listing_products.v1.GetMoreHoursTypesResponse\"\x12\x82\xb5\x18\x0e\n" +
	"\fbusiness-app\x12\x8f\x01\n" +
	"\x14GetAttributeMetadata\x120.listing_products.v1.GetAttributeMetadataRequest\x1a1.listing_products.v1.GetAttributeMetadataResponse\"\x12\x82\xb5\x18\x0e\n" +
	"\fbusiness-app\x12\x8a\x01\n" +
	"\x19GetDoctorDotComCategories\x125.listing_products.v1.GetDoctorDotComCategoriesRequest\x1a6.listing_products.v1.GetDoctorDotComCategoriesResponse\x12V\n" +
	"\x0fLegacyAPICreate\x12+.listing_products.v1.LegacyAPICreateRequest\x1a\x16.google.protobuf.Empty\x12V\n" +
	"\x0fLegacyAPIUpdate\x12+.listing_products.v1.LegacyAPIUpdateRequest\x1a\x16.google.protobuf.Empty\x12\x96\x01\n" +
	"\x1dGetBusinessProfileFieldStatus\x129.listing_products.v1.GetBusinessProfileFieldStatusRequest\x1a:.listing_products.v1.GetBusinessProfileFieldStatusResponse\x12{\n" +
	"\x14GetMultiAccountGroup\x120.listing_products.v1.GetMultiAccountGroupRequest\x1a1.listing_products.v1.GetMultiAccountGroupResponse2\xe0\x04\n" +
	"\x11SuggestionService\x12N\n" +
	"\x06Upsert\x12,.listing_products.v1.UpsertSuggestionRequest\x1a\x16.google.protobuf.Empty\x12f\n" +
	"\rGetSuggestion\x12).listing_products.v1.GetSuggestionRequest\x1a*.listing_products.v1.GetSuggestionResponse\x12k\n" +
	"\bGetMulti\x12..listing_products.v1.GetMultiSuggestionRequest\x1a/.listing_products.v1.GetMultiSuggestionResponse\x12N\n" +
	"\x06Delete\x12,.listing_products.v1.DeleteSuggestionRequest\x1a\x16.google.protobuf.Empty\x12_\n" +
	"\x04List\x12*.listing_products.v1.ListSuggestionRequest\x1a+.listing_products.v1.ListSuggestionResponse\x12u\n" +
	"\x12SuggestFieldUpdate\x12..listing_products.v1.SuggestFieldUpdateRequest\x1a/.listing_products.v1.SuggestFieldUpdateResponse2\xa6\r\n" +
	"\n" +
	"SEOService\x12q\n" +
	"\n" +
	"GetSEOData\x12&.listing_products.v1.GetSEODataRequest\x1a'.listing_products.v1.GetSEODataResponse\"\x12\x82\xb5\x18\x0e\n" +
	"\fbusiness-app\x12i\n" +
	"\x0eGetAllAIOAudit\x12*.listing_products.v1.GetAllAIOAuditRequest\x1a+.listing_products.v1.GetAllAIOAuditResponse\x12`\n" +
	"\vGetAIOAudit\x12'.listing_products.v1.GetAIOAuditRequest\x1a(.listing_products.v1.GetAIOAuditResponse\x12\x8d\x01\n" +
	"\x1aGetAllAIOAuditScoreResults\x126.listing_products.v1.GetAllAIOAuditScoreResultsRequest\x1a7.listing_products.v1.GetAllAIOAuditScoreResultsResponse\x12r\n" +
	"\x11GetAIOAuditStatus\x12-.listing_products.v1.GetAIOAuditStatusRequest\x1a..listing_products.v1.GetAIOAuditStatusResponse\x12l\n" +
	"\x0fTriggerAIOAudit\x12+.listing_products.v1.TriggerAIOAuditRequest\x1a,.listing_products.v1.TriggerAIOAuditResponse\x12\x86\x01\n" +
	"\x11GetSEODataSummary\x12-.listing_products.v1.GetSEODataSummaryRequest\x1a..listing_products.v1.GetSEODataSummaryResponse\"\x12\x82\xb5\x18\x0e\n" +
	"\fbusiness-app\x12\x92\x01\n" +
	"\x15GetLocalSearchSEOData\x121.listing_products.v1.GetLocalSearchSEODataRequest\x1a2.listing_products.v1.GetLocalSearchSEODataResponse\"\x12\x82\xb5\x18\x0e\n" +
	"\fbusiness-app\x12j\n" +
	"\x19StartLocalSEODataWorkflow\x125.listing_products.v1.StartLocalSEODataWorkflowRequest\x1a\x16.google.protobuf.Empty\x12j\n" +
	"\x0fSaveSEOSettings\x12+.listing_products.v1.SaveSEOSettingsRequest\x1a\x16.google.protobuf.Empty\"\x12\x82\xb5\x18\x0e\n" +
	"\fbusiness-app\x12z\n" +
	"\x0eGetSEOSettings\x12*.listing_products.v1.GetSEOSettingsRequest\x1a(.listing_products.v1.SEOSettingsResponse\"\x12\x82\xb5\x18\x0e\n" +
	"\fbusiness-app\x12\x89\x01\n" +
	"\x12GetActiveSEOAddons\x12..listing_products.v1.GetActiveSEOAddonsRequest\x1a/.listing_products.v1.GetActiveSEOAddonsResponse\"\x12\x82\xb5\x18\x0e\n" +
	"\fbusiness-app\x12h\n" +
	"\x18StartSEOCategoryWorkflow\x124.listing_products.v1.StartSEOCategoryWorkflowRequest\x1a\x16.google.protobuf.Empty\x12~\n" +
	"\x15GetDataForSEOCategory\x121.listing_products.v1.GetDataForSEOCategoryRequest\x1a2.listing_products.v1.GetDataForSEOCategoryResponse2\xa6\x04\n" +
	"\tCitations\x12b\n" +
	"\x15StartCitationWorkflow\x121.listing_products.v1.StartCitationWorkflowRequest\x1a\x16.google.protobuf.Empty\x12l\n" +
	"\x0fGetCitationData\x12+.listing_products.v1.GetCitationDataRequest\x1a,.listing_products.v1.GetCitationDataResponse\x12V\n" +
	"\x0fDeleteCitations\x12+.listing_products.v1.DeleteCitationsRequest\x1a\x16.google.protobuf.Empty\x12u\n" +
	"\x12GetCitationSummary\x12..listing_products.v1.GetCitationSummaryRequest\x1a/.listing_products.v1.GetCitationSummaryResponse\x12x\n" +
	"\x13ListCitationDomains\x12/.listing_products.v1.ListCitationDomainsRequest\x1a0.listing_products.v1.ListCitationDomainsResponse2\xdd\x03\n" +
	"\x16PartnerSettingsService\x12b\n" +
	"\x15CreatePartnerSettings\x121.listing_products.v1.CreatePartnerSettingsRequest\x1a\x16.google.protobuf.Empty\x12u\n" +
	"\x12GetPartnerSettings\x12..listing_products.v1.GetPartnerSettingsRequest\x1a/.listing_products.v1.GetPartnerSettingsResponse\x12b\n" +
	"\x15UpsertPartnerSettings\x121.listing_products.v1.UpsertPartnerSettingsRequest\x1a\x16.google.protobuf.Empty\x12\x83\x01\n" +
	"\x10GetConfiguration\x12,.listing_products.v1.GetConfigurationRequest\x1a-.listing_products.v1.GetConfigurationResponse\"\x12\x82\xb5\x18\x0e\n" +
	"\fbusiness-app2\xdd\x02\n" +
	"\x1bSEOSuggestedKeywordsService\x12\xb6\x01\n" +
	"!GetOrGenerateSEOSuggestedKeywords\x12=.listing_products.v1.GetOrGenerateSEOSuggestedKeywordsRequest\x1a>.listing_products.v1.GetOrGenerateSEOSuggestedKeywordsResponse\"\x12\x82\xb5\x18\x0e\n" +
	"\fbusiness-app\x12\x84\x01\n" +
	"\x17GetSEOSuggestedKeywords\x123.listing_products.v1.GetSEOSuggestedKeywordsRequest\x1a4.listing_products.v1.GetSEOSuggestedKeywordsResponseB\x86\x01\n" +
	")com.vendasta.listingproducts.v1.generatedB\bApiProtoZOgithub.com/vendasta/generated-protos-go/listing_products/v1;listing_products_v1b\x06proto3"

var (
	file_listing_products_v1_api_proto_rawDescOnce sync.Once
	file_listing_products_v1_api_proto_rawDescData []byte
)

func file_listing_products_v1_api_proto_rawDescGZIP() []byte {
	file_listing_products_v1_api_proto_rawDescOnce.Do(func() {
		file_listing_products_v1_api_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_listing_products_v1_api_proto_rawDesc), len(file_listing_products_v1_api_proto_rawDesc)))
	})
	return file_listing_products_v1_api_proto_rawDescData
}

var file_listing_products_v1_api_proto_msgTypes = make([]protoimpl.MessageInfo, 42)
var file_listing_products_v1_api_proto_goTypes = []any{
	(*ManuallyFixYextProvisioningRequest)(nil),             // 0: listing_products.v1.ManuallyFixYextProvisioningRequest
	(*ManuallyDeactivateUberallProvisioningRequest)(nil),   // 1: listing_products.v1.ManuallyDeactivateUberallProvisioningRequest
	(*ManuallyProvisionUberallRequest)(nil),                // 2: listing_products.v1.ManuallyProvisionUberallRequest
	(*GetGoogleBusinessInfoRequest)(nil),                   // 3: listing_products.v1.GetGoogleBusinessInfoRequest
	(*GetGoogleBusinessInfoResponse)(nil),                  // 4: listing_products.v1.GetGoogleBusinessInfoResponse
	(*GetFacebookPageInfoRequest)(nil),                     // 5: listing_products.v1.GetFacebookPageInfoRequest
	(*GetFacebookPageInfoResponse)(nil),                    // 6: listing_products.v1.GetFacebookPageInfoResponse
	(*GetAppleBusinessConnectInfoRequest)(nil),             // 7: listing_products.v1.GetAppleBusinessConnectInfoRequest
	(*GetAppleBusinessConnectInfoResponse)(nil),            // 8: listing_products.v1.GetAppleBusinessConnectInfoResponse
	(*GetBingPlacesInfoRequest)(nil),                       // 9: listing_products.v1.GetBingPlacesInfoRequest
	(*GetBingPlacesInfoResponse)(nil),                      // 10: listing_products.v1.GetBingPlacesInfoResponse
	(*TriggerBingInsightsBackFillRequest)(nil),             // 11: listing_products.v1.TriggerBingInsightsBackFillRequest
	(*CheckSubmissionStatusRequest)(nil),                   // 12: listing_products.v1.CheckSubmissionStatusRequest
	(*GetInfoGroupIdRequest)(nil),                          // 13: listing_products.v1.GetInfoGroupIdRequest
	(*GetInfoGroupIdResponse)(nil),                         // 14: listing_products.v1.GetInfoGroupIdResponse
	(*GetListingDistributionActivationStatusRequest)(nil),  // 15: listing_products.v1.GetListingDistributionActivationStatusRequest
	(*GetListingDistributionActivationStatusResponse)(nil), // 16: listing_products.v1.GetListingDistributionActivationStatusResponse
	(*BulkConnectLocationsForGoogleUserRequest)(nil),       // 17: listing_products.v1.BulkConnectLocationsForGoogleUserRequest
	(*BulkConnectLocationsForGoogleUserResponse)(nil),      // 18: listing_products.v1.BulkConnectLocationsForGoogleUserResponse
	(*GetProductSettingsRequest)(nil),                      // 19: listing_products.v1.GetProductSettingsRequest
	(*GetProductSettingsResponse)(nil),                     // 20: listing_products.v1.GetProductSettingsResponse
	(*GetDirectSyncSourceInfoRequest)(nil),                 // 21: listing_products.v1.GetDirectSyncSourceInfoRequest
	(*DirectSyncSource)(nil),                               // 22: listing_products.v1.DirectSyncSource
	(*GetDirectSyncSourceInfoResponse)(nil),                // 23: listing_products.v1.GetDirectSyncSourceInfoResponse
	(*StartBusinessDataScoreWorkflowRequest)(nil),          // 24: listing_products.v1.StartBusinessDataScoreWorkflowRequest
	(*IsPartnerUserRequest)(nil),                           // 25: listing_products.v1.IsPartnerUserRequest
	(*IsPartnerUserResponse)(nil),                          // 26: listing_products.v1.IsPartnerUserResponse
	(*GetLDDataRequest)(nil),                               // 27: listing_products.v1.GetLDDataRequest
	(*GetLDDataResponse)(nil),                              // 28: listing_products.v1.GetLDDataResponse
	(*SyncToSourcesRequest)(nil),                           // 29: listing_products.v1.SyncToSourcesRequest
	(*GetNeustarPublicationsRequest)(nil),                  // 30: listing_products.v1.GetNeustarPublicationsRequest
	(*NeustarPublication)(nil),                             // 31: listing_products.v1.NeustarPublication
	(*GetNeustarPublicationsResponse)(nil),                 // 32: listing_products.v1.GetNeustarPublicationsResponse
	(*GetDoctorDotComCategoriesRequest)(nil),               // 33: listing_products.v1.GetDoctorDotComCategoriesRequest
	(*GetDoctorDotComCategoriesResponse)(nil),              // 34: listing_products.v1.GetDoctorDotComCategoriesResponse
	(*GetBusinessProfileFieldStatusRequest)(nil),           // 35: listing_products.v1.GetBusinessProfileFieldStatusRequest
	(*FieldStatus)(nil),                                    // 36: listing_products.v1.FieldStatus
	(*GetBusinessProfileFieldStatusResponse)(nil),          // 37: listing_products.v1.GetBusinessProfileFieldStatusResponse
	(*UpdateVendorSyncFlagRequest)(nil),                    // 38: listing_products.v1.UpdateVendorSyncFlagRequest
	(*IsLocalSEOProActiveForAccountRequest)(nil),           // 39: listing_products.v1.IsLocalSEOProActiveForAccountRequest
	(*IsLocalSEOProActiveForAccountResponse)(nil),          // 40: listing_products.v1.IsLocalSEOProActiveForAccountResponse
	nil,                                            // 41: listing_products.v1.BulkConnectLocationsForGoogleUserResponse.ConnectionsEntry
	(*timestamppb.Timestamp)(nil),                  // 42: google.protobuf.Timestamp
	(*ConnectedDirectSyncAccount)(nil),             // 43: listing_products.v1.ConnectedDirectSyncAccount
	(*SourceAccuracy)(nil),                         // 44: listing_products.v1.SourceAccuracy
	(*SyndicationStatus)(nil),                      // 45: listing_products.v1.SyndicationStatus
	(*DoctorDotComCategory)(nil),                   // 46: listing_products.v1.DoctorDotComCategory
	(*GetGoogleMyBusinessInsightsDataRequest)(nil), // 47: listing_products.v1.GetGoogleMyBusinessInsightsDataRequest
	(*GetGoogleMyBusinessInsightsDataBucketedRequest)(nil),  // 48: listing_products.v1.GetGoogleMyBusinessInsightsDataBucketedRequest
	(*SubmitTurboListerRequest)(nil),                        // 49: listing_products.v1.SubmitTurboListerRequest
	(*GetAddonAttributesRequest)(nil),                       // 50: listing_products.v1.GetAddonAttributesRequest
	(*CreateAddonAttributesRequest)(nil),                    // 51: listing_products.v1.CreateAddonAttributesRequest
	(*UpdateAddonAttributesRequest)(nil),                    // 52: listing_products.v1.UpdateAddonAttributesRequest
	(*DeleteAddonAttributesRequest)(nil),                    // 53: listing_products.v1.DeleteAddonAttributesRequest
	(*GetMultiAddonAttributesRequest)(nil),                  // 54: listing_products.v1.GetMultiAddonAttributesRequest
	(*GetSyncDataRequest)(nil),                              // 55: listing_products.v1.GetSyncDataRequest
	(*GetSyndicationInfoRequest)(nil),                       // 56: listing_products.v1.GetSyndicationInfoRequest
	(*FetchAndSubmitSourceURLsToCSRequest)(nil),             // 57: listing_products.v1.FetchAndSubmitSourceURLsToCSRequest
	(*GetListingSourcesRequest)(nil),                        // 58: listing_products.v1.GetListingSourcesRequest
	(*GetListingSourceByIdRequest)(nil),                     // 59: listing_products.v1.GetListingSourceByIdRequest
	(*GetListingSourceByProviderIdRequest)(nil),             // 60: listing_products.v1.GetListingSourceByProviderIdRequest
	(*GetPartnerSourcesRequest)(nil),                        // 61: listing_products.v1.GetPartnerSourcesRequest
	(*CreateListingSourceRequest)(nil),                      // 62: listing_products.v1.CreateListingSourceRequest
	(*DeleteListingSourceRequest)(nil),                      // 63: listing_products.v1.DeleteListingSourceRequest
	(*UndeleteListingSourceRequest)(nil),                    // 64: listing_products.v1.UndeleteListingSourceRequest
	(*UpdateListingSourceRequest)(nil),                      // 65: listing_products.v1.UpdateListingSourceRequest
	(*CreateListingProfileRequest)(nil),                     // 66: listing_products.v1.CreateListingProfileRequest
	(*GetMultiListingProfileRequest)(nil),                   // 67: listing_products.v1.GetMultiListingProfileRequest
	(*UpdateListingProfileRequest)(nil),                     // 68: listing_products.v1.UpdateListingProfileRequest
	(*DeleteListingProfileRequest)(nil),                     // 69: listing_products.v1.DeleteListingProfileRequest
	(*GetMoreHoursTypesRequest)(nil),                        // 70: listing_products.v1.GetMoreHoursTypesRequest
	(*GetAttributeMetadataRequest)(nil),                     // 71: listing_products.v1.GetAttributeMetadataRequest
	(*LegacyAPICreateRequest)(nil),                          // 72: listing_products.v1.LegacyAPICreateRequest
	(*LegacyAPIUpdateRequest)(nil),                          // 73: listing_products.v1.LegacyAPIUpdateRequest
	(*GetMultiAccountGroupRequest)(nil),                     // 74: listing_products.v1.GetMultiAccountGroupRequest
	(*UpsertSuggestionRequest)(nil),                         // 75: listing_products.v1.UpsertSuggestionRequest
	(*GetSuggestionRequest)(nil),                            // 76: listing_products.v1.GetSuggestionRequest
	(*GetMultiSuggestionRequest)(nil),                       // 77: listing_products.v1.GetMultiSuggestionRequest
	(*DeleteSuggestionRequest)(nil),                         // 78: listing_products.v1.DeleteSuggestionRequest
	(*ListSuggestionRequest)(nil),                           // 79: listing_products.v1.ListSuggestionRequest
	(*SuggestFieldUpdateRequest)(nil),                       // 80: listing_products.v1.SuggestFieldUpdateRequest
	(*GetSEODataRequest)(nil),                               // 81: listing_products.v1.GetSEODataRequest
	(*GetAllAIOAuditRequest)(nil),                           // 82: listing_products.v1.GetAllAIOAuditRequest
	(*GetAIOAuditRequest)(nil),                              // 83: listing_products.v1.GetAIOAuditRequest
	(*GetAllAIOAuditScoreResultsRequest)(nil),               // 84: listing_products.v1.GetAllAIOAuditScoreResultsRequest
	(*GetAIOAuditStatusRequest)(nil),                        // 85: listing_products.v1.GetAIOAuditStatusRequest
	(*TriggerAIOAuditRequest)(nil),                          // 86: listing_products.v1.TriggerAIOAuditRequest
	(*GetSEODataSummaryRequest)(nil),                        // 87: listing_products.v1.GetSEODataSummaryRequest
	(*GetLocalSearchSEODataRequest)(nil),                    // 88: listing_products.v1.GetLocalSearchSEODataRequest
	(*StartLocalSEODataWorkflowRequest)(nil),                // 89: listing_products.v1.StartLocalSEODataWorkflowRequest
	(*SaveSEOSettingsRequest)(nil),                          // 90: listing_products.v1.SaveSEOSettingsRequest
	(*GetSEOSettingsRequest)(nil),                           // 91: listing_products.v1.GetSEOSettingsRequest
	(*GetActiveSEOAddonsRequest)(nil),                       // 92: listing_products.v1.GetActiveSEOAddonsRequest
	(*StartSEOCategoryWorkflowRequest)(nil),                 // 93: listing_products.v1.StartSEOCategoryWorkflowRequest
	(*GetDataForSEOCategoryRequest)(nil),                    // 94: listing_products.v1.GetDataForSEOCategoryRequest
	(*StartCitationWorkflowRequest)(nil),                    // 95: listing_products.v1.StartCitationWorkflowRequest
	(*GetCitationDataRequest)(nil),                          // 96: listing_products.v1.GetCitationDataRequest
	(*DeleteCitationsRequest)(nil),                          // 97: listing_products.v1.DeleteCitationsRequest
	(*GetCitationSummaryRequest)(nil),                       // 98: listing_products.v1.GetCitationSummaryRequest
	(*ListCitationDomainsRequest)(nil),                      // 99: listing_products.v1.ListCitationDomainsRequest
	(*CreatePartnerSettingsRequest)(nil),                    // 100: listing_products.v1.CreatePartnerSettingsRequest
	(*GetPartnerSettingsRequest)(nil),                       // 101: listing_products.v1.GetPartnerSettingsRequest
	(*UpsertPartnerSettingsRequest)(nil),                    // 102: listing_products.v1.UpsertPartnerSettingsRequest
	(*GetConfigurationRequest)(nil),                         // 103: listing_products.v1.GetConfigurationRequest
	(*GetOrGenerateSEOSuggestedKeywordsRequest)(nil),        // 104: listing_products.v1.GetOrGenerateSEOSuggestedKeywordsRequest
	(*GetSEOSuggestedKeywordsRequest)(nil),                  // 105: listing_products.v1.GetSEOSuggestedKeywordsRequest
	(*GetGoogleMyBusinessInsightsDataResponse)(nil),         // 106: listing_products.v1.GetGoogleMyBusinessInsightsDataResponse
	(*GetGoogleMyBusinessInsightsDataBucketedResponse)(nil), // 107: listing_products.v1.GetGoogleMyBusinessInsightsDataBucketedResponse
	(*emptypb.Empty)(nil),                                   // 108: google.protobuf.Empty
	(*SubmitTurboListerResponse)(nil),                       // 109: listing_products.v1.SubmitTurboListerResponse
	(*GetAddonAttributesResponse)(nil),                      // 110: listing_products.v1.GetAddonAttributesResponse
	(*GetMultiAddonAttributesResponse)(nil),                 // 111: listing_products.v1.GetMultiAddonAttributesResponse
	(*GetSyncDataResponse)(nil),                             // 112: listing_products.v1.GetSyncDataResponse
	(*GetSyndicationInfoResponse)(nil),                      // 113: listing_products.v1.GetSyndicationInfoResponse
	(*GetListingSourcesResponse)(nil),                       // 114: listing_products.v1.GetListingSourcesResponse
	(*GetListingSourceByIdResponse)(nil),                    // 115: listing_products.v1.GetListingSourceByIdResponse
	(*GetListingSourceByProviderIdResponse)(nil),            // 116: listing_products.v1.GetListingSourceByProviderIdResponse
	(*GetPartnerSourcesResponse)(nil),                       // 117: listing_products.v1.GetPartnerSourcesResponse
	(*UpdateListingSourceResponse)(nil),                     // 118: listing_products.v1.UpdateListingSourceResponse
	(*CreateListingProfileResponse)(nil),                    // 119: listing_products.v1.CreateListingProfileResponse
	(*GetMultiListingProfileResponse)(nil),                  // 120: listing_products.v1.GetMultiListingProfileResponse
	(*GetMoreHoursTypesResponse)(nil),                       // 121: listing_products.v1.GetMoreHoursTypesResponse
	(*GetAttributeMetadataResponse)(nil),                    // 122: listing_products.v1.GetAttributeMetadataResponse
	(*GetMultiAccountGroupResponse)(nil),                    // 123: listing_products.v1.GetMultiAccountGroupResponse
	(*GetSuggestionResponse)(nil),                           // 124: listing_products.v1.GetSuggestionResponse
	(*GetMultiSuggestionResponse)(nil),                      // 125: listing_products.v1.GetMultiSuggestionResponse
	(*ListSuggestionResponse)(nil),                          // 126: listing_products.v1.ListSuggestionResponse
	(*SuggestFieldUpdateResponse)(nil),                      // 127: listing_products.v1.SuggestFieldUpdateResponse
	(*GetSEODataResponse)(nil),                              // 128: listing_products.v1.GetSEODataResponse
	(*GetAllAIOAuditResponse)(nil),                          // 129: listing_products.v1.GetAllAIOAuditResponse
	(*GetAIOAuditResponse)(nil),                             // 130: listing_products.v1.GetAIOAuditResponse
	(*GetAllAIOAuditScoreResultsResponse)(nil),              // 131: listing_products.v1.GetAllAIOAuditScoreResultsResponse
	(*GetAIOAuditStatusResponse)(nil),                       // 132: listing_products.v1.GetAIOAuditStatusResponse
	(*TriggerAIOAuditResponse)(nil),                         // 133: listing_products.v1.TriggerAIOAuditResponse
	(*GetSEODataSummaryResponse)(nil),                       // 134: listing_products.v1.GetSEODataSummaryResponse
	(*GetLocalSearchSEODataResponse)(nil),                   // 135: listing_products.v1.GetLocalSearchSEODataResponse
	(*SEOSettingsResponse)(nil),                             // 136: listing_products.v1.SEOSettingsResponse
	(*GetActiveSEOAddonsResponse)(nil),                      // 137: listing_products.v1.GetActiveSEOAddonsResponse
	(*GetDataForSEOCategoryResponse)(nil),                   // 138: listing_products.v1.GetDataForSEOCategoryResponse
	(*GetCitationDataResponse)(nil),                         // 139: listing_products.v1.GetCitationDataResponse
	(*GetCitationSummaryResponse)(nil),                      // 140: listing_products.v1.GetCitationSummaryResponse
	(*ListCitationDomainsResponse)(nil),                     // 141: listing_products.v1.ListCitationDomainsResponse
	(*GetPartnerSettingsResponse)(nil),                      // 142: listing_products.v1.GetPartnerSettingsResponse
	(*GetConfigurationResponse)(nil),                        // 143: listing_products.v1.GetConfigurationResponse
	(*GetOrGenerateSEOSuggestedKeywordsResponse)(nil),       // 144: listing_products.v1.GetOrGenerateSEOSuggestedKeywordsResponse
	(*GetSEOSuggestedKeywordsResponse)(nil),                 // 145: listing_products.v1.GetSEOSuggestedKeywordsResponse
}
var file_listing_products_v1_api_proto_depIdxs = []int32{
	42,  // 0: listing_products.v1.GetListingDistributionActivationStatusResponse.activation:type_name -> google.protobuf.Timestamp
	41,  // 1: listing_products.v1.BulkConnectLocationsForGoogleUserResponse.connections:type_name -> listing_products.v1.BulkConnectLocationsForGoogleUserResponse.ConnectionsEntry
	43,  // 2: listing_products.v1.DirectSyncSource.account:type_name -> listing_products.v1.ConnectedDirectSyncAccount
	44,  // 3: listing_products.v1.DirectSyncSource.accuracy:type_name -> listing_products.v1.SourceAccuracy
	45,  // 4: listing_products.v1.DirectSyncSource.sync_status:type_name -> listing_products.v1.SyndicationStatus
	43,  // 5: listing_products.v1.DirectSyncSource.additional_accounts:type_name -> listing_products.v1.ConnectedDirectSyncAccount
	22,  // 6: listing_products.v1.GetDirectSyncSourceInfoResponse.direct_submit_sources:type_name -> listing_products.v1.DirectSyncSource
	22,  // 7: listing_products.v1.GetLDDataResponse.neustar:type_name -> listing_products.v1.DirectSyncSource
	22,  // 8: listing_products.v1.GetLDDataResponse.data_axle:type_name -> listing_products.v1.DirectSyncSource
	22,  // 9: listing_products.v1.GetLDDataResponse.foursquare:type_name -> listing_products.v1.DirectSyncSource
	42,  // 10: listing_products.v1.NeustarPublication.last_delivery_time:type_name -> google.protobuf.Timestamp
	42,  // 11: listing_products.v1.NeustarPublication.last_modified:type_name -> google.protobuf.Timestamp
	31,  // 12: listing_products.v1.GetNeustarPublicationsResponse.publications:type_name -> listing_products.v1.NeustarPublication
	46,  // 13: listing_products.v1.GetDoctorDotComCategoriesResponse.categories:type_name -> listing_products.v1.DoctorDotComCategory
	42,  // 14: listing_products.v1.FieldStatus.updated:type_name -> google.protobuf.Timestamp
	36,  // 15: listing_products.v1.GetBusinessProfileFieldStatusResponse.company_name:type_name -> listing_products.v1.FieldStatus
	36,  // 16: listing_products.v1.GetBusinessProfileFieldStatusResponse.address:type_name -> listing_products.v1.FieldStatus
	36,  // 17: listing_products.v1.GetBusinessProfileFieldStatusResponse.address2:type_name -> listing_products.v1.FieldStatus
	36,  // 18: listing_products.v1.GetBusinessProfileFieldStatusResponse.city:type_name -> listing_products.v1.FieldStatus
	36,  // 19: listing_products.v1.GetBusinessProfileFieldStatusResponse.state:type_name -> listing_products.v1.FieldStatus
	36,  // 20: listing_products.v1.GetBusinessProfileFieldStatusResponse.zip:type_name -> listing_products.v1.FieldStatus
	36,  // 21: listing_products.v1.GetBusinessProfileFieldStatusResponse.country:type_name -> listing_products.v1.FieldStatus
	36,  // 22: listing_products.v1.GetBusinessProfileFieldStatusResponse.work_number:type_name -> listing_products.v1.FieldStatus
	36,  // 23: listing_products.v1.GetBusinessProfileFieldStatusResponse.call_tracking_number:type_name -> listing_products.v1.FieldStatus
	36,  // 24: listing_products.v1.GetBusinessProfileFieldStatusResponse.website:type_name -> listing_products.v1.FieldStatus
	36,  // 25: listing_products.v1.GetBusinessProfileFieldStatusResponse.location:type_name -> listing_products.v1.FieldStatus
	36,  // 26: listing_products.v1.GetBusinessProfileFieldStatusResponse.sales_person_id:type_name -> listing_products.v1.FieldStatus
	36,  // 27: listing_products.v1.GetBusinessProfileFieldStatusResponse.additional_sales_person_id:type_name -> listing_products.v1.FieldStatus
	36,  // 28: listing_products.v1.GetBusinessProfileFieldStatusResponse.vcategory_ids:type_name -> listing_products.v1.FieldStatus
	36,  // 29: listing_products.v1.GetBusinessProfileFieldStatusResponse.customer_identifier:type_name -> listing_products.v1.FieldStatus
	36,  // 30: listing_products.v1.GetBusinessProfileFieldStatusResponse.lifecycle_stage:type_name -> listing_products.v1.FieldStatus
	42,  // 31: listing_products.v1.GetBusinessProfileFieldStatusResponse.created:type_name -> google.protobuf.Timestamp
	42,  // 32: listing_products.v1.GetBusinessProfileFieldStatusResponse.updated:type_name -> google.protobuf.Timestamp
	42,  // 33: listing_products.v1.GetBusinessProfileFieldStatusResponse.deleted:type_name -> google.protobuf.Timestamp
	47,  // 34: listing_products.v1.ListingProductsService.GetGoogleMyBusinessInsightsData:input_type -> listing_products.v1.GetGoogleMyBusinessInsightsDataRequest
	48,  // 35: listing_products.v1.ListingProductsService.GetGoogleMyBusinessInsightsDataBucketed:input_type -> listing_products.v1.GetGoogleMyBusinessInsightsDataBucketedRequest
	38,  // 36: listing_products.v1.ListingProductsService.UpdateVendorSyncFlag:input_type -> listing_products.v1.UpdateVendorSyncFlagRequest
	49,  // 37: listing_products.v1.ListingProductsService.SubmitTurboLister:input_type -> listing_products.v1.SubmitTurboListerRequest
	50,  // 38: listing_products.v1.ListingProductsService.GetAddonAttributes:input_type -> listing_products.v1.GetAddonAttributesRequest
	51,  // 39: listing_products.v1.ListingProductsService.CreateAddonAttributes:input_type -> listing_products.v1.CreateAddonAttributesRequest
	52,  // 40: listing_products.v1.ListingProductsService.UpdateAddonAttributes:input_type -> listing_products.v1.UpdateAddonAttributesRequest
	53,  // 41: listing_products.v1.ListingProductsService.DeleteAddonAttributes:input_type -> listing_products.v1.DeleteAddonAttributesRequest
	54,  // 42: listing_products.v1.ListingProductsService.GetMultiAddonAttributes:input_type -> listing_products.v1.GetMultiAddonAttributesRequest
	17,  // 43: listing_products.v1.ListingProductsService.BulkConnectLocationsForGoogleUser:input_type -> listing_products.v1.BulkConnectLocationsForGoogleUserRequest
	0,   // 44: listing_products.v1.ListingProductsService.ManuallyFixYextProvisioning:input_type -> listing_products.v1.ManuallyFixYextProvisioningRequest
	2,   // 45: listing_products.v1.ListingProductsService.ManuallyProvisionUberall:input_type -> listing_products.v1.ManuallyProvisionUberallRequest
	1,   // 46: listing_products.v1.ListingProductsService.ManuallyDeactivateUberallProvisioning:input_type -> listing_products.v1.ManuallyDeactivateUberallProvisioningRequest
	3,   // 47: listing_products.v1.ListingProductsService.GetGoogleBusinessInfo:input_type -> listing_products.v1.GetGoogleBusinessInfoRequest
	5,   // 48: listing_products.v1.ListingProductsService.GetFacebookPageInfo:input_type -> listing_products.v1.GetFacebookPageInfoRequest
	7,   // 49: listing_products.v1.ListingProductsService.GetAppleBusinessConnectInfo:input_type -> listing_products.v1.GetAppleBusinessConnectInfoRequest
	9,   // 50: listing_products.v1.ListingProductsService.GetBingPlacesInfo:input_type -> listing_products.v1.GetBingPlacesInfoRequest
	11,  // 51: listing_products.v1.ListingProductsService.TriggerBingInsightsBackFill:input_type -> listing_products.v1.TriggerBingInsightsBackFillRequest
	12,  // 52: listing_products.v1.ListingProductsService.CheckSubmissionStatus:input_type -> listing_products.v1.CheckSubmissionStatusRequest
	13,  // 53: listing_products.v1.ListingProductsService.GetInfoGroupId:input_type -> listing_products.v1.GetInfoGroupIdRequest
	15,  // 54: listing_products.v1.ListingProductsService.GetListingDistributionActivationStatus:input_type -> listing_products.v1.GetListingDistributionActivationStatusRequest
	21,  // 55: listing_products.v1.ListingProductsService.GetDirectSyncSourceInfo:input_type -> listing_products.v1.GetDirectSyncSourceInfoRequest
	55,  // 56: listing_products.v1.ListingProductsService.GetSyncData:input_type -> listing_products.v1.GetSyncDataRequest
	56,  // 57: listing_products.v1.ListingProductsService.GetSyndicationInfo:input_type -> listing_products.v1.GetSyndicationInfoRequest
	19,  // 58: listing_products.v1.ListingProductsService.GetProductSettings:input_type -> listing_products.v1.GetProductSettingsRequest
	33,  // 59: listing_products.v1.ListingProductsService.GetDoctorDotComCategories:input_type -> listing_products.v1.GetDoctorDotComCategoriesRequest
	57,  // 60: listing_products.v1.ListingProductsService.FetchAndSubmitSourceURLsToCS:input_type -> listing_products.v1.FetchAndSubmitSourceURLsToCSRequest
	24,  // 61: listing_products.v1.ListingProductsService.StartBusinessDataScoreWorkflow:input_type -> listing_products.v1.StartBusinessDataScoreWorkflowRequest
	25,  // 62: listing_products.v1.ListingProductsService.IsPartnerUser:input_type -> listing_products.v1.IsPartnerUserRequest
	39,  // 63: listing_products.v1.ListingProductsService.IsLocalSEOProActiveForAccount:input_type -> listing_products.v1.IsLocalSEOProActiveForAccountRequest
	27,  // 64: listing_products.v1.ListingProductsService.GetLDData:input_type -> listing_products.v1.GetLDDataRequest
	29,  // 65: listing_products.v1.ListingProductsService.SyncToSources:input_type -> listing_products.v1.SyncToSourcesRequest
	30,  // 66: listing_products.v1.ListingProductsService.GetNeustarPublications:input_type -> listing_products.v1.GetNeustarPublicationsRequest
	58,  // 67: listing_products.v1.ListingSourceService.GetListingSources:input_type -> listing_products.v1.GetListingSourcesRequest
	59,  // 68: listing_products.v1.ListingSourceService.GetListingSourceById:input_type -> listing_products.v1.GetListingSourceByIdRequest
	60,  // 69: listing_products.v1.ListingSourceService.GetListingSourceByProviderId:input_type -> listing_products.v1.GetListingSourceByProviderIdRequest
	61,  // 70: listing_products.v1.ListingSourceService.GetPartnerSources:input_type -> listing_products.v1.GetPartnerSourcesRequest
	62,  // 71: listing_products.v1.ListingSourceService.CreateListingSource:input_type -> listing_products.v1.CreateListingSourceRequest
	63,  // 72: listing_products.v1.ListingSourceService.DeleteListingSource:input_type -> listing_products.v1.DeleteListingSourceRequest
	64,  // 73: listing_products.v1.ListingSourceService.UndeleteListingSource:input_type -> listing_products.v1.UndeleteListingSourceRequest
	65,  // 74: listing_products.v1.ListingSourceService.UpdateListingSource:input_type -> listing_products.v1.UpdateListingSourceRequest
	66,  // 75: listing_products.v1.ListingProfileService.Create:input_type -> listing_products.v1.CreateListingProfileRequest
	67,  // 76: listing_products.v1.ListingProfileService.GetMulti:input_type -> listing_products.v1.GetMultiListingProfileRequest
	68,  // 77: listing_products.v1.ListingProfileService.Update:input_type -> listing_products.v1.UpdateListingProfileRequest
	69,  // 78: listing_products.v1.ListingProfileService.Delete:input_type -> listing_products.v1.DeleteListingProfileRequest
	70,  // 79: listing_products.v1.ListingProfileService.GetMoreHoursTypes:input_type -> listing_products.v1.GetMoreHoursTypesRequest
	71,  // 80: listing_products.v1.ListingProfileService.GetAttributeMetadata:input_type -> listing_products.v1.GetAttributeMetadataRequest
	33,  // 81: listing_products.v1.ListingProfileService.GetDoctorDotComCategories:input_type -> listing_products.v1.GetDoctorDotComCategoriesRequest
	72,  // 82: listing_products.v1.ListingProfileService.LegacyAPICreate:input_type -> listing_products.v1.LegacyAPICreateRequest
	73,  // 83: listing_products.v1.ListingProfileService.LegacyAPIUpdate:input_type -> listing_products.v1.LegacyAPIUpdateRequest
	35,  // 84: listing_products.v1.ListingProfileService.GetBusinessProfileFieldStatus:input_type -> listing_products.v1.GetBusinessProfileFieldStatusRequest
	74,  // 85: listing_products.v1.ListingProfileService.GetMultiAccountGroup:input_type -> listing_products.v1.GetMultiAccountGroupRequest
	75,  // 86: listing_products.v1.SuggestionService.Upsert:input_type -> listing_products.v1.UpsertSuggestionRequest
	76,  // 87: listing_products.v1.SuggestionService.GetSuggestion:input_type -> listing_products.v1.GetSuggestionRequest
	77,  // 88: listing_products.v1.SuggestionService.GetMulti:input_type -> listing_products.v1.GetMultiSuggestionRequest
	78,  // 89: listing_products.v1.SuggestionService.Delete:input_type -> listing_products.v1.DeleteSuggestionRequest
	79,  // 90: listing_products.v1.SuggestionService.List:input_type -> listing_products.v1.ListSuggestionRequest
	80,  // 91: listing_products.v1.SuggestionService.SuggestFieldUpdate:input_type -> listing_products.v1.SuggestFieldUpdateRequest
	81,  // 92: listing_products.v1.SEOService.GetSEOData:input_type -> listing_products.v1.GetSEODataRequest
	82,  // 93: listing_products.v1.SEOService.GetAllAIOAudit:input_type -> listing_products.v1.GetAllAIOAuditRequest
	83,  // 94: listing_products.v1.SEOService.GetAIOAudit:input_type -> listing_products.v1.GetAIOAuditRequest
	84,  // 95: listing_products.v1.SEOService.GetAllAIOAuditScoreResults:input_type -> listing_products.v1.GetAllAIOAuditScoreResultsRequest
	85,  // 96: listing_products.v1.SEOService.GetAIOAuditStatus:input_type -> listing_products.v1.GetAIOAuditStatusRequest
	86,  // 97: listing_products.v1.SEOService.TriggerAIOAudit:input_type -> listing_products.v1.TriggerAIOAuditRequest
	87,  // 98: listing_products.v1.SEOService.GetSEODataSummary:input_type -> listing_products.v1.GetSEODataSummaryRequest
	88,  // 99: listing_products.v1.SEOService.GetLocalSearchSEOData:input_type -> listing_products.v1.GetLocalSearchSEODataRequest
	89,  // 100: listing_products.v1.SEOService.StartLocalSEODataWorkflow:input_type -> listing_products.v1.StartLocalSEODataWorkflowRequest
	90,  // 101: listing_products.v1.SEOService.SaveSEOSettings:input_type -> listing_products.v1.SaveSEOSettingsRequest
	91,  // 102: listing_products.v1.SEOService.GetSEOSettings:input_type -> listing_products.v1.GetSEOSettingsRequest
	92,  // 103: listing_products.v1.SEOService.GetActiveSEOAddons:input_type -> listing_products.v1.GetActiveSEOAddonsRequest
	93,  // 104: listing_products.v1.SEOService.StartSEOCategoryWorkflow:input_type -> listing_products.v1.StartSEOCategoryWorkflowRequest
	94,  // 105: listing_products.v1.SEOService.GetDataForSEOCategory:input_type -> listing_products.v1.GetDataForSEOCategoryRequest
	95,  // 106: listing_products.v1.Citations.StartCitationWorkflow:input_type -> listing_products.v1.StartCitationWorkflowRequest
	96,  // 107: listing_products.v1.Citations.GetCitationData:input_type -> listing_products.v1.GetCitationDataRequest
	97,  // 108: listing_products.v1.Citations.DeleteCitations:input_type -> listing_products.v1.DeleteCitationsRequest
	98,  // 109: listing_products.v1.Citations.GetCitationSummary:input_type -> listing_products.v1.GetCitationSummaryRequest
	99,  // 110: listing_products.v1.Citations.ListCitationDomains:input_type -> listing_products.v1.ListCitationDomainsRequest
	100, // 111: listing_products.v1.PartnerSettingsService.CreatePartnerSettings:input_type -> listing_products.v1.CreatePartnerSettingsRequest
	101, // 112: listing_products.v1.PartnerSettingsService.GetPartnerSettings:input_type -> listing_products.v1.GetPartnerSettingsRequest
	102, // 113: listing_products.v1.PartnerSettingsService.UpsertPartnerSettings:input_type -> listing_products.v1.UpsertPartnerSettingsRequest
	103, // 114: listing_products.v1.PartnerSettingsService.GetConfiguration:input_type -> listing_products.v1.GetConfigurationRequest
	104, // 115: listing_products.v1.SEOSuggestedKeywordsService.GetOrGenerateSEOSuggestedKeywords:input_type -> listing_products.v1.GetOrGenerateSEOSuggestedKeywordsRequest
	105, // 116: listing_products.v1.SEOSuggestedKeywordsService.GetSEOSuggestedKeywords:input_type -> listing_products.v1.GetSEOSuggestedKeywordsRequest
	106, // 117: listing_products.v1.ListingProductsService.GetGoogleMyBusinessInsightsData:output_type -> listing_products.v1.GetGoogleMyBusinessInsightsDataResponse
	107, // 118: listing_products.v1.ListingProductsService.GetGoogleMyBusinessInsightsDataBucketed:output_type -> listing_products.v1.GetGoogleMyBusinessInsightsDataBucketedResponse
	108, // 119: listing_products.v1.ListingProductsService.UpdateVendorSyncFlag:output_type -> google.protobuf.Empty
	109, // 120: listing_products.v1.ListingProductsService.SubmitTurboLister:output_type -> listing_products.v1.SubmitTurboListerResponse
	110, // 121: listing_products.v1.ListingProductsService.GetAddonAttributes:output_type -> listing_products.v1.GetAddonAttributesResponse
	108, // 122: listing_products.v1.ListingProductsService.CreateAddonAttributes:output_type -> google.protobuf.Empty
	108, // 123: listing_products.v1.ListingProductsService.UpdateAddonAttributes:output_type -> google.protobuf.Empty
	108, // 124: listing_products.v1.ListingProductsService.DeleteAddonAttributes:output_type -> google.protobuf.Empty
	111, // 125: listing_products.v1.ListingProductsService.GetMultiAddonAttributes:output_type -> listing_products.v1.GetMultiAddonAttributesResponse
	18,  // 126: listing_products.v1.ListingProductsService.BulkConnectLocationsForGoogleUser:output_type -> listing_products.v1.BulkConnectLocationsForGoogleUserResponse
	108, // 127: listing_products.v1.ListingProductsService.ManuallyFixYextProvisioning:output_type -> google.protobuf.Empty
	108, // 128: listing_products.v1.ListingProductsService.ManuallyProvisionUberall:output_type -> google.protobuf.Empty
	108, // 129: listing_products.v1.ListingProductsService.ManuallyDeactivateUberallProvisioning:output_type -> google.protobuf.Empty
	4,   // 130: listing_products.v1.ListingProductsService.GetGoogleBusinessInfo:output_type -> listing_products.v1.GetGoogleBusinessInfoResponse
	6,   // 131: listing_products.v1.ListingProductsService.GetFacebookPageInfo:output_type -> listing_products.v1.GetFacebookPageInfoResponse
	8,   // 132: listing_products.v1.ListingProductsService.GetAppleBusinessConnectInfo:output_type -> listing_products.v1.GetAppleBusinessConnectInfoResponse
	10,  // 133: listing_products.v1.ListingProductsService.GetBingPlacesInfo:output_type -> listing_products.v1.GetBingPlacesInfoResponse
	108, // 134: listing_products.v1.ListingProductsService.TriggerBingInsightsBackFill:output_type -> google.protobuf.Empty
	108, // 135: listing_products.v1.ListingProductsService.CheckSubmissionStatus:output_type -> google.protobuf.Empty
	14,  // 136: listing_products.v1.ListingProductsService.GetInfoGroupId:output_type -> listing_products.v1.GetInfoGroupIdResponse
	16,  // 137: listing_products.v1.ListingProductsService.GetListingDistributionActivationStatus:output_type -> listing_products.v1.GetListingDistributionActivationStatusResponse
	23,  // 138: listing_products.v1.ListingProductsService.GetDirectSyncSourceInfo:output_type -> listing_products.v1.GetDirectSyncSourceInfoResponse
	112, // 139: listing_products.v1.ListingProductsService.GetSyncData:output_type -> listing_products.v1.GetSyncDataResponse
	113, // 140: listing_products.v1.ListingProductsService.GetSyndicationInfo:output_type -> listing_products.v1.GetSyndicationInfoResponse
	20,  // 141: listing_products.v1.ListingProductsService.GetProductSettings:output_type -> listing_products.v1.GetProductSettingsResponse
	34,  // 142: listing_products.v1.ListingProductsService.GetDoctorDotComCategories:output_type -> listing_products.v1.GetDoctorDotComCategoriesResponse
	108, // 143: listing_products.v1.ListingProductsService.FetchAndSubmitSourceURLsToCS:output_type -> google.protobuf.Empty
	108, // 144: listing_products.v1.ListingProductsService.StartBusinessDataScoreWorkflow:output_type -> google.protobuf.Empty
	26,  // 145: listing_products.v1.ListingProductsService.IsPartnerUser:output_type -> listing_products.v1.IsPartnerUserResponse
	40,  // 146: listing_products.v1.ListingProductsService.IsLocalSEOProActiveForAccount:output_type -> listing_products.v1.IsLocalSEOProActiveForAccountResponse
	28,  // 147: listing_products.v1.ListingProductsService.GetLDData:output_type -> listing_products.v1.GetLDDataResponse
	108, // 148: listing_products.v1.ListingProductsService.SyncToSources:output_type -> google.protobuf.Empty
	32,  // 149: listing_products.v1.ListingProductsService.GetNeustarPublications:output_type -> listing_products.v1.GetNeustarPublicationsResponse
	114, // 150: listing_products.v1.ListingSourceService.GetListingSources:output_type -> listing_products.v1.GetListingSourcesResponse
	115, // 151: listing_products.v1.ListingSourceService.GetListingSourceById:output_type -> listing_products.v1.GetListingSourceByIdResponse
	116, // 152: listing_products.v1.ListingSourceService.GetListingSourceByProviderId:output_type -> listing_products.v1.GetListingSourceByProviderIdResponse
	117, // 153: listing_products.v1.ListingSourceService.GetPartnerSources:output_type -> listing_products.v1.GetPartnerSourcesResponse
	108, // 154: listing_products.v1.ListingSourceService.CreateListingSource:output_type -> google.protobuf.Empty
	108, // 155: listing_products.v1.ListingSourceService.DeleteListingSource:output_type -> google.protobuf.Empty
	108, // 156: listing_products.v1.ListingSourceService.UndeleteListingSource:output_type -> google.protobuf.Empty
	118, // 157: listing_products.v1.ListingSourceService.UpdateListingSource:output_type -> listing_products.v1.UpdateListingSourceResponse
	119, // 158: listing_products.v1.ListingProfileService.Create:output_type -> listing_products.v1.CreateListingProfileResponse
	120, // 159: listing_products.v1.ListingProfileService.GetMulti:output_type -> listing_products.v1.GetMultiListingProfileResponse
	108, // 160: listing_products.v1.ListingProfileService.Update:output_type -> google.protobuf.Empty
	108, // 161: listing_products.v1.ListingProfileService.Delete:output_type -> google.protobuf.Empty
	121, // 162: listing_products.v1.ListingProfileService.GetMoreHoursTypes:output_type -> listing_products.v1.GetMoreHoursTypesResponse
	122, // 163: listing_products.v1.ListingProfileService.GetAttributeMetadata:output_type -> listing_products.v1.GetAttributeMetadataResponse
	34,  // 164: listing_products.v1.ListingProfileService.GetDoctorDotComCategories:output_type -> listing_products.v1.GetDoctorDotComCategoriesResponse
	108, // 165: listing_products.v1.ListingProfileService.LegacyAPICreate:output_type -> google.protobuf.Empty
	108, // 166: listing_products.v1.ListingProfileService.LegacyAPIUpdate:output_type -> google.protobuf.Empty
	37,  // 167: listing_products.v1.ListingProfileService.GetBusinessProfileFieldStatus:output_type -> listing_products.v1.GetBusinessProfileFieldStatusResponse
	123, // 168: listing_products.v1.ListingProfileService.GetMultiAccountGroup:output_type -> listing_products.v1.GetMultiAccountGroupResponse
	108, // 169: listing_products.v1.SuggestionService.Upsert:output_type -> google.protobuf.Empty
	124, // 170: listing_products.v1.SuggestionService.GetSuggestion:output_type -> listing_products.v1.GetSuggestionResponse
	125, // 171: listing_products.v1.SuggestionService.GetMulti:output_type -> listing_products.v1.GetMultiSuggestionResponse
	108, // 172: listing_products.v1.SuggestionService.Delete:output_type -> google.protobuf.Empty
	126, // 173: listing_products.v1.SuggestionService.List:output_type -> listing_products.v1.ListSuggestionResponse
	127, // 174: listing_products.v1.SuggestionService.SuggestFieldUpdate:output_type -> listing_products.v1.SuggestFieldUpdateResponse
	128, // 175: listing_products.v1.SEOService.GetSEOData:output_type -> listing_products.v1.GetSEODataResponse
	129, // 176: listing_products.v1.SEOService.GetAllAIOAudit:output_type -> listing_products.v1.GetAllAIOAuditResponse
	130, // 177: listing_products.v1.SEOService.GetAIOAudit:output_type -> listing_products.v1.GetAIOAuditResponse
	131, // 178: listing_products.v1.SEOService.GetAllAIOAuditScoreResults:output_type -> listing_products.v1.GetAllAIOAuditScoreResultsResponse
	132, // 179: listing_products.v1.SEOService.GetAIOAuditStatus:output_type -> listing_products.v1.GetAIOAuditStatusResponse
	133, // 180: listing_products.v1.SEOService.TriggerAIOAudit:output_type -> listing_products.v1.TriggerAIOAuditResponse
	134, // 181: listing_products.v1.SEOService.GetSEODataSummary:output_type -> listing_products.v1.GetSEODataSummaryResponse
	135, // 182: listing_products.v1.SEOService.GetLocalSearchSEOData:output_type -> listing_products.v1.GetLocalSearchSEODataResponse
	108, // 183: listing_products.v1.SEOService.StartLocalSEODataWorkflow:output_type -> google.protobuf.Empty
	108, // 184: listing_products.v1.SEOService.SaveSEOSettings:output_type -> google.protobuf.Empty
	136, // 185: listing_products.v1.SEOService.GetSEOSettings:output_type -> listing_products.v1.SEOSettingsResponse
	137, // 186: listing_products.v1.SEOService.GetActiveSEOAddons:output_type -> listing_products.v1.GetActiveSEOAddonsResponse
	108, // 187: listing_products.v1.SEOService.StartSEOCategoryWorkflow:output_type -> google.protobuf.Empty
	138, // 188: listing_products.v1.SEOService.GetDataForSEOCategory:output_type -> listing_products.v1.GetDataForSEOCategoryResponse
	108, // 189: listing_products.v1.Citations.StartCitationWorkflow:output_type -> google.protobuf.Empty
	139, // 190: listing_products.v1.Citations.GetCitationData:output_type -> listing_products.v1.GetCitationDataResponse
	108, // 191: listing_products.v1.Citations.DeleteCitations:output_type -> google.protobuf.Empty
	140, // 192: listing_products.v1.Citations.GetCitationSummary:output_type -> listing_products.v1.GetCitationSummaryResponse
	141, // 193: listing_products.v1.Citations.ListCitationDomains:output_type -> listing_products.v1.ListCitationDomainsResponse
	108, // 194: listing_products.v1.PartnerSettingsService.CreatePartnerSettings:output_type -> google.protobuf.Empty
	142, // 195: listing_products.v1.PartnerSettingsService.GetPartnerSettings:output_type -> listing_products.v1.GetPartnerSettingsResponse
	108, // 196: listing_products.v1.PartnerSettingsService.UpsertPartnerSettings:output_type -> google.protobuf.Empty
	143, // 197: listing_products.v1.PartnerSettingsService.GetConfiguration:output_type -> listing_products.v1.GetConfigurationResponse
	144, // 198: listing_products.v1.SEOSuggestedKeywordsService.GetOrGenerateSEOSuggestedKeywords:output_type -> listing_products.v1.GetOrGenerateSEOSuggestedKeywordsResponse
	145, // 199: listing_products.v1.SEOSuggestedKeywordsService.GetSEOSuggestedKeywords:output_type -> listing_products.v1.GetSEOSuggestedKeywordsResponse
	117, // [117:200] is the sub-list for method output_type
	34,  // [34:117] is the sub-list for method input_type
	34,  // [34:34] is the sub-list for extension type_name
	34,  // [34:34] is the sub-list for extension extendee
	0,   // [0:34] is the sub-list for field type_name
}

func init() { file_listing_products_v1_api_proto_init() }
func file_listing_products_v1_api_proto_init() {
	if File_listing_products_v1_api_proto != nil {
		return
	}
	file_listing_products_v1_addon_attributes_proto_init()
	file_listing_products_v1_insights_proto_init()
	file_listing_products_v1_turbolister_proto_init()
	file_listing_products_v1_listing_sources_proto_init()
	file_listing_products_v1_listing_profile_proto_init()
	file_listing_products_v1_suggestions_proto_init()
	file_listing_products_v1_seo_proto_init()
	file_listing_products_v1_citations_proto_init()
	file_listing_products_v1_partnersettings_proto_init()
	file_listing_products_v1_seosuggestedkeywords_proto_init()
	file_listing_products_v1_syncdata_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_listing_products_v1_api_proto_rawDesc), len(file_listing_products_v1_api_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   42,
			NumExtensions: 0,
			NumServices:   8,
		},
		GoTypes:           file_listing_products_v1_api_proto_goTypes,
		DependencyIndexes: file_listing_products_v1_api_proto_depIdxs,
		MessageInfos:      file_listing_products_v1_api_proto_msgTypes,
	}.Build()
	File_listing_products_v1_api_proto = out.File
	file_listing_products_v1_api_proto_goTypes = nil
	file_listing_products_v1_api_proto_depIdxs = nil
}
