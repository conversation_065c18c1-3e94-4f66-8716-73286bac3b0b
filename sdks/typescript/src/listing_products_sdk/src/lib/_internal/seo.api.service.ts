// *********************************
// Code generated by sdkgen
// DO NOT EDIT!.
//
// API Service.
// *********************************
import {Injectable} from '@angular/core';
import {
        GetAIOAuditRequest,
        GetAIOAuditResponse,
        GetAIOAuditStatusRequest,
        GetAIOAuditStatusResponse,
        GetActiveSEOAddonsRequest,
        GetActiveSEOAddonsResponse,
        GetAllAIOAuditRequest,
        GetAllAIOAuditResponse,
        GetAllAIOAuditScoreResultsRequest,
        GetAllAIOAuditScoreResultsResponse,
        GetDataForSEOCategoryRequest,
        GetDataForSEOCategoryResponse,
        GetLocalSearchSEODataRequest,
        GetLocalSearchSEODataResponse,
        GetSEODataRequest,
        GetSEODataResponse,
        GetSEODataSummaryRequest,
        GetSEODataSummaryResponse,
        GetSEOSettingsRequest,
        SEOSettingsResponse,
        SaveSEOSettingsRequest,
        StartLocalSEODataWorkflowRequest,
        StartSEOCategoryWorkflowRequest,
        TriggerAIOAuditRequest,
        TriggerAIOAuditResponse,
} from './objects/';
import {
        GetAIOAuditRequestInterface,
        GetAIOAuditResponseInterface,
        GetAIOAuditStatusRequestInterface,
        GetAIOAuditStatusResponseInterface,
        GetActiveSEOAddonsRequestInterface,
        GetActiveSEOAddonsResponseInterface,
        GetAllAIOAuditRequestInterface,
        GetAllAIOAuditResponseInterface,
        GetAllAIOAuditScoreResultsRequestInterface,
        GetAllAIOAuditScoreResultsResponseInterface,
        GetDataForSEOCategoryRequestInterface,
        GetDataForSEOCategoryResponseInterface,
        GetLocalSearchSEODataRequestInterface,
        GetLocalSearchSEODataResponseInterface,
        GetSEODataRequestInterface,
        GetSEODataResponseInterface,
        GetSEODataSummaryRequestInterface,
        GetSEODataSummaryResponseInterface,
        GetSEOSettingsRequestInterface,
        SEOSettingsResponseInterface,
        SaveSEOSettingsRequestInterface,
        StartLocalSEODataWorkflowRequestInterface,
        StartSEOCategoryWorkflowRequestInterface,
        TriggerAIOAuditRequestInterface,
        TriggerAIOAuditResponseInterface,
} from './interfaces/';
import {HttpHeaders, HttpClient, HttpResponse} from '@angular/common/http';
import {inject} from '@angular/core';
import {HostService} from '../_generated/host.service';
import {Observable} from 'rxjs';
import {map} from 'rxjs/operators';

@Injectable({providedIn: 'root'})
export class SEOApiService {
    private readonly hostService = inject(HostService);
    private readonly http = inject(HttpClient);
    private _host = this.hostService.hostWithScheme;

    private apiOptions(): {headers: HttpHeaders, withCredentials: boolean} {
        return {
            headers: new HttpHeaders({
                'Content-Type': 'application/json'
            }),
            withCredentials: true
        };
    }

    getSeoData(r: GetSEODataRequest | GetSEODataRequestInterface): Observable<GetSEODataResponse> {
        const request = ((<GetSEODataRequest>r).toApiJson) ? (<GetSEODataRequest>r) : new GetSEODataRequest(r);
        return this.http.post<GetSEODataResponseInterface>(this._host + "/listing_products.v1.SEOService/GetSEOData", request.toApiJson(), this.apiOptions())
            .pipe(
                map(resp => GetSEODataResponse.fromProto(resp))
            );
    }
    getAllAioAudit(r: GetAllAIOAuditRequest | GetAllAIOAuditRequestInterface): Observable<GetAllAIOAuditResponse> {
        const request = ((<GetAllAIOAuditRequest>r).toApiJson) ? (<GetAllAIOAuditRequest>r) : new GetAllAIOAuditRequest(r);
        return this.http.post<GetAllAIOAuditResponseInterface>(this._host + "/listing_products.v1.SEOService/GetAllAIOAudit", request.toApiJson(), this.apiOptions())
            .pipe(
                map(resp => GetAllAIOAuditResponse.fromProto(resp))
            );
    }
    getAioAudit(r: GetAIOAuditRequest | GetAIOAuditRequestInterface): Observable<GetAIOAuditResponse> {
        const request = ((<GetAIOAuditRequest>r).toApiJson) ? (<GetAIOAuditRequest>r) : new GetAIOAuditRequest(r);
        return this.http.post<GetAIOAuditResponseInterface>(this._host + "/listing_products.v1.SEOService/GetAIOAudit", request.toApiJson(), this.apiOptions())
            .pipe(
                map(resp => GetAIOAuditResponse.fromProto(resp))
            );
    }
    getAllAioAuditScoreResults(r: GetAllAIOAuditScoreResultsRequest | GetAllAIOAuditScoreResultsRequestInterface): Observable<GetAllAIOAuditScoreResultsResponse> {
        const request = ((<GetAllAIOAuditScoreResultsRequest>r).toApiJson) ? (<GetAllAIOAuditScoreResultsRequest>r) : new GetAllAIOAuditScoreResultsRequest(r);
        return this.http.post<GetAllAIOAuditScoreResultsResponseInterface>(this._host + "/listing_products.v1.SEOService/GetAllAIOAuditScoreResults", request.toApiJson(), this.apiOptions())
            .pipe(
                map(resp => GetAllAIOAuditScoreResultsResponse.fromProto(resp))
            );
    }
    getAioAuditStatus(r: GetAIOAuditStatusRequest | GetAIOAuditStatusRequestInterface): Observable<GetAIOAuditStatusResponse> {
        const request = ((<GetAIOAuditStatusRequest>r).toApiJson) ? (<GetAIOAuditStatusRequest>r) : new GetAIOAuditStatusRequest(r);
        return this.http.post<GetAIOAuditStatusResponseInterface>(this._host + "/listing_products.v1.SEOService/GetAIOAuditStatus", request.toApiJson(), this.apiOptions())
            .pipe(
                map(resp => GetAIOAuditStatusResponse.fromProto(resp))
            );
    }
    triggerAioAudit(r: TriggerAIOAuditRequest | TriggerAIOAuditRequestInterface): Observable<TriggerAIOAuditResponse> {
        const request = ((<TriggerAIOAuditRequest>r).toApiJson) ? (<TriggerAIOAuditRequest>r) : new TriggerAIOAuditRequest(r);
        return this.http.post<TriggerAIOAuditResponseInterface>(this._host + "/listing_products.v1.SEOService/TriggerAIOAudit", request.toApiJson(), this.apiOptions())
            .pipe(
                map(resp => TriggerAIOAuditResponse.fromProto(resp))
            );
    }
    getSeoDataSummary(r: GetSEODataSummaryRequest | GetSEODataSummaryRequestInterface): Observable<GetSEODataSummaryResponse> {
        const request = ((<GetSEODataSummaryRequest>r).toApiJson) ? (<GetSEODataSummaryRequest>r) : new GetSEODataSummaryRequest(r);
        return this.http.post<GetSEODataSummaryResponseInterface>(this._host + "/listing_products.v1.SEOService/GetSEODataSummary", request.toApiJson(), this.apiOptions())
            .pipe(
                map(resp => GetSEODataSummaryResponse.fromProto(resp))
            );
    }
    getLocalSearchSeoData(r: GetLocalSearchSEODataRequest | GetLocalSearchSEODataRequestInterface): Observable<GetLocalSearchSEODataResponse> {
        const request = ((<GetLocalSearchSEODataRequest>r).toApiJson) ? (<GetLocalSearchSEODataRequest>r) : new GetLocalSearchSEODataRequest(r);
        return this.http.post<GetLocalSearchSEODataResponseInterface>(this._host + "/listing_products.v1.SEOService/GetLocalSearchSEOData", request.toApiJson(), this.apiOptions())
            .pipe(
                map(resp => GetLocalSearchSEODataResponse.fromProto(resp))
            );
    }
    startLocalSeoDataWorkflow(r: StartLocalSEODataWorkflowRequest | StartLocalSEODataWorkflowRequestInterface): Observable<HttpResponse<null>> {
        const request = ((<StartLocalSEODataWorkflowRequest>r).toApiJson) ? (<StartLocalSEODataWorkflowRequest>r) : new StartLocalSEODataWorkflowRequest(r);
        return this.http.post<null>(this._host + "/listing_products.v1.SEOService/StartLocalSEODataWorkflow", request.toApiJson(), {...this.apiOptions(), observe: 'response'});
    }
    saveSeoSettings(r: SaveSEOSettingsRequest | SaveSEOSettingsRequestInterface): Observable<HttpResponse<null>> {
        const request = ((<SaveSEOSettingsRequest>r).toApiJson) ? (<SaveSEOSettingsRequest>r) : new SaveSEOSettingsRequest(r);
        return this.http.post<null>(this._host + "/listing_products.v1.SEOService/SaveSEOSettings", request.toApiJson(), {...this.apiOptions(), observe: 'response'});
    }
    getSeoSettings(r: GetSEOSettingsRequest | GetSEOSettingsRequestInterface): Observable<SEOSettingsResponse> {
        const request = ((<GetSEOSettingsRequest>r).toApiJson) ? (<GetSEOSettingsRequest>r) : new GetSEOSettingsRequest(r);
        return this.http.post<SEOSettingsResponseInterface>(this._host + "/listing_products.v1.SEOService/GetSEOSettings", request.toApiJson(), this.apiOptions())
            .pipe(
                map(resp => SEOSettingsResponse.fromProto(resp))
            );
    }
    getActiveSeoAddons(r: GetActiveSEOAddonsRequest | GetActiveSEOAddonsRequestInterface): Observable<GetActiveSEOAddonsResponse> {
        const request = ((<GetActiveSEOAddonsRequest>r).toApiJson) ? (<GetActiveSEOAddonsRequest>r) : new GetActiveSEOAddonsRequest(r);
        return this.http.post<GetActiveSEOAddonsResponseInterface>(this._host + "/listing_products.v1.SEOService/GetActiveSEOAddons", request.toApiJson(), this.apiOptions())
            .pipe(
                map(resp => GetActiveSEOAddonsResponse.fromProto(resp))
            );
    }
    startSeoCategoryWorkflow(r: StartSEOCategoryWorkflowRequest | StartSEOCategoryWorkflowRequestInterface): Observable<HttpResponse<null>> {
        const request = ((<StartSEOCategoryWorkflowRequest>r).toApiJson) ? (<StartSEOCategoryWorkflowRequest>r) : new StartSEOCategoryWorkflowRequest(r);
        return this.http.post<null>(this._host + "/listing_products.v1.SEOService/StartSEOCategoryWorkflow", request.toApiJson(), {...this.apiOptions(), observe: 'response'});
    }
    getDataForSeoCategory(r: GetDataForSEOCategoryRequest | GetDataForSEOCategoryRequestInterface): Observable<GetDataForSEOCategoryResponse> {
        const request = ((<GetDataForSEOCategoryRequest>r).toApiJson) ? (<GetDataForSEOCategoryRequest>r) : new GetDataForSEOCategoryRequest(r);
        return this.http.post<GetDataForSEOCategoryResponseInterface>(this._host + "/listing_products.v1.SEOService/GetDataForSEOCategory", request.toApiJson(), this.apiOptions())
            .pipe(
                map(resp => GetDataForSEOCategoryResponse.fromProto(resp))
            );
    }
    
}
