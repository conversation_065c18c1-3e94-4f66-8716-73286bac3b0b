package seoworkflow

import (
	"context"

	cssdk "github.com/vendasta/CS/sdks/go/v1"
	address "github.com/vendasta/address/sdks/go"
	advertising "github.com/vendasta/advertising/sdks/go/v2"
	category "github.com/vendasta/category/sdks/go/v1"
	accounts_v2 "github.com/vendasta/generated-protos-go/accounts/v2"
	"github.com/vendasta/gosdks/openai"
	lpBq "github.com/vendasta/listing-products/internal/common/bigquery"
	lpslack "github.com/vendasta/listing-products/internal/common/gchat"
	dataforseo "github.com/vendasta/listing-products/internal/dataforseo"
	dataforseocategoriesservice "github.com/vendasta/listing-products/internal/dataforseocategories/service"
	"github.com/vendasta/listing-products/internal/gcs"
	listingprofileservice "github.com/vendasta/listing-products/internal/listingprofile/service"
	aioauditresultservice "github.com/vendasta/listing-products/internal/seo/aioauditresult/service"
	keywordinfoservice "github.com/vendasta/listing-products/internal/seo/keywordinfo/service"
	googleSeoKeywordInfo "github.com/vendasta/listing-products/internal/seo/seogooglekeywordinfo/service"
	seodataservice "github.com/vendasta/listing-products/internal/seo/service"
	seofailedworkflowinfoservice "github.com/vendasta/listing-products/internal/seo/workflow/seofailedworkflowinfo/service"
	seosettingsservice "github.com/vendasta/listing-products/internal/seosettings/service"
	seosuggestedkeywordsservice "github.com/vendasta/listing-products/internal/seosuggestedkeywords/service"
	suggestionsservice "github.com/vendasta/listing-products/internal/suggestions/service"
	snapshot "github.com/vendasta/snapshot/sdks/go"
	"google.golang.org/grpc"
)

type activityServicesKey struct{}

type activityServices struct {
	LpBqClient                   lpBq.BQWrapperInterface
	lpService                    listingprofileservice.Interface
	cloudStorageService          gcs.Interface
	seoDataService               seodataservice.Service
	dataForSEOClient             dataforseo.SERPClient
	seoSettingsService           seosettingsservice.Service
	snapshotSeo                  snapshot.SeoSectionServiceClientInterface
	nap                          address.NapDataServiceClientInterface
	accountsClient               AccountsServiceInterface
	gChat                        lpslack.GChatAlerter
	seoSuggestedKeywordsService  seosuggestedkeywordsservice.Service
	dataforseoCategoryService    dataforseocategoriesservice.Service
	keywordInfo                  keywordinfoservice.Service
	csGMBClient                  cssdk.GoogleMyBusinessClientInterface
	categoryClient               category.CategoriesClientInterface
	suggestionService            suggestionsservice.Service
	advertisingClient            advertising.AdwordsClientInterface
	seogooglekeywordInfoService  googleSeoKeywordInfo.Service
	seofailedworkflowInfoService seofailedworkflowinfoservice.Service
	aioAuditResultService        aioauditresultservice.Service
	openAIClient                 openai.Client
}

//go:generate mockgen -destination=./mocks/mock_accounts_sdk.go -package=mocks -source=activity_services.go AccountsServiceInterface
type AccountsServiceInterface interface {
	GetMultiActivationStatuses(ctx context.Context, in *accounts_v2.GetMultiActivationStatusesRequest, opts ...grpc.CallOption) (*accounts_v2.GetMultiActivationStatusesResponse, error)
}

func NewActivityContext(ctx context.Context, lpBqClient lpBq.BQWrapperInterface, lpService listingprofileservice.Interface, cloudStorageService gcs.Interface, seoDataService seodataservice.Service, dataForSEOClient dataforseo.SERPClient, seoSettingsService seosettingsservice.Service, snapshotSeo snapshot.SeoSectionServiceClientInterface, nap address.NapDataServiceClientInterface, accountsClient AccountsServiceInterface, gChat lpslack.GChatAlerter, seoSuggestedKeywordsService seosuggestedkeywordsservice.Service, dataforseoCategoryService dataforseocategoriesservice.Service, keywordInfo keywordinfoservice.Service, csGMBClient cssdk.GoogleMyBusinessClientInterface, categoryClient category.CategoriesClientInterface, suggestionService suggestionsservice.Service, advertisingClient advertising.AdwordsClientInterface, seogooglekeywordInfoService googleSeoKeywordInfo.Service, seoFailedworkflowInfoService seofailedworkflowinfoservice.Service, openAIClient openai.Client, aioAuditResultService aioauditresultservice.Service) context.Context {
	return context.WithValue(ctx, activityServicesKey{}, &activityServices{
		LpBqClient:                   lpBqClient,
		lpService:                    lpService,
		cloudStorageService:          cloudStorageService,
		seoDataService:               seoDataService,
		dataForSEOClient:             dataForSEOClient,
		seoSettingsService:           seoSettingsService,
		snapshotSeo:                  snapshotSeo,
		nap:                          nap,
		accountsClient:               accountsClient,
		gChat:                        gChat,
		seoSuggestedKeywordsService:  seoSuggestedKeywordsService,
		dataforseoCategoryService:    dataforseoCategoryService,
		keywordInfo:                  keywordInfo,
		csGMBClient:                  csGMBClient,
		categoryClient:               categoryClient,
		suggestionService:            suggestionService,
		advertisingClient:            advertisingClient,
		seogooglekeywordInfoService:  seogooglekeywordInfoService,
		seofailedworkflowInfoService: seoFailedworkflowInfoService,
		openAIClient:                 openAIClient,
		aioAuditResultService:        aioAuditResultService,
	})
}

func getActivityServices(ctx context.Context) *activityServices {
	as := ctx.Value(activityServicesKey{})
	if as == nil {
		panic("getActivityServices: Missing account services in activity context")
	}
	services, ok := as.(*activityServices)
	if !ok {
		panic("getActivityServices: Invalid account services in activity context")
	}

	return services
}
