package main

import (
	"context"

	advertising "github.com/vendasta/advertising/sdks/go/v2"
	category "github.com/vendasta/category/sdks/go/v1"
	dataforseocategoriesservice "github.com/vendasta/listing-products/internal/dataforseocategories/service"
	aioauditresultservice "github.com/vendasta/listing-products/internal/seo/aioauditresult/service"
	keywordinfoservice "github.com/vendasta/listing-products/internal/seo/keywordinfo/service"
	seofailedworkflowinfoservice "github.com/vendasta/listing-products/internal/seo/workflow/seofailedworkflowinfo/service"
	seosuggestedkeywordsservice "github.com/vendasta/listing-products/internal/seosuggestedkeywords/service"
	suggestionsservice "github.com/vendasta/listing-products/internal/suggestions/service"

	accounts "github.com/vendasta/accounts/sdks/go/v2"
	address "github.com/vendasta/address/sdks/go"
	citationdataservice "github.com/vendasta/listing-products/internal/citations/citationdata/service"
	rejectedcitationsservice "github.com/vendasta/listing-products/internal/citations/rejectedcitations/service"
	citationworkflow "github.com/vendasta/listing-products/internal/citations/workflow"
	"github.com/vendasta/listing-products/internal/dataforseo"
	"github.com/vendasta/listing-products/internal/gcs"
	listingprofileservice "github.com/vendasta/listing-products/internal/listingprofile/service"
	listingsourceservice "github.com/vendasta/listing-products/internal/listingsource/service"
	googleSeoKeywordInfo "github.com/vendasta/listing-products/internal/seo/seogooglekeywordinfo/service"
	seodataservice "github.com/vendasta/listing-products/internal/seo/service"
	seoworkflow "github.com/vendasta/listing-products/internal/seo/workflow"

	seosettingsservice "github.com/vendasta/listing-products/internal/seosettings/service"
	"github.com/vendasta/listing-products/internal/syndication/dataaxle"
	dataaxleverification "github.com/vendasta/listing-products/internal/syndication/dataaxle/verificationworkflow"
	"github.com/vendasta/listing-products/internal/syndication/foursquare"
	"github.com/vendasta/listing-products/internal/syndication/neustar/claimworkflow"
	"github.com/vendasta/listing-products/internal/syndication/submissions/sftpworkflow"
	uberall "github.com/vendasta/listing-products/internal/syndication/uberall/activation"
	"github.com/vendasta/listing-products/internal/syndication/uberall/migrations"
	partner "github.com/vendasta/partner/sdks/go/v1"
	snapshot "github.com/vendasta/snapshot/sdks/go"

	cssdk "github.com/vendasta/CS/sdks/go/v1"

	"github.com/vendasta/gosdks/logging"
	"github.com/vendasta/gosdks/openai"
	vendastatemporal "github.com/vendasta/gosdks/temporal"
	lpBq "github.com/vendasta/listing-products/internal/common/bigquery"
	lpslack "github.com/vendasta/listing-products/internal/common/gchat"
	"github.com/vendasta/listing-products/internal/datahealth"
	"github.com/vendasta/listing-products/internal/syndication/neustar"
	"github.com/vendasta/listing-products/internal/syndication/neustar/verificationworkflow"
)

// setupTemporal is used for managing workers and calling registration functions. Workers that belong to a specific
// package should not be configured here.
func setupTemporal(ctx context.Context, client *vendastatemporal.Temporal, lpBqClient *lpBq.BQWrapper, lpGChatAlerter lpslack.GChatAlerter, neustarService neustar.Verifier, neustarServiceClaimer neustar.Claimer, dataAxleService *dataaxle.Service, csGMBClient cssdk.GoogleMyBusinessClientInterface, foursquareSFTPService foursquare.FoursquareService, featureFlagClient *partner.Client, uberallService uberall.Activator, listingProfileService listingprofileservice.Interface,
	seoDataService seodataservice.Service, dataForSEOClient dataforseo.SERPClient, seoCloudStorageService gcs.Interface, citationDataService citationdataservice.Service, sourceService listingsourceservice.Service, rejectedCitations rejectedcitationsservice.Service, seoSettingsSerivce seosettingsservice.Service, snapshotSeo snapshot.SeoSectionServiceClientInterface, nap address.NapDataServiceClientInterface, accountsClient accounts.AccountsServiceClientInterface, seoSuggestedKeywordsService seosuggestedkeywordsservice.Service, dataforseoCateogryService dataforseocategoriesservice.Service, keywordInfo keywordinfoservice.Service, categoryClient category.CategoriesClientInterface, suggestionService suggestionsservice.Service, advertisingClient advertising.AdwordsClientInterface, seogooglekeywordInfoService googleSeoKeywordInfo.Service, seoFailedWorkflowInfoService seofailedworkflowinfoservice.Service, aioAuditResultService aioauditresultservice.Service, openAIClient openai.Client) (*vendastatemporal.Temporal, error) {
	err := datahealth.SetupTemporalAndStartWorker(ctx, client, lpBqClient, lpGChatAlerter)
	if err != nil {
		logging.Criticalf(ctx, "failed to start data health temporal worker: %s", err)
		return nil, err
	}

	err = verificationworkflow.SetupTemporalAndStartWorker(ctx, client, lpBqClient, neustarService)
	if err != nil {
		logging.Criticalf(ctx, "failed to start neustar verification temporal worker: %s", err)
		return nil, err
	}

	err = dataaxleverification.SetupTemporalAndStartWorker(ctx, client, lpBqClient, dataAxleService)
	if err != nil {
		logging.Criticalf(ctx, "failed to start data-axle verification temporal worker: %s", err)
		return nil, err
	}

	err = sftpworkflow.SetupTemporalActivities(ctx, client, lpBqClient, foursquareSFTPService, lpGChatAlerter, featureFlagClient)
	if err != nil {
		logging.Criticalf(ctx, "failed to start foursquare sftp temporal worker: %s", err)
		return nil, err
	}

	err = migrations.NewUberallMigrationWorkflow(ctx, client, *lpBqClient, uberallService)
	if err != nil {
		logging.Criticalf(ctx, "failed to start uberall autosync temporal worker: %s", err)
		return nil, err
	}
	err = claimworkflow.SetupTemporalAndStartWorker(ctx, client, lpBqClient, neustarServiceClaimer)

	if err != nil {
		logging.Criticalf(ctx, "failed to start neustar claim temporal worker: %s", err)
		return nil, err
	}

	err = seoworkflow.SetupTemporalAndStartWorker(ctx, client, lpBqClient, listingProfileService,
		seoCloudStorageService, seoDataService, dataForSEOClient, seoSettingsSerivce, snapshotSeo, nap, accountsClient, lpGChatAlerter, seoSuggestedKeywordsService, dataforseoCateogryService, keywordInfo, csGMBClient, categoryClient, suggestionService, advertisingClient, seogooglekeywordInfoService, seoFailedWorkflowInfoService, aioAuditResultService, openAIClient)
	if err != nil {
		logging.Criticalf(ctx, "failed to start seo data temporal worker: %s", err)
		return nil, err
	}

	err = citationworkflow.SetupTemporalAndStartWorker(ctx, client, listingProfileService, citationDataService, dataForSEOClient, sourceService, rejectedCitations)
	if err != nil {
		logging.Criticalf(ctx, "failed to start citation temporal worker: %s", err)
		return nil, err
	}

	return client, nil
}
