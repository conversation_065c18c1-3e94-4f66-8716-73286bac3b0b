package api

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/vendasta/IAM/sdks/go/v2/policy"
	"github.com/vendasta/gosdks/config"
	listingproductsresources "github.com/vendasta/iam-resources/applications/listing-products"
	listingprofileservice "github.com/vendasta/listing-products/internal/listingprofile/service"
	"github.com/vendasta/listing-products/internal/syncdata"

	"github.com/vendasta/listing-products/internal/accountswrapper"
	"github.com/vendasta/listing-products/internal/authservice"
	bingInsightsService "github.com/vendasta/listing-products/internal/binginsights"
	uberallhttpclient "github.com/vendasta/listing-products/internal/syndication/uberall/httpclient"

	"github.com/vendasta/gosdks/statsd"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/vendasta/listing-products/internal/bingplaces"
	"github.com/vendasta/listing-products/internal/directsyncsources"
	applebusinessconnect_syndication "github.com/vendasta/listing-products/internal/syndication/applebusinessconnect"
	"github.com/vendasta/listing-products/internal/syndication/facebook"
	"github.com/vendasta/listing-products/internal/syndication/gmb/bulkconnect"
	"github.com/vendasta/listing-products/internal/syndication/neustar"
	"github.com/vendasta/listing-products/internal/syndication/neustar/claimworkflow"

	"github.com/vendasta/listing-products/internal/activation/ldactivations"

	"github.com/vendasta/listing-products/internal/syndication/dataaxle"

	"github.com/golang/protobuf/ptypes/empty"

	model "github.com/vendasta/listing-products/internal/addonattributes"
	addonattributesservice "github.com/vendasta/listing-products/internal/addonattributes/service"
	"github.com/vendasta/listing-products/internal/syndication/uberall"
	"github.com/vendasta/listing-products/internal/syndication/yext"

	"github.com/golang/protobuf/ptypes/timestamp"
	iam "github.com/vendasta/IAM/sdks/go/v1"
	listing_products_v1 "github.com/vendasta/generated-protos-go/listing_products/v1"
	"github.com/vendasta/gosdks/logging"
	"github.com/vendasta/gosdks/validation"
	"github.com/vendasta/gosdks/validation/rules"
	"github.com/vendasta/gosdks/verrors"

	"github.com/vendasta/listing-products/internal/google/businessprofile"
	"github.com/vendasta/listing-products/internal/insights"
	turbolisterapisubmission "github.com/vendasta/listing-products/internal/syndication/submissions/apiworkflow"
	uberallmigrations "github.com/vendasta/listing-products/internal/syndication/uberall/migrations"
)

const (
	API_USAGE_METRIC = "api-usage"
)

type HandleBulkConnectRequest interface {
	HandleBulkConnectRequest(w http.ResponseWriter, r *http.Request)
}
type ListingProductsService struct {
	iamService                   IAMService
	authService                  authservice.Interface
	insightsService              insights.ServiceInterface
	bingSubmissionService        bingplaces.BingPlacesService
	TurboListerSubmissionService *turbolisterapisubmission.TurbolisterSubmissionWorkflow
	uberallMigrationService      *uberallmigrations.UberallMigrationWorkFlow
	addonAttributesService       addonattributesservice.Service
	yextService                  *yext.Service
	uberallService               *uberall.Service
	googleBusinessProfileService businessprofile.Servicer
	ldActivationsService         ldactivations.Service
	dataaxleService              *dataaxle.Service
	neustarClaimService          claimworkflow.Servicer
	googleBulkConnectService     bulkconnect.Servicer
	directSyncSourcesService     directsyncsources.Interface
	facebookLocation             facebook.Servicer
	neustarService               *neustar.Service
	abcService                   *applebusinessconnect_syndication.Service
	uberallHttpClient            uberallhttpclient.HttpClienter
	accountsWrapper              accountswrapper.ServiceInterface
	listingProfileService        listingprofileservice.Service
	syncDataService              syncdata.Interface
	bingInsightsService          *bingInsightsService.BingInsightsService
	seoServer                    *SEOServer
}

func (s *ListingProductsService) GetDoctorDotComCategories(ctx context.Context, request *listing_products_v1.GetDoctorDotComCategoriesRequest) (*listing_products_v1.GetDoctorDotComCategoriesResponse, error) {
	categories, err := s.uberallHttpClient.GetDoctorDotComCategories(ctx)
	if err != nil {
		return nil, err
	}

	return &listing_products_v1.GetDoctorDotComCategoriesResponse{
		Categories: categories,
	}, nil
}

func New(iam IAMService, authService authservice.Interface, is insights.ServiceInterface, tlss *turbolisterapisubmission.TurbolisterSubmissionWorkflow, ums *uberallmigrations.UberallMigrationWorkFlow, aas addonattributesservice.Service, ys *yext.Service, us *uberall.Service, gbps businessprofile.Servicer, lda ldactivations.Service, das *dataaxle.Service, ncs claimworkflow.Servicer, gbcs bulkconnect.Servicer, ss directsyncsources.Interface, facebookLocation facebook.Servicer, neustarService *neustar.Service, bingService bingplaces.BingPlacesService, abcService *applebusinessconnect_syndication.Service, uberallhttpclienter uberallhttpclient.HttpClienter, accountsWrapper accountswrapper.ServiceInterface, listingProfileService listingprofileservice.Service, syncDataService syncdata.Interface, bingInsightsService *bingInsightsService.BingInsightsService, seoServer *SEOServer) *ListingProductsService {
	return &ListingProductsService{
		iamService:                   iam,
		authService:                  authService,
		insightsService:              is,
		bingSubmissionService:        bingService,
		TurboListerSubmissionService: tlss,
		uberallMigrationService:      ums,
		addonAttributesService:       aas,
		yextService:                  ys,
		uberallService:               us,
		googleBusinessProfileService: gbps,
		ldActivationsService:         lda,
		dataaxleService:              das,
		neustarClaimService:          ncs,
		googleBulkConnectService:     gbcs,
		directSyncSourcesService:     ss,
		facebookLocation:             facebookLocation,
		neustarService:               neustarService,
		abcService:                   abcService,
		uberallHttpClient:            uberallhttpclienter,
		accountsWrapper:              accountsWrapper,
		listingProfileService:        listingProfileService,
		syncDataService:              syncDataService,
		bingInsightsService:          bingInsightsService,
		seoServer:                    seoServer,
	}
}

// GetGoogleMyBusinessInsightsData gets google my business insights data for various metrics, grouped by day
func (s *ListingProductsService) GetGoogleMyBusinessInsightsData(ctx context.Context, req *listing_products_v1.GetGoogleMyBusinessInsightsDataRequest) (*listing_products_v1.GetGoogleMyBusinessInsightsDataResponse, error) {
	statsd.Incr(API_USAGE_METRIC, []string{"message:GetGoogleMyBusinessInsightsData"}, 1)

	err := s.iamService.AccessAccountGroup(ctx, req.GetBusinessId(), iam.READ)
	if err != nil {
		return nil, verrors.ToGrpcError(err)
	}

	return s.insightsService.GetDataFromExternalService(ctx, req.GetBusinessId(), *req.GetStartDate(), *req.GetEndDate())
}

// GetGoogleMyBusinessInsightsDataBucketed gets bucketed google my business insights data for various metrics
func (s *ListingProductsService) GetGoogleMyBusinessInsightsDataBucketed(ctx context.Context, req *listing_products_v1.GetGoogleMyBusinessInsightsDataBucketedRequest) (*listing_products_v1.GetGoogleMyBusinessInsightsDataBucketedResponse, error) {
	statsd.Incr(API_USAGE_METRIC, []string{"message:GetGoogleMyBusinessInsightsDataBucketed"}, 1)

	err := s.iamService.AccessAccountGroup(ctx, req.GetBusinessId(), iam.READ)
	if err != nil {
		return nil, verrors.ToGrpcError(err)
	}

	bucketedMetrics, err := s.insightsService.GetDataBucketedFromExternalService(ctx, req.BusinessId, *req.StartDate, *req.EndDate)
	if err != nil {
		return nil, verrors.ToGrpcError(err)
	}
	//contextForInsights, err := s.insightsService.GetContextForInsights(ctx, req.BusinessId)
	//if err != nil {
	//	return nil, verrors.ToGrpcError(err)
	//}

	pastEndDate := req.StartDate.Seconds - 1
	pastStartDate := pastEndDate - (req.EndDate.Seconds - req.StartDate.Seconds)
	bucketedPastMetrics, err := s.insightsService.GetDataBucketedFromExternalService(ctx, req.BusinessId, timestamp.Timestamp{Seconds: pastStartDate}, timestamp.Timestamp{Seconds: pastEndDate})
	if err != nil {
		return nil, verrors.ToGrpcError(err)
	}

	return &listing_products_v1.GetGoogleMyBusinessInsightsDataBucketedResponse{
		ActionsDrivingDirections: mapDomainDataToApiData(bucketedMetrics["actions_driving_directions"], bucketedPastMetrics["actions_driving_directions"]),
		ActionsPhone:             mapDomainDataToApiData(bucketedMetrics["actions_phone"], bucketedPastMetrics["actions_phone"]),
	}, nil
}

// UpdateVendorSyncFlag: DEPRECATED: Replaced with endpoints in listing-syndication to turn on or off syncing
func (s *ListingProductsService) UpdateVendorSyncFlag(ctx context.Context, req *listing_products_v1.UpdateVendorSyncFlagRequest) (*empty.Empty, error) {
	return &empty.Empty{}, nil
}

// Generally we do not have these enabled. Will return (nil, nil) if there is no migration workflow that matches the
//
//	request.
func (s *ListingProductsService) handleSpecialCases(
	ctx context.Context,
	vendorID string,
) (*listing_products_v1.SubmitTurboListerResponse, error) {
	if vendorID == "uberallturnon" {
		err := s.uberallMigrationService.StartWorkflow(ctx)
		if err != nil {
			return &listing_products_v1.SubmitTurboListerResponse{
				RejectedReason: err.Error(),
			}, err
		}
		return &listing_products_v1.SubmitTurboListerResponse{}, nil
	}
	if vendorID == "neustarclaim" {
		err := s.neustarClaimService.StartWorkflow(ctx)
		if err != nil {
			return &listing_products_v1.SubmitTurboListerResponse{
				RejectedReason: err.Error(),
			}, err
		}
	}

	return nil, nil
}

// SubmitTurboLister initiate a TurboLister workflow
func (s *ListingProductsService) SubmitTurboLister(ctx context.Context, req *listing_products_v1.SubmitTurboListerRequest) (*listing_products_v1.SubmitTurboListerResponse, error) {
	businessID := req.BusinessId
	vendorID := req.VendorId

	err := validation.NewValidator().Rule(
		validation.StringNotEmpty(businessID, verrors.InvalidArgument, "business_id is required"),
		validation.StringNotEmpty(vendorID, verrors.InvalidArgument, "vendor_name is required"),
	).Validate()
	if err != nil {
		return &listing_products_v1.SubmitTurboListerResponse{
			RejectedReason: err.Error(),
		}, nil
	}

	migrationResp, err := s.handleSpecialCases(ctx, vendorID)
	if migrationResp != nil || err != nil {
		return migrationResp, err
	}
	err = s.TurboListerSubmissionService.StartSubmissionWorkflow(ctx, businessID, vendorID, req.GetForce())

	var rejectedReason string
	if err != nil {
		rejectedReason = err.Error()
	}

	return &listing_products_v1.SubmitTurboListerResponse{
		RejectedReason: rejectedReason,
	}, err
}

func (s *ListingProductsService) GetAddonAttributes(ctx context.Context, request *listing_products_v1.GetAddonAttributesRequest) (*listing_products_v1.GetAddonAttributesResponse, error) {
	err := validation.NewValidator().
		Rule(validation.StringNotEmpty(request.GetAddonId(), verrors.InvalidArgument, "addonID is required")).
		Validate()
	if err != nil {
		return nil, err
	}
	modelValue, err := s.addonAttributesService.Get(ctx, request.GetAddonId())
	if err != nil {
		return nil, err
	}

	addonID, addonAttributes := AddonAttributesModelToProto(modelValue)
	return &listing_products_v1.GetAddonAttributesResponse{
		AddonId:         addonID,
		AddonAttributes: addonAttributes,
	}, nil
}

func (s *ListingProductsService) GetMultiAddonAttributes(ctx context.Context, req *listing_products_v1.GetMultiAddonAttributesRequest) (*listing_products_v1.GetMultiAddonAttributesResponse, error) {
	addonIDs := req.GetAddonIds()
	addonTypes := AddonTypesToStrings(req.GetAddonTypes())
	providerTypes := ProviderTypesToStrings(req.GetProviderTypes())

	attributesList, err := s.addonAttributesService.GetMulti(ctx, addonIDs, addonTypes, providerTypes)
	if err != nil {
		return nil, err
	}

	out := make(map[string]*listing_products_v1.AddonAttributes)
	for _, a := range attributesList {
		addonID, addonAttributes := AddonAttributesModelToProto(a)
		out[addonID] = addonAttributes
	}

	return &listing_products_v1.GetMultiAddonAttributesResponse{
		AddonAttributes: out,
	}, nil
}

func (s *ListingProductsService) CreateAddonAttributes(ctx context.Context, request *listing_products_v1.CreateAddonAttributesRequest) (*empty.Empty, error) {
	createAddonData := AddonAttributesProtoToModel(request.AddonId, request.GetAddonAttributes())
	err := s.addonAttributesService.Create(ctx, createAddonData)
	if err != nil {
		return nil, err
	}
	return &empty.Empty{}, nil
}

func (s *ListingProductsService) UpdateAddonAttributes(ctx context.Context, request *listing_products_v1.UpdateAddonAttributesRequest) (*empty.Empty, error) {
	fieldMask := model.FieldMaskToSnakeCase(request.GetFieldMask().GetPaths())
	err := validation.NewValidator().
		Rule(validation.BoolTrue(len(fieldMask) > 0, verrors.InvalidArgument, "field_mask must contain at least 1 value")).
		Validate()
	if err != nil {
		return nil, err
	}
	err = s.addonAttributesService.Update(ctx, request.GetAddonId(), AddonAttributesProtoToModel(request.AddonId, request.GetAddonAttributes()), fieldMask)
	if err != nil {
		return nil, err
	}
	return &empty.Empty{}, nil
}

func (s *ListingProductsService) DeleteAddonAttributes(ctx context.Context, request *listing_products_v1.DeleteAddonAttributesRequest) (*empty.Empty, error) {
	addonID := request.GetAddonId()
	err := s.addonAttributesService.Delete(ctx, addonID)
	if err != nil {
		return nil, err
	}
	return &empty.Empty{}, nil
}

func (s *ListingProductsService) BulkConnectLocationsForGoogleUser(ctx context.Context, req *listing_products_v1.BulkConnectLocationsForGoogleUserRequest) (resp *listing_products_v1.BulkConnectLocationsForGoogleUserResponse, err error) {
	err = validation.NewValidator().
		Rule(rules.AtLeastOneStringRequired(req.GetBusinessId(), "business ID(s) of the already connected account on the partner is required")).
		Validate()
	if err != nil {
		return nil, err
	}
	// TODO: populate the response once the wf is complete
	return &listing_products_v1.BulkConnectLocationsForGoogleUserResponse{}, s.googleBulkConnectService.StartWorkflow(ctx, req.GetBusinessId(), false)
}

func (s *ListingProductsService) HandleBulkConnectRequest(w http.ResponseWriter, r *http.Request) {
	type businessID struct {
		BusinessID string `json:"business_id"`
		Commit     bool   `json:"commit"`
	}

	var businessIDs businessID
	ctx := context.Background()
	body, err := io.ReadAll(r.Body)
	if err != nil {
		http.Error(w, fmt.Sprintf("ReadAll: %s", err.Error()), http.StatusInternalServerError)
		return
	}
	err = json.Unmarshal(body, &businessIDs)
	if err != nil {
		http.Error(w, fmt.Sprintf("Unmarshal: %s", err.Error()), http.StatusInternalServerError)
		return
	}
	if businessIDs.BusinessID == "" {
		http.Error(w, "business_id is required", http.StatusBadRequest)
		return
	}

	// Don't run bulk connect workflows on demo
	if config.CurEnv() == config.Demo {
		return
	}

	err = s.googleBulkConnectService.StartWorkflow(ctx, []string{businessIDs.BusinessID}, businessIDs.Commit)
	if err != nil {
		http.Error(w, fmt.Sprintf("StartWorkflow: %s", err.Error()), http.StatusInternalServerError)
		return
	}
	return
}

func (s *ListingProductsService) ManuallyFixYextProvisioning(
	ctx context.Context,
	request *listing_products_v1.ManuallyFixYextProvisioningRequest,
) (*empty.Empty, error) {
	err := validation.NewValidator().
		Rule(validation.StringNotEmpty(request.GetBusinessId(), verrors.InvalidArgument, "business ID required")).
		Rule(validation.StringNotEmpty(request.GetAddonId(), verrors.InvalidArgument, "addon ID required")).
		Rule(validation.StringNotEmpty(request.GetActivationId(), verrors.InvalidArgument, "activation ID required")).
		Validate()
	if err != nil {
		return nil, err
	}
	err = s.yextService.SendManualRequestToYext(ctx, request.GetBusinessId(), request.GetAddonId(), request.GetActivationId())
	return &empty.Empty{}, err
}

func (s *ListingProductsService) ManuallyProvisionUberall(
	ctx context.Context,
	request *listing_products_v1.ManuallyProvisionUberallRequest,
) (*empty.Empty, error) {
	err := validation.NewValidator().
		Rule(validation.StringNotEmpty(request.GetBusinessId(), verrors.InvalidArgument, "business ID required")).
		Rule(validation.StringNotEmpty(request.GetAddonId(), verrors.InvalidArgument, "addon ID required")).
		Validate()
	if err != nil {
		return nil, err
	}
	return &empty.Empty{}, s.uberallService.Provision(ctx, request.GetBusinessId(), request.GetAddonId())
}

func (s *ListingProductsService) ManuallyDeactivateUberallProvisioning(
	ctx context.Context,
	request *listing_products_v1.ManuallyDeactivateUberallProvisioningRequest,
) (*empty.Empty, error) {
	err := validation.NewValidator().
		Rule(validation.StringNotEmpty(request.GetBusinessId(), verrors.InvalidArgument, "business ID required")).
		Validate()
	if err != nil {
		return nil, err
	}
	return &empty.Empty{}, s.uberallService.DeactivateUberall(ctx, request.GetBusinessId())
}

func (s *ListingProductsService) GetGoogleBusinessInfo(
	ctx context.Context,
	request *listing_products_v1.GetGoogleBusinessInfoRequest,
) (*listing_products_v1.GetGoogleBusinessInfoResponse, error) {
	err := validation.NewValidator().
		Rule(validation.StringNotEmpty(request.GetBusinessId(), verrors.InvalidArgument, "business ID required")).
		Validate()
	if err != nil {
		return nil, err
	}

	businessInfoJSON, err := s.googleBusinessProfileService.GetBusinessInfo(ctx, request.BusinessId)

	return &listing_products_v1.GetGoogleBusinessInfoResponse{
		TextDump: businessInfoJSON,
	}, err
}

func (s *ListingProductsService) GetFacebookPageInfo(ctx context.Context, request *listing_products_v1.GetFacebookPageInfoRequest) (*listing_products_v1.GetFacebookPageInfoResponse, error) {
	err := validation.NewValidator().
		Rule(validation.StringNotEmpty(request.GetBusinessId(), verrors.InvalidArgument, "business ID required")).
		Validate()
	if err != nil {
		return nil, err
	}

	businessInfoJSON, err := s.facebookLocation.GetPageInfo(ctx, request.BusinessId)

	return &listing_products_v1.GetFacebookPageInfoResponse{
		TextDump: businessInfoJSON,
	}, err
}

func (s *ListingProductsService) GetAppleBusinessConnectInfo(ctx context.Context, request *listing_products_v1.GetAppleBusinessConnectInfoRequest) (*listing_products_v1.GetAppleBusinessConnectInfoResponse, error) {
	err := validation.NewValidator().
		Rule(validation.StringNotEmpty(request.GetBusinessId(), verrors.InvalidArgument, "business ID required")).
		Validate()
	if err != nil {
		return nil, err
	}

	return s.abcService.GetLocationInfoForBusiness(ctx, request.BusinessId)
}

func (s *ListingProductsService) GetBingPlacesInfo(ctx context.Context, request *listing_products_v1.GetBingPlacesInfoRequest) (*listing_products_v1.GetBingPlacesInfoResponse, error) {
	err := validation.NewValidator().
		Rule(validation.StringNotEmpty(request.GetBusinessId(), verrors.InvalidArgument, "business ID required")).
		Validate()
	if err != nil {
		return nil, err
	}

	info, err := s.bingSubmissionService.GetBusinessesInfo(ctx, request.BusinessId)
	if err != nil {
		return nil, err
	}
	if len(info) < 1 {
		return nil, verrors.New(verrors.NotFound, "no business info found for (%s)", request.GetBusinessId())
	}

	status, err := s.bingSubmissionService.CheckStatusBusinesses(ctx, []string{request.BusinessId})
	if err != nil {
		return nil, err
	}
	if len(status) < 1 {
		return nil, verrors.New(verrors.NotFound, "no status info found for (%s)", request.GetBusinessId())
	}
	b, err := json.Marshal(map[string]interface{}{
		"business": info,
		"status":   status[0],
	})
	if err != nil {
		return nil, err
	}

	var prettyBody bytes.Buffer
	err = json.Indent(&prettyBody, b, "", "  ")
	if err != nil {
		return nil, nil
	}

	return &listing_products_v1.GetBingPlacesInfoResponse{
		TextDump: prettyBody.String(),
	}, nil
}
func (s *ListingProductsService) TriggerBingInsightsBackFill(ctx context.Context, request *listing_products_v1.TriggerBingInsightsBackFillRequest) (*empty.Empty, error) {
	if request.GetFirstSuccessfulSync() {
		err := validation.NewValidator().
			Rule(validation.StringNotEmpty(request.GetBusinessId(), verrors.InvalidArgument, "business ID required")).
			Validate()
		if err != nil {
			return nil, err
		}
	} else {
		err := validation.NewValidator().
			Rule(validation.StringNotEmpty(request.GetBusinessId(), verrors.InvalidArgument, "business ID required")).
			Rule(validation.StringNotEmpty(request.GetStartDate(), verrors.InvalidArgument, "start date required")).
			Rule(validation.StringNotEmpty(request.GetEndDate(), verrors.InvalidArgument, "end date required")).
			Validate()
		if err != nil {
			return nil, err
		}
	}
	_, isProEdition, err := s.accountsWrapper.GetCurrentMSEditionForAccount(ctx, request.GetBusinessId())
	if err != nil {
		if verrors.IsError(verrors.NotFound, err) {
			return nil, verrors.New(verrors.NotFound, "account %s not found", request.GetBusinessId())
		}
		return nil, verrors.New(verrors.Internal, "error getting current edition for account: %s", err.Error())
	}
	if !isProEdition {
		return nil, verrors.New(verrors.FailedPrecondition, "account %s is not a pro edition", request.GetBusinessId())
	}
	err = s.bingInsightsService.TriggerBingInsightsWorkflow(ctx, request.BusinessId, request.StartDate, request.EndDate, request.FirstSuccessfulSync)
	if err != nil {
		return nil, err
	}
	return &empty.Empty{}, nil
}

func (s *ListingProductsService) CheckSubmissionStatus(
	ctx context.Context,
	request *listing_products_v1.CheckSubmissionStatusRequest,
) (*empty.Empty, error) {
	statsd.Incr(dataaxle.CheckSubmissionStatusMetricFmt, []string{"message:check-submission-status-attempted"}, 1)
	err := validation.NewValidator().
		Rule(validation.StringNotEmpty(request.GetBusinessId(), verrors.InvalidArgument, "business ID required")).
		Validate()
	if err != nil {
		statsd.Incr(dataaxle.CheckSubmissionStatusMetricFmt, []string{"message:business-id-not-found"}, 1)
		return nil, err
	}

	_, err = s.dataaxleService.CheckSubmissionStatus(ctx, request.BusinessId)

	return &empty.Empty{}, err
}

func (s *ListingProductsService) GetInfoGroupId(ctx context.Context, request *listing_products_v1.GetInfoGroupIdRequest) (*listing_products_v1.GetInfoGroupIdResponse, error) {
	statsd.Incr(dataaxle.GetInfoGroupIdMetricFmt, []string{"message:get-info-group-id-attempted"}, 1)
	err := validation.NewValidator().
		Rule(validation.StringNotEmpty(request.GetBusinessId(), verrors.InvalidArgument, "business ID required")).
		Validate()
	if err != nil {
		statsd.Incr(dataaxle.GetInfoGroupIdMetricFmt, []string{"message:business-id-not-found"}, 1)
		return nil, err
	}

	return s.dataaxleService.GetInfoGroupId(ctx, request.BusinessId)
}

func (s *ListingProductsService) GetListingDistributionActivationStatus(ctx context.Context, req *listing_products_v1.GetListingDistributionActivationStatusRequest) (*listing_products_v1.GetListingDistributionActivationStatusResponse, error) {
	if err := validation.NewValidator().
		Rule(validation.StringNotEmpty(req.GetAccountGroupId(), verrors.InvalidArgument, "account_group_id is required")).Validate(); err != nil {
		return nil, err
	}
	return s.ldActivationsService.GetListingDistributionActivationStatus(ctx, req.GetAccountGroupId())
}

func (s *ListingProductsService) GetSyndicationInfo(ctx context.Context, req *listing_products_v1.GetSyndicationInfoRequest) (*listing_products_v1.GetSyndicationInfoResponse, error) {
	businessID := req.GetBusinessId()
	vendorID := req.GetVendorId()

	err := validation.NewValidator().
		Rule(validation.StringNotEmpty(req.GetBusinessId(), verrors.InvalidArgument, "business ID required")).
		Validate()
	if err != nil {
		return nil, err
	}
	var info []*listing_products_v1.SyndicationInfo

	if vendorID != "" {
		info, err = s.getSyndicationInfoByVendor(ctx, businessID, vendorID)
		return &listing_products_v1.GetSyndicationInfoResponse{
			SyndicationInfo: info,
		}, nil
	}

	facebook, err := s.facebookLocation.GetLocation(ctx, businessID)
	if err != nil && !verrors.IsError(verrors.NotFound, err) {
		return nil, err
	}
	if facebook != nil {
		info = append(info, &listing_products_v1.SyndicationInfo{
			VendorId:        turbolisterapisubmission.Facebook,
			LastAttempted:   timestamppb.New(facebook.SyncLastAttemptedOn),
			LastSucceeded:   timestamppb.New(facebook.LocationSyncSucceededOn),
			LastWorkflowUrl: facebook.LastWorkflowURL,
		})
	}
	uberall, err := s.uberallService.GetLocation(ctx, businessID)
	if err != nil && !verrors.IsError(verrors.NotFound, err) {
		return nil, err
	}
	if uberall != nil {
		info = append(info, &listing_products_v1.SyndicationInfo{
			VendorId:        turbolisterapisubmission.Uberall,
			LastAttempted:   timestamppb.New(uberall.SyncLastAttemptedOn),
			LastSucceeded:   timestamppb.New(uberall.LocationSyncSucceededOn),
			LastWorkflowUrl: uberall.LastWorkflowURL,
		})
	}
	dataaxle, err := s.dataaxleService.GetLocation(ctx, businessID)
	if err != nil && !verrors.IsError(verrors.NotFound, err) {
		return nil, err
	}
	if dataaxle != nil {
		info = append(info, &listing_products_v1.SyndicationInfo{
			VendorId:        turbolisterapisubmission.DataAxle,
			LastAttempted:   timestamppb.New(dataaxle.SyncLastAttemptedOn),
			LastSucceeded:   timestamppb.New(dataaxle.SubmissionLastSucceededOn),
			LastWorkflowUrl: dataaxle.LastWorkflowURL,
		})
	}
	neustar, err := s.neustarService.GetLocation(ctx, businessID)
	if err != nil && !verrors.IsError(verrors.NotFound, err) {
		return nil, err
	}
	if neustar != nil {
		info = append(info, &listing_products_v1.SyndicationInfo{
			VendorId:        turbolisterapisubmission.Neustar,
			LastAttempted:   timestamppb.New(neustar.SyncLastAttemptedOn),
			LastSucceeded:   timestamppb.New(neustar.LocationSyncSucceededOn),
			LastWorkflowUrl: neustar.LastWorkflowURL,
		})
	}
	return &listing_products_v1.GetSyndicationInfoResponse{SyndicationInfo: info}, nil
}

func (s *ListingProductsService) getSyndicationInfoByVendor(ctx context.Context, businessID string, vendorID string) ([]*listing_products_v1.SyndicationInfo, error) {
	var info []*listing_products_v1.SyndicationInfo
	switch vendorID {
	case turbolisterapisubmission.Facebook:
		resp, err := s.facebookLocation.GetLocation(ctx, businessID)
		if err != nil && !verrors.IsError(verrors.NotFound, err) {
			return nil, err
		}
		if resp != nil {
			info = append(info, &listing_products_v1.SyndicationInfo{
				VendorId:        turbolisterapisubmission.Facebook,
				LastAttempted:   timestamppb.New(resp.SyncLastAttemptedOn),
				LastSucceeded:   timestamppb.New(resp.LocationSyncSucceededOn),
				LastWorkflowUrl: resp.LastWorkflowURL,
			})
		}
	case turbolisterapisubmission.Uberall:
		resp, err := s.uberallService.GetLocation(ctx, businessID)
		if err != nil && !verrors.IsError(verrors.NotFound, err) {
			return nil, err
		}
		if resp != nil {
			info = append(info, &listing_products_v1.SyndicationInfo{
				VendorId:        turbolisterapisubmission.Uberall,
				LastAttempted:   timestamppb.New(resp.SyncLastAttemptedOn),
				LastSucceeded:   timestamppb.New(resp.LocationSyncSucceededOn),
				LastWorkflowUrl: resp.LastWorkflowURL,
			})
		}
	case turbolisterapisubmission.DataAxle:
		resp, err := s.dataaxleService.GetLocation(ctx, businessID)
		if err != nil && !verrors.IsError(verrors.NotFound, err) {
			return nil, err
		}
		if resp != nil {
			info = append(info, &listing_products_v1.SyndicationInfo{
				VendorId:        turbolisterapisubmission.DataAxle,
				LastAttempted:   timestamppb.New(resp.SyncLastAttemptedOn),
				LastSucceeded:   timestamppb.New(resp.SubmissionLastSucceededOn),
				LastWorkflowUrl: resp.LastWorkflowURL,
			})
		}
	case turbolisterapisubmission.Neustar:
		resp, err := s.neustarService.GetLocation(ctx, businessID)
		if err != nil && !verrors.IsError(verrors.NotFound, err) {
			return nil, err
		}
		if resp != nil {
			info = append(info, &listing_products_v1.SyndicationInfo{
				VendorId:        turbolisterapisubmission.Neustar,
				LastAttempted:   timestamppb.New(resp.SyncLastAttemptedOn),
				LastSucceeded:   timestamppb.New(resp.LocationSyncSucceededOn),
				LastWorkflowUrl: resp.LastWorkflowURL,
			})
		}
	}
	return info, nil
}

// input: map[MTWTF]numberForDayOfWeek
func mapDomainDataToApiData(dailyValues, pastDailyValues map[time.Time]int64) *listing_products_v1.InsightBucketed {
	retVal := listing_products_v1.InsightBucketed{}

	bucketedValues := getBucketedValues(dailyValues)
	pastBucketedValues := getBucketedValues(pastDailyValues)

	var pastTotalAcrossBuckets int64
	for _, valueForWeekday := range pastBucketedValues {
		pastTotalAcrossBuckets += valueForWeekday
	}

	for weekday, valueForWeekday := range bucketedValues {
		retVal.TotalAcrossBuckets += valueForWeekday
		retVal.Buckets = append(retVal.Buckets, &listing_products_v1.InsightBucket{
			Value:       valueForWeekday,
			BucketLabel: weekday,
		})
	}

	retVal.TotalChangeAcrossBuckets = retVal.TotalAcrossBuckets - pastTotalAcrossBuckets

	return &retVal
}

/*
These strings are the ones we need in `listing-builder-client`.
Don't mess with them, unless you want to build in a translation thing into our TS SDK, or in here somewhere, etc.
*/
func dayOfWeekStringFromTimestamp(t time.Time) string {
	return map[time.Weekday]string{
		time.Monday:    "Monday",
		time.Tuesday:   "Tuesday",
		time.Wednesday: "Wednesday",
		time.Thursday:  "Thursday",
		time.Friday:    "Friday",
		time.Saturday:  "Saturday",
		time.Sunday:    "Sunday",
	}[t.Weekday()]
}

func getBucketedValues(dailyValues map[time.Time]int64) map[string]int64 {
	bucketedValues := make(map[string]int64, 7)
	for dateTime, valueForWeekday := range dailyValues {
		weekday := dayOfWeekStringFromTimestamp(dateTime)
		bucketedValues[weekday] += valueForWeekday
	}

	return bucketedValues
}

func (s *ListingProductsService) GetProductSettings(ctx context.Context, request *listing_products_v1.GetProductSettingsRequest) (*listing_products_v1.GetProductSettingsResponse, error) {
	err := validation.NewValidator().
		Rule(rules.StringNotEmpty(request.GetPartnerId(), "partner_id is required")).
		Rule(rules.StringNotEmpty(request.GetMarketId(), "market_id is required")).
		Validate()
	if err != nil {
		return nil, err
	}
	res := &listingproductsresources.Keywords{
		PartnerID: request.GetPartnerId(),
	}

	canEditKeywords, err := s.authService.AccessKeywords(ctx, request.GetPartnerId(), request.GetMarketId(), res, policy.ActionWrite)
	if err != nil {
		return nil, err
	}

	return &listing_products_v1.GetProductSettingsResponse{
		PartnerId:       request.GetPartnerId(),
		MarketId:        request.GetMarketId(),
		CanEditKeywords: canEditKeywords,
	}, nil
}

func (s *ListingProductsService) IsPartnerUser(ctx context.Context, request *listing_products_v1.IsPartnerUserRequest) (*listing_products_v1.IsPartnerUserResponse, error) {
	res := &listingproductsresources.Keywords{
		PartnerID: request.GetPartnerId(),
	}
	resp := &listing_products_v1.IsPartnerUserResponse{
		IsPartnerUser: false,
	}
	err := s.authService.AccessResource(ctx, res, policy.ActionWrite)
	if err != nil {
		if !verrors.IsError(verrors.PermissionDenied, err) {
			return nil, err
		}
		return resp, nil
	}
	resp.IsPartnerUser = true
	return resp, nil
}

// DEPRECATED
func (s *ListingProductsService) FetchAndSubmitSourceURLsToCS(ctx context.Context, req *listing_products_v1.FetchAndSubmitSourceURLsToCSRequest) (*empty.Empty, error) {
	return &empty.Empty{}, verrors.New(verrors.Unimplemented, "FetchAndSubmitSourceURLsToCS is not implemented, because no source submits listings to CS")
}

// DEPRECATED: TODO: Remove this method
func (s *ListingProductsService) StartBusinessDataScoreWorkflow(ctx context.Context, req *listing_products_v1.StartBusinessDataScoreWorkflowRequest) (*empty.Empty, error) {
	return &empty.Empty{}, nil
}

func (s *ListingProductsService) GetVendorService(ctx context.Context, vendorID string) turbolisterapisubmission.DataVendorServicer {
	switch vendorID {
	case turbolisterapisubmission.Facebook:
		return s.facebookLocation
	default:
		panic(fmt.Sprintf("getActivityServices: No Data Vendor service for %s", vendorID))
	}
}

// IsLocalSEOProActiveForAccount checks if Local SEO Pro is active for the account
func (s *ListingProductsService) IsLocalSEOProActiveForAccount(ctx context.Context, request *listing_products_v1.IsLocalSEOProActiveForAccountRequest) (*listing_products_v1.IsLocalSEOProActiveForAccountResponse, error) {
	err := validation.NewValidator().
		Rule(rules.StringNotEmpty(request.GetAccountGroupId(), "business_id is required")).
		Validate()
	if err != nil {
		return nil, err
	}

	editionID, isProEdition, err := s.accountsWrapper.GetCurrentMSEditionForAccount(ctx, request.GetAccountGroupId())
	if err != nil {
		if verrors.IsError(verrors.NotFound, err) {
			return &listing_products_v1.IsLocalSEOProActiveForAccountResponse{
				IsActive:       false,
				AccountGroupId: request.GetAccountGroupId(),
			}, nil
		}
		return nil, verrors.New(verrors.Internal, "error getting current edition for account: %s", err.Error())
	}
	return &listing_products_v1.IsLocalSEOProActiveForAccountResponse{
		IsActive:       isProEdition,
		EditionId:      editionID,
		AccountGroupId: request.GetAccountGroupId(),
	}, nil
}

func (s *ListingProductsService) GetDirectSyncSourceInfo(ctx context.Context, req *listing_products_v1.GetDirectSyncSourceInfoRequest) (*listing_products_v1.GetDirectSyncSourceInfoResponse, error) {
	businessID := req.GetBusinessId()

	err := validation.NewValidator().
		Rule(validation.StringNotEmpty(req.GetBusinessId(), verrors.InvalidArgument, "business ID required")).
		Validate()
	if err != nil {
		return nil, err
	}
	logging.Tag(ctx, "business_id", businessID)

	sources, err := s.directSyncSourcesService.GetDirectSyncSourceInfo(ctx, businessID, req.GetRefresh())
	if err != nil {
		return nil, err
	}

	return &listing_products_v1.GetDirectSyncSourceInfoResponse{
		DirectSubmitSources: sources,
	}, nil
}

func (s *ListingProductsService) GetLDData(ctx context.Context, req *listing_products_v1.GetLDDataRequest) (*listing_products_v1.GetLDDataResponse, error) {
	businessID := req.GetBusinessId()

	err := validation.NewValidator().
		Rule(validation.StringNotEmpty(req.GetBusinessId(), verrors.InvalidArgument, "business ID required")).
		Validate()
	if err != nil {
		return nil, err
	}
	logging.Tag(ctx, "business_id", businessID)

	return s.directSyncSourcesService.GetLDData(ctx, businessID)
}

func (s *ListingProductsService) SyncToSources(ctx context.Context, req *listing_products_v1.SyncToSourcesRequest) (*empty.Empty, error) {
	businessID := req.GetBusinessId()
	trigger := req.GetTrigger()

	err := validation.NewValidator().
		Rule(validation.StringNotEmpty(businessID, verrors.InvalidArgument, "business ID required")).
		Validate()
	if err != nil {
		return nil, verrors.ToGrpcError(err)
	}
	logging.Tag(ctx, "business_id", businessID)

	listingProfile, err := s.listingProfileService.Get(ctx, businessID, false)
	if err != nil {
		return nil, verrors.ToGrpcError(err)
	}

	country := listingProfile.GetCountry()

	err = s.listingProfileService.StartSyncToSourcesWorkflow(ctx, businessID, country, trigger)
	if err != nil {
		return nil, verrors.ToGrpcError(err)
	}
	return &empty.Empty{}, nil
}

func (s *ListingProductsService) GetNeustarPublications(ctx context.Context, req *listing_products_v1.GetNeustarPublicationsRequest) (*listing_products_v1.GetNeustarPublicationsResponse, error) {
	businessID := req.GetBusinessId()
	err := validation.NewValidator().
		Rule(rules.StringNotEmpty(businessID, "business ID required")).
		Validate()
	if err != nil {
		return nil, verrors.ToGrpcError(err)
	}

	publications, err := s.neustarService.GetPublications(ctx, businessID)
	if err != nil {
		logging.Errorf(ctx, "error getting Neustar publications: %s", err)
		return nil, verrors.ToGrpcError(err)
	}
	var protoPublications []*listing_products_v1.NeustarPublication
	for _, publication := range publications {
		protoPublications = append(protoPublications, &listing_products_v1.NeustarPublication{
			Id:               publication.ID,
			ParentId:         publication.ParentID,
			Platform:         publication.Platform,
			Description:      publication.Description,
			Message:          publication.Message,
			LastDeliveryTime: timestamppb.New(publication.LastDeliveryTime),
			LastModified:     timestamppb.New(publication.LastModified),
			Disposition:      publication.Disposition,
			Link:             publication.Link,
		})
	}

	return &listing_products_v1.GetNeustarPublicationsResponse{
		Publications: protoPublications,
	}, nil
}
