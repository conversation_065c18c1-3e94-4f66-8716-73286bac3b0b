package openai

import (
	"context"
	"fmt"

	secretmanager "cloud.google.com/go/secretmanager/apiv1"
	"cloud.google.com/go/secretmanager/apiv1/secretmanagerpb"
)

// SecretManager to allow google cloud secrets to be retrieved
type SecretManager struct {
	client *secretmanager.Client
}

// NewSecretManager creates a new secret manager
func NewSecretManager(client *secretmanager.Client) *SecretManager {
	return &SecretManager{
		client: client,
	}
}

// GetSecret returns the value of the given secret and version
func (s *SecretManager) GetSecret(ctx context.Context, projectID string, secret string, version string) (string, error) {
	accessSecretReq := &secretmanagerpb.AccessSecretVersionRequest{
		Name: fmt.Sprintf("projects/%s/secrets/%s/versions/%s", projectID, secret, version),
	}

	res, err := s.client.AccessSecretVersion(ctx, accessSecretReq)
	if err != nil {
		return "", err
	}

	return string(res.GetPayload().GetData()), nil
}
