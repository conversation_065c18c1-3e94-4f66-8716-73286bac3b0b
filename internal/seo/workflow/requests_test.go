package seoworkflow

import (
	"bytes"
	"context"
	"io"
	"regexp"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	address "github.com/vendasta/address/sdks/go"
	accounts_v2 "github.com/vendasta/generated-protos-go/accounts/v2"
	nap_v1 "github.com/vendasta/generated-protos-go/nap/v1"
	"github.com/vendasta/generated-protos-go/vstorepb"
	"github.com/vendasta/listing-products/internal/constants"
	"github.com/vendasta/listing-products/internal/dataforseo"
	listingprofilemodel "github.com/vendasta/listing-products/internal/listingprofile/model"
	listingprofileservice "github.com/vendasta/listing-products/internal/listingprofile/service"
	seodataservice "github.com/vendasta/listing-products/internal/seo/service"
	"github.com/vendasta/listing-products/internal/seo/workflow/mocks"
	seosettingsmodel "github.com/vendasta/listing-products/internal/seosettings/model"
	seosettingsservice "github.com/vendasta/listing-products/internal/seosettings/service"
)

type MyWriteCloser struct {
	name     string
	Expected []byte
	Actual   *bytes.Buffer
	t        *testing.T
}

func NewMockWriteCloser(name string, expected []byte, t *testing.T) *MyWriteCloser {
	return &MyWriteCloser{
		name:     name,
		Actual:   bytes.NewBuffer([]byte{}),
		Expected: expected,
		t:        t,
	}
}

func (m *MyWriteCloser) Reset(newExpected []byte) {
	m.Actual.Reset()
	m.Expected = newExpected
}

func (m *MyWriteCloser) Close() error {
	b, err := io.ReadAll(m.Actual)
	if err != nil {
		m.t.Errorf("Error reading from buffer: %v", err)
	}
	reg, _ := regexp.Compile(`\s+`)

	if reg.ReplaceAllString(string(b), "") != reg.ReplaceAllString(string(m.Expected), "") {
		m.t.Errorf("%s Expected %s\n", m.name, m.Expected)
		m.t.Errorf("got %s\n", b)
	}
	return nil
}

func (m *MyWriteCloser) Write(p []byte) (n int, err error) {
	return m.Actual.Write(p)
}

func Test_shouldRefreshSEOData(t *testing.T) {
	type testCase struct {
		name                         string
		date                         string
		paidEdition                  bool
		getMultiActivationStatuesErr error

		expectedErr   error
		shouldRefresh bool
	}

	cases := []*testCase{
		{
			name:          "Free edition refreshes on last day of month",
			date:          "2023-07-31",
			paidEdition:   false,
			expectedErr:   nil,
			shouldRefresh: true,
		},
		{
			name:          "Free edition refreshes does not refresh if it is not the last day of the month",
			date:          "2023-07-30",
			paidEdition:   false,
			expectedErr:   nil,
			shouldRefresh: false,
		},
		{
			name:          "Paid edition refreshes on saturday",
			date:          "2023-08-05",
			paidEdition:   true,
			expectedErr:   nil,
			shouldRefresh: true,
		},
		{
			name:          "Paid edition does not refresh if it is not saturday",
			date:          "2023-08-04",
			paidEdition:   true,
			expectedErr:   nil,
			shouldRefresh: false,
		},
	}
	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			result, err := shouldRefreshSEOData(c.paidEdition, c.date)
			assert.Equal(t, c.expectedErr, err)
			assert.Equal(t, c.shouldRefresh, result)
		})
	}
}

func Test_buildRequests(t *testing.T) {
	type keywordInfoCall struct {
		BusinessID   string
		Keyword      string
		Date         string
		LocationCode int64
		LocationName string
		Fetched      bool
	}
	type upsertCall struct {
		BusinessID  string
		Keyword     string
		Date        string
		Radius      float64
		WorkflowURL string
	}

	type testCase struct {
		name                     string
		rows                     []bigQueryRow
		seoSettings              []*seosettingsmodel.SEOSettings
		dataForSEOLocations      []*dataforseo.Location
		listingProfile           *listingprofilemodel.ListingProfile
		activations              *accounts_v2.GetMultiActivationStatusesResponse
		country                  *nap_v1.Country
		states                   *nap_v1.ListStatesResponse
		expectedKeywordInfoCalls []*keywordInfoCall
		expectedUpsertCall       []*upsertCall
		expectedResponse         *BuildRequestsResult
		expectedErr              error
	}
	cases := []*testCase{
		{
			name: "Test builds grid points for business and returns requests",
			dataForSEOLocations: []*dataforseo.Location{
				{
					Code:           1002791,
					Name:           "Saskatoon,Saskatchewan,Canada",
					CodeParent:     20124,
					CountryISOCode: "CA",
					Type:           "City",
				},
			},
			listingProfile: &listingprofilemodel.ListingProfile{
				BusinessID:  "AG-M8GZ83PL",
				CompanyName: "Dr. Connor McDavid",
				Address:     "212 Saskatchewan Crescent E",
				City:        "Saskatoon",
				State:       "SK",
				Country:     "CA",
				Website:     "Website1",
				Location: &vstorepb.GeoPoint{
					Latitude:  50,
					Longitude: 20,
				},
			},
			activations: &accounts_v2.GetMultiActivationStatusesResponse{
				ActivationStatuses: []*accounts_v2.GetMultiActivationStatusesResponse_AppsAndAddonsActivationStatuses{
					{
						BusinessId: "AG-M8GZ83PL",
						ProductId:  constants.ListingBuilderMarketplaceAppID,
						Status:     accounts_v2.ActivationStatus_ACTIVATION_STATUS_ACTIVATED,
						Count:      1,
						EditionId:  constants.ListingBuilderDemoPaidEditionID,
					},
				},
			},
			country: &nap_v1.Country{
				Id:   "CA",
				Name: "Canada",
			},
			states: &nap_v1.ListStatesResponse{
				States: []*nap_v1.State{
					{
						Id:   "SK",
						Name: "Saskatchewan",
					},
				},
			},
			expectedKeywordInfoCalls: []*keywordInfoCall{
				{
					BusinessID:   "AG-123",
					Keyword:      "keyword1",
					Date:         "2023-01-01",
					LocationCode: 1002791,
					LocationName: "Saskatoon,Saskatchewan,Canada",
					Fetched:      true,
				},
				{
					BusinessID:   "AG-123",
					Keyword:      "keyword2",
					Date:         "2023-01-01",
					LocationCode: 1002791,
					LocationName: "Saskatoon,Saskatchewan,Canada",
					Fetched:      true,
				},
			},
			expectedUpsertCall: []*upsertCall{},
			expectedResponse: &BuildRequestsResult{
				HasPro:       true,
				SearchRadius: 1.25,
				MapsRequests: []*dataforseo.GoogleMapsSERPTaskPostRequest{
					{
						BusinessID: "AG-123", Keyword: "keyword1", LanguageCode: "en",
						Location: &dataforseo.MapLocation{
							Latitude: 50.01819866111815, Longitude: 19.971886287207884, Zoom: 13,
						}, Vicinity: "VICINITY_A1",
					},
					{
						BusinessID: "AG-123", Keyword: "keyword1", LanguageCode: "en",
						Location: &dataforseo.MapLocation{
							Latitude: 50.01819866111815, Longitude: 19.98594314360394, Zoom: 13,
						}, Vicinity: "VICINITY_A2",
					},
					{
						BusinessID: "AG-123", Keyword: "keyword1", LanguageCode: "en",
						Location: &dataforseo.MapLocation{Latitude: 50.01819866111815, Longitude: 20, Zoom: 13},
						Vicinity: "VICINITY_A3",
					},
					{
						BusinessID: "AG-123", Keyword: "keyword1", LanguageCode: "en",
						Location: &dataforseo.MapLocation{
							Latitude: 50.01819866111815, Longitude: 20.01405685639606, Zoom: 13,
						}, Vicinity: "VICINITY_A4",
					},
					{
						BusinessID: "AG-123", Keyword: "keyword1", LanguageCode: "en",
						Location: &dataforseo.MapLocation{
							Latitude: 50.01819866111815, Longitude: 20.028113712792116, Zoom: 13,
						}, Vicinity: "VICINITY_A5",
					},
					{
						BusinessID: "AG-123", Keyword: "keyword1", LanguageCode: "en",
						Location: &dataforseo.MapLocation{
							Latitude: 50.009099330559074, Longitude: 19.971886287207884, Zoom: 13,
						}, Vicinity: "VICINITY_B1",
					},
					{
						BusinessID: "AG-123", Keyword: "keyword1", LanguageCode: "en",
						Location: &dataforseo.MapLocation{
							Latitude: 50.009099330559074, Longitude: 19.98594314360394, Zoom: 13,
						}, Vicinity: "VICINITY_B2",
					},
					{
						BusinessID: "AG-123", Keyword: "keyword1", LanguageCode: "en",
						Location: &dataforseo.MapLocation{Latitude: 50.009099330559074, Longitude: 20, Zoom: 13},
						Vicinity: "VICINITY_B3",
					},
					{
						BusinessID: "AG-123", Keyword: "keyword1", LanguageCode: "en",
						Location: &dataforseo.MapLocation{
							Latitude: 50.009099330559074, Longitude: 20.01405685639606, Zoom: 13,
						}, Vicinity: "VICINITY_B4",
					},
					{
						BusinessID: "AG-123", Keyword: "keyword1", LanguageCode: "en",
						Location: &dataforseo.MapLocation{
							Latitude: 50.009099330559074, Longitude: 20.028113712792116, Zoom: 13,
						}, Vicinity: "VICINITY_B5",
					},
					{
						BusinessID: "AG-123", Keyword: "keyword1", LanguageCode: "en",
						Location: &dataforseo.MapLocation{Latitude: 50, Longitude: 19.971886287207884, Zoom: 13},
						Vicinity: "VICINITY_C1",
					},
					{
						BusinessID: "AG-123", Keyword: "keyword1", LanguageCode: "en",
						Location: &dataforseo.MapLocation{Latitude: 50, Longitude: 19.98594314360394, Zoom: 13},
						Vicinity: "VICINITY_C2",
					},
					{
						BusinessID: "AG-123", Keyword: "keyword1", LanguageCode: "en",
						Location: &dataforseo.MapLocation{Latitude: 50, Longitude: 20, Zoom: 13},
						Vicinity: "VICINITY_C3",
					},
					{
						BusinessID: "AG-123", Keyword: "keyword1", LanguageCode: "en",
						Location: &dataforseo.MapLocation{Latitude: 50, Longitude: 20.01405685639606, Zoom: 13},
						Vicinity: "VICINITY_C4",
					},
					{
						BusinessID: "AG-123", Keyword: "keyword1", LanguageCode: "en",
						Location: &dataforseo.MapLocation{Latitude: 50, Longitude: 20.028113712792116, Zoom: 13},
						Vicinity: "VICINITY_C5",
					}, {
						BusinessID: "AG-123", Keyword: "keyword1", LanguageCode: "en",
						Location: &dataforseo.MapLocation{
							Latitude: 49.990900669440926, Longitude: 19.971886287207884, Zoom: 13,
						}, Vicinity: "VICINITY_D1",
					}, {
						BusinessID: "AG-123", Keyword: "keyword1", LanguageCode: "en",
						Location: &dataforseo.MapLocation{
							Latitude: 49.990900669440926, Longitude: 19.98594314360394, Zoom: 13,
						}, Vicinity: "VICINITY_D2",
					}, {
						BusinessID: "AG-123", Keyword: "keyword1", LanguageCode: "en",
						Location: &dataforseo.MapLocation{Latitude: 49.990900669440926, Longitude: 20, Zoom: 13},
						Vicinity: "VICINITY_D3",
					}, {
						BusinessID: "AG-123", Keyword: "keyword1", LanguageCode: "en",
						Location: &dataforseo.MapLocation{
							Latitude: 49.990900669440926, Longitude: 20.01405685639606, Zoom: 13,
						}, Vicinity: "VICINITY_D4",
					}, {
						BusinessID: "AG-123", Keyword: "keyword1", LanguageCode: "en",
						Location: &dataforseo.MapLocation{
							Latitude: 49.990900669440926, Longitude: 20.028113712792116, Zoom: 13,
						}, Vicinity: "VICINITY_D5",
					}, {
						BusinessID: "AG-123", Keyword: "keyword1", LanguageCode: "en",
						Location: &dataforseo.MapLocation{
							Latitude: 49.98180133888185, Longitude: 19.971886287207884, Zoom: 13,
						}, Vicinity: "VICINITY_E1",
					}, {
						BusinessID: "AG-123", Keyword: "keyword1", LanguageCode: "en",
						Location: &dataforseo.MapLocation{
							Latitude: 49.98180133888185, Longitude: 19.98594314360394, Zoom: 13,
						}, Vicinity: "VICINITY_E2",
					}, {
						BusinessID: "AG-123", Keyword: "keyword1", LanguageCode: "en",
						Location: &dataforseo.MapLocation{Latitude: 49.98180133888185, Longitude: 20, Zoom: 13},
						Vicinity: "VICINITY_E3",
					}, {
						BusinessID: "AG-123", Keyword: "keyword1", LanguageCode: "en",
						Location: &dataforseo.MapLocation{
							Latitude: 49.98180133888185, Longitude: 20.01405685639606, Zoom: 13,
						}, Vicinity: "VICINITY_E4",
					}, {
						BusinessID: "AG-123", Keyword: "keyword1", LanguageCode: "en",
						Location: &dataforseo.MapLocation{
							Latitude: 49.98180133888185, Longitude: 20.028113712792116, Zoom: 13,
						}, Vicinity: "VICINITY_E5",
					}, {
						BusinessID: "AG-123", Keyword: "keyword2", LanguageCode: "en",
						Location: &dataforseo.MapLocation{
							Latitude: 50.01819866111815, Longitude: 19.971886287207884, Zoom: 13,
						}, Vicinity: "VICINITY_A1",
					}, {
						BusinessID: "AG-123", Keyword: "keyword2", LanguageCode: "en",
						Location: &dataforseo.MapLocation{
							Latitude: 50.01819866111815, Longitude: 19.98594314360394, Zoom: 13,
						}, Vicinity: "VICINITY_A2",
					}, {
						BusinessID: "AG-123", Keyword: "keyword2", LanguageCode: "en",
						Location: &dataforseo.MapLocation{Latitude: 50.01819866111815, Longitude: 20, Zoom: 13},
						Vicinity: "VICINITY_A3",
					}, {
						BusinessID: "AG-123", Keyword: "keyword2", LanguageCode: "en",
						Location: &dataforseo.MapLocation{
							Latitude: 50.01819866111815, Longitude: 20.01405685639606, Zoom: 13,
						}, Vicinity: "VICINITY_A4",
					}, {
						BusinessID: "AG-123", Keyword: "keyword2", LanguageCode: "en",
						Location: &dataforseo.MapLocation{
							Latitude: 50.01819866111815, Longitude: 20.028113712792116, Zoom: 13,
						}, Vicinity: "VICINITY_A5",
					}, {
						BusinessID: "AG-123", Keyword: "keyword2", LanguageCode: "en",
						Location: &dataforseo.MapLocation{
							Latitude: 50.009099330559074, Longitude: 19.971886287207884, Zoom: 13,
						}, Vicinity: "VICINITY_B1",
					}, {
						BusinessID: "AG-123", Keyword: "keyword2", LanguageCode: "en",
						Location: &dataforseo.MapLocation{
							Latitude: 50.009099330559074, Longitude: 19.98594314360394, Zoom: 13,
						}, Vicinity: "VICINITY_B2",
					}, {
						BusinessID: "AG-123", Keyword: "keyword2", LanguageCode: "en",
						Location: &dataforseo.MapLocation{Latitude: 50.009099330559074, Longitude: 20, Zoom: 13},
						Vicinity: "VICINITY_B3",
					}, {
						BusinessID: "AG-123", Keyword: "keyword2", LanguageCode: "en",
						Location: &dataforseo.MapLocation{
							Latitude: 50.009099330559074, Longitude: 20.01405685639606, Zoom: 13,
						}, Vicinity: "VICINITY_B4",
					}, {
						BusinessID: "AG-123", Keyword: "keyword2", LanguageCode: "en",
						Location: &dataforseo.MapLocation{
							Latitude: 50.009099330559074, Longitude: 20.028113712792116, Zoom: 13,
						}, Vicinity: "VICINITY_B5",
					}, {
						BusinessID: "AG-123", Keyword: "keyword2", LanguageCode: "en",
						Location: &dataforseo.MapLocation{Latitude: 50, Longitude: 19.971886287207884, Zoom: 13},
						Vicinity: "VICINITY_C1",
					}, {
						BusinessID: "AG-123", Keyword: "keyword2", LanguageCode: "en",
						Location: &dataforseo.MapLocation{Latitude: 50, Longitude: 19.98594314360394, Zoom: 13},
						Vicinity: "VICINITY_C2",
					}, {
						BusinessID: "AG-123", Keyword: "keyword2", LanguageCode: "en",
						Location: &dataforseo.MapLocation{Latitude: 50, Longitude: 20, Zoom: 13},
						Vicinity: "VICINITY_C3",
					}, {
						BusinessID: "AG-123", Keyword: "keyword2", LanguageCode: "en",
						Location: &dataforseo.MapLocation{Latitude: 50, Longitude: 20.01405685639606, Zoom: 13},
						Vicinity: "VICINITY_C4",
					}, {
						BusinessID: "AG-123", Keyword: "keyword2", LanguageCode: "en",
						Location: &dataforseo.MapLocation{Latitude: 50, Longitude: 20.028113712792116, Zoom: 13},
						Vicinity: "VICINITY_C5",
					}, {
						BusinessID: "AG-123", Keyword: "keyword2", LanguageCode: "en",
						Location: &dataforseo.MapLocation{
							Latitude: 49.990900669440926, Longitude: 19.971886287207884, Zoom: 13,
						}, Vicinity: "VICINITY_D1",
					}, {
						BusinessID: "AG-123", Keyword: "keyword2", LanguageCode: "en",
						Location: &dataforseo.MapLocation{
							Latitude: 49.990900669440926, Longitude: 19.98594314360394, Zoom: 13,
						}, Vicinity: "VICINITY_D2",
					}, {
						BusinessID: "AG-123", Keyword: "keyword2", LanguageCode: "en",
						Location: &dataforseo.MapLocation{Latitude: 49.990900669440926, Longitude: 20, Zoom: 13},
						Vicinity: "VICINITY_D3",
					}, {
						BusinessID: "AG-123", Keyword: "keyword2", LanguageCode: "en",
						Location: &dataforseo.MapLocation{
							Latitude: 49.990900669440926, Longitude: 20.01405685639606, Zoom: 13,
						}, Vicinity: "VICINITY_D4",
					}, {
						BusinessID: "AG-123", Keyword: "keyword2", LanguageCode: "en",
						Location: &dataforseo.MapLocation{
							Latitude: 49.990900669440926, Longitude: 20.028113712792116, Zoom: 13,
						}, Vicinity: "VICINITY_D5",
					}, {
						BusinessID: "AG-123", Keyword: "keyword2", LanguageCode: "en",
						Location: &dataforseo.MapLocation{
							Latitude: 49.98180133888185, Longitude: 19.971886287207884, Zoom: 13,
						}, Vicinity: "VICINITY_E1",
					}, {
						BusinessID: "AG-123", Keyword: "keyword2", LanguageCode: "en",
						Location: &dataforseo.MapLocation{
							Latitude: 49.98180133888185, Longitude: 19.98594314360394, Zoom: 13,
						}, Vicinity: "VICINITY_E2",
					}, {
						BusinessID: "AG-123", Keyword: "keyword2", LanguageCode: "en",
						Location: &dataforseo.MapLocation{Latitude: 49.98180133888185, Longitude: 20, Zoom: 13},
						Vicinity: "VICINITY_E3",
					}, {
						BusinessID: "AG-123", Keyword: "keyword2", LanguageCode: "en",
						Location: &dataforseo.MapLocation{
							Latitude: 49.98180133888185, Longitude: 20.01405685639606, Zoom: 13,
						}, Vicinity: "VICINITY_E4",
					}, {
						BusinessID: "AG-123", Keyword: "keyword2", LanguageCode: "en",
						Location: &dataforseo.MapLocation{
							Latitude: 49.98180133888185, Longitude: 20.028113712792116, Zoom: 13,
						}, Vicinity: "VICINITY_E5",
					},
				}, OrganicRequests: []*dataforseo.GoogleOrganicSERPTaskPostRequest{
					{
						BusinessID: "AG-123", Vicinity: "VICINITY_CITY", Keyword: "keyword1", LanguageCode: "en",
						Location: &dataforseo.OrganicSERPLocation{Latitude: 50, Longitude: 20, Radius: 20000},
						Tags:     nil,
					}, {
						BusinessID: "AG-123", Vicinity: "VICINITY_CITY", Keyword: "keyword2", LanguageCode: "en",
						Location: &dataforseo.OrganicSERPLocation{Latitude: 50, Longitude: 20, Radius: 20000},
						Tags:     nil,
					},
				},
			},
			expectedErr: nil,
			seoSettings: []*seosettingsmodel.SEOSettings{
				{
					BusinessID:        "AG-1",
					LocalSearchRadius: 1.75,
				},
			},
		},
		{
			name: "Test builds single grid point for free business",
			rows: []bigQueryRow{
				{
					BusinessID:  "AG-123",
					SEOKeywords: []string{"keyword1"},
					Latitude:    50,
					Longitude:   20,
				},
			},
			dataForSEOLocations: []*dataforseo.Location{
				{
					Code:           1002791,
					Name:           "Saskatoon,Saskatchewan,Canada",
					CodeParent:     20124,
					CountryISOCode: "CA",
					Type:           "City",
				},
			},
			listingProfile: &listingprofilemodel.ListingProfile{
				BusinessID:  "AG-M8GZ83PL",
				CompanyName: "Dr. Connor McDavid",
				Address:     "212 Saskatchewan Crescent E",
				City:        "Saskatoon",
				State:       "SK",
				Country:     "CA",
				Website:     "Website1",
				Location: &vstorepb.GeoPoint{
					Latitude:  50,
					Longitude: 20,
				},
			},
			activations: &accounts_v2.GetMultiActivationStatusesResponse{
				ActivationStatuses: []*accounts_v2.GetMultiActivationStatusesResponse_AppsAndAddonsActivationStatuses{
					{
						BusinessId: "AG-M8GZ83PL",
						ProductId:  constants.ListingBuilderMarketplaceAppID,
						Status:     accounts_v2.ActivationStatus_ACTIVATION_STATUS_ACTIVATED,
						Count:      1,
						EditionId:  "",
					},
				},
			},
			country: &nap_v1.Country{
				Id:   "CA",
				Name: "Canada",
			},
			states: &nap_v1.ListStatesResponse{
				States: []*nap_v1.State{
					{
						Id:   "SK",
						Name: "Saskatchewan",
					},
				},
			},
			expectedKeywordInfoCalls: []*keywordInfoCall{
				{
					BusinessID:   "AG-123",
					Keyword:      "keyword1",
					Date:         "2023-01-01",
					LocationCode: 1002791,
					LocationName: "Saskatoon,Saskatchewan,Canada",
					Fetched:      true,
				},
				{
					BusinessID:   "AG-123",
					Keyword:      "keyword2",
					Date:         "2023-01-01",
					LocationCode: 1002791,
					LocationName: "Saskatoon,Saskatchewan,Canada",
					Fetched:      true,
				},
			},
			expectedUpsertCall: []*upsertCall{},
			expectedResponse: &BuildRequestsResult{
				SearchRadius: 1.25,
				MapsRequests: []*dataforseo.GoogleMapsSERPTaskPostRequest{
					{
						BusinessID: "AG-123", Keyword: "keyword1", LanguageCode: "en",
						Location: &dataforseo.MapLocation{Latitude: 50, Longitude: 20, Zoom: 13},
						Vicinity: "VICINITY_C3",
					}, {
						BusinessID: "AG-123", Keyword: "keyword2", LanguageCode: "en",
						Location: &dataforseo.MapLocation{Latitude: 50, Longitude: 20, Zoom: 13},
						Vicinity: "VICINITY_C3",
					},
				}, OrganicRequests: []*dataforseo.GoogleOrganicSERPTaskPostRequest{
					{
						BusinessID: "AG-123", Vicinity: "VICINITY_CITY", Keyword: "keyword1", LanguageCode: "en",
						Location: &dataforseo.OrganicSERPLocation{Latitude: 50, Longitude: 20, Radius: 20000},
						Tags:     nil,
					}, {
						BusinessID: "AG-123", Vicinity: "VICINITY_CITY", Keyword: "keyword2", LanguageCode: "en",
						Location: &dataforseo.OrganicSERPLocation{Latitude: 50, Longitude: 20, Radius: 20000},
						Tags:     nil,
					},
				},
			},
			expectedErr: nil,
			seoSettings: []*seosettingsmodel.SEOSettings{
				{
					BusinessID:        "AG-1",
					LocalSearchRadius: 1.75,
				},
			},
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			ctx := context.Background()
			ctrl := gomock.NewController(t)

			mockSEODataService := seodataservice.NewMockService(ctrl)
			mockSEODataService.EXPECT().QueryDateRange(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

			for _, req := range c.expectedUpsertCall {
				mockSEODataService.EXPECT().Upsert(gomock.Any(), req.BusinessID, req.Keyword, req.Date,
					gomock.Any(),
				).Return(nil)
			}

			mockSEOSettingsService := seosettingsservice.NewMockService(ctrl)
			mockSEOSettingsService.EXPECT().Get(gomock.Any(), gomock.Any()).Return(nil, nil)

			mockDataForSEOService := dataforseo.NewMockSERPClient(ctrl)

			mockListingProfileService := listingprofileservice.NewMockInterface(ctrl)
			mockListingProfileService.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).Return(c.listingProfile, nil)

			mockNAP := address.MockNapDataInterface{}
			mockNAP.On("FindCountryByCode", mock.Anything, mock.Anything).Return(c.country, nil)
			mockNAP.On("ListStates", mock.Anything, mock.Anything).Return(c.states, nil)

			mockAccounts := mocks.NewMockAccountsServiceInterface(ctrl)
			mockAccounts.EXPECT().GetMultiActivationStatuses(gomock.Any(), gomock.Any()).Return(c.activations, nil).AnyTimes()

			ctx = NewActivityContext(ctx, nil, mockListingProfileService, nil, mockSEODataService, mockDataForSEOService, mockSEOSettingsService, nil, &mockNAP, mockAccounts, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil)

			resp, err := buildRequests(ctx, "AG-123", []string{"keyword1", "keyword2"}, true, "2023-01-01", "url")

			assert.Equal(t, c.expectedResponse, resp)
			assert.Equal(t, c.expectedErr, err)
		})
	}
}

func Test_processMapsRequestBatch(t *testing.T) {
	type testCase struct {
		name               string
		rows               []bigQueryRow
		requests           []*dataforseo.GoogleMapsSERPTaskPostRequest
		expectedFile       string
		dataForSeoResponse *dataforseo.GoogleMapsSERPResponse
		expectedResults    *ProcessedTaskResults
		expectedErr        error
	}
	cases := []*testCase{
		{
			name: "Test parses tasks from response and saves IDs to file",
			requests: []*dataforseo.GoogleMapsSERPTaskPostRequest{
				{
					BusinessID:   "AG-123",
					Keyword:      "keyword1",
					LanguageCode: "en",
					Location: &dataforseo.MapLocation{
						Latitude:  50,
						Longitude: 19,
						Zoom:      14,
					},
					Vicinity: "VICINITY_A1",
				},
				{
					BusinessID:   "AG-123",
					Keyword:      "keyword1",
					LanguageCode: "en",
					Location: &dataforseo.MapLocation{
						Latitude:  50,
						Longitude: 19,
						Zoom:      14,
					}, Vicinity: "VICINITY_A2",
				},
			},
			dataForSeoResponse: &dataforseo.GoogleMapsSERPResponse{
				StatusCode: 200,
				TasksCount: 90,
				TasksError: 0,
				Tasks: []*dataforseo.GoogleMapsTask{
					{
						ID:         "05240009-3621-0066-0000-e1d08f9c6849",
						StatusCode: 20100,
						Data: &dataforseo.Data{
							Tag: "AG-123:keyword1:VICINITY_A1",
						},
					},
					{
						ID:            "05240009-3621-0066-0000-e1d08f9c6849",
						StatusCode:    40100,
						StatusMessage: "Error Message",
						Data: &dataforseo.Data{
							Tag: "AG-123:keyword1:VICINITY_A2",
						},
					},
				},
			},
			expectedResults: &ProcessedTaskResults{
				PostedTasks: []*PostedTask{
					{
						ID:         "05240009-3621-0066-0000-e1d08f9c6849",
						BusinessID: "AG-123",
						Keyword:    "keyword1",
						Vicinity:   "VICINITY_A1",
						Lat:        50,
						Lng:        19,
					},
				},
				Errors: []string{"AG-123:keyword1:VICINITY_A2 task failed: Error Message"},
			},
			expectedErr: nil,
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			ctx := context.Background()
			ctrl := gomock.NewController(t)

			mockDataForSEO := dataforseo.NewMockSERPClient(ctrl)
			mockDataForSEO.EXPECT().GoogleMapsSERPTaskPost(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(c.dataForSeoResponse, nil)

			mockSEOSettingsService := seosettingsservice.NewMockService(ctrl)

			ctx = NewActivityContext(ctx, nil, nil, nil, nil, mockDataForSEO, mockSEOSettingsService, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil)

			resp, err := processMapsRequests(ctx, &SERPWorkflowParams{}, c.requests, false)

			assert.Equal(t, c.expectedResults, resp)
			assert.Equal(t, c.expectedErr, err)
		})
	}
}

func Test_processOrganicRequestBatch(t *testing.T) {
	type testCase struct {
		name               string
		rows               []bigQueryRow
		requests           []*dataforseo.GoogleOrganicSERPTaskPostRequest
		existingFile       string
		startIndex         int
		expectedResponse   *ProcessedTaskResults
		dataForSeoResponse *dataforseo.GoogleOrganicSERPResponse
		expectedErr        error
	}
	cases := []*testCase{
		{
			name: "Test parses tasks from response and saves IDs to file",
			requests: []*dataforseo.GoogleOrganicSERPTaskPostRequest{
				{
					BusinessID:   "AG-123",
					Vicinity:     "VICINITY_A1",
					Keyword:      "keyword1",
					LanguageCode: "en",
					Location: &dataforseo.OrganicSERPLocation{
						Latitude:  50.028948796815634,
						Longitude: 19.955279228836208,
						Radius:    200,
					},
				},
				{
					BusinessID:   "AG-123",
					Vicinity:     "VICINITY_A2",
					Keyword:      "keyword1",
					LanguageCode: "en",
					Location: &dataforseo.OrganicSERPLocation{
						Latitude:  50.028948796815634,
						Longitude: 19.977639614418106,
						Radius:    14,
					},
				},
			},
			dataForSeoResponse: &dataforseo.GoogleOrganicSERPResponse{
				StatusCode: 200,
				TasksCount: 90,
				TasksError: 0,
				Tasks: []*dataforseo.GoogleOrganicTask{
					{
						ID:         "05240009-3621-0066-0000-e1d08f9c6849",
						StatusCode: 20100,
						Data: &dataforseo.Data{
							Tag: "AG-123:keyword1:VICINITY_A1",
						},
					},
					{
						ID:            "05240009-3621-0066-0000-e1d08f9c6849",
						StatusCode:    40100,
						StatusMessage: "Error Message",
						Data: &dataforseo.Data{
							Tag: "AG-123:keyword1:VICINITY_A2",
						},
					},
				},
			},
			expectedResponse: &ProcessedTaskResults{
				PostedTasks: []*PostedTask{
					{
						ID:         "05240009-3621-0066-0000-e1d08f9c6849",
						BusinessID: "AG-123",
						Keyword:    "keyword1",
						Vicinity:   "VICINITY_A1",
						Lat:        50.028948796815634,
						Lng:        19.955279228836208,
					},
				},
				Errors: []string{"AG-123:keyword1:VICINITY_A2 task failed: Error Message"},
			},
			expectedErr: nil,
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			ctx := context.Background()
			ctrl := gomock.NewController(t)

			mockDataForSEO := dataforseo.NewMockSERPClient(ctrl)
			mockDataForSEO.EXPECT().GoogleOrganicSERPTaskPost(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(c.dataForSeoResponse, nil)
			mockSEOSettingsService := seosettingsservice.NewMockService(ctrl)
			ctx = NewActivityContext(ctx, nil, nil, nil, nil, mockDataForSEO, mockSEOSettingsService, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil)

			resp, err := processOrganicRequestBatch(ctx, &SERPWorkflowParams{
				Date: "2022-01-01",
			}, c.requests, false)

			assert.Equal(t, c.expectedResponse, resp)
			assert.Equal(t, c.expectedErr, err)
		})
	}
}
