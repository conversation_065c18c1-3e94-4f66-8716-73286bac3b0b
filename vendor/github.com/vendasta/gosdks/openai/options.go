package openai

import (
	"reflect"

	gogpt "github.com/sashabaranov/go-openai"
	"github.com/vendasta/gosdks/validation"
	"github.com/vendasta/gosdks/validation/rules"
	"github.com/vendasta/gosdks/verrors"
)

type options struct {
	maxTokens                    int
	temperature                  float32
	model                        string
	responseFormat               string
	responseFormatJSONSchemaDefn *JSONSchemaDefinition
	tools                        []ToolDefinition
	functionParamTypeMap         map[string]interface{}
	trimmer                      ChatCompletionWindower
	secretKey                    string
	secretVersion                string
	toolChoice                   any
	maxRetries                   int
	attributionID                string
	parallelToolCalls            *bool
	projectID                    string
}

type ToolChoiceOption string

const (
	// ToolChoiceAuto will automatically choose the tool to use
	ToolChoiceAuto ToolChoiceOption = "auto"
	// ToolChoiceRequired will require the use of one or more of the provided tools
	ToolChoiceRequired ToolChoiceOption = "required"
	// ToolChoiceNone will not use any tools
	ToolChoiceNone ToolChoiceOption = "none"
	// ToolChoiceForce will force the use one or more of the provided tools
	ToolChoiceForce ToolChoiceOption = "force"
)

type ToolChoice struct {
	Choice        ToolChoiceOption
	FunctionNames []string
}

type ClientOption func(opt *options) error

// WithMaxTokens sets the max tokens to generate in the completion
// Note that this has no impact on how many tokens are provided in existing messages
func WithMaxTokens(maxTokens int) ClientOption {
	return func(o *options) error {

		validator := validation.NewValidator().
			Rule(validation.IntGreaterThan(int64(maxTokens), 0, verrors.InvalidArgument, "maxTokens must be greater than 0"))

		switch o.model {
		case string(GPT4):
			validator = validator.Rule(validation.IntLessThan(int64(maxTokens), 8191, verrors.InvalidArgument, "maxTokens must be less than 8191"))
		case string(GPT3Dot5Turbo):
			validator = validator.Rule(validation.IntLessThan(int64(maxTokens), 4095, verrors.InvalidArgument, "maxTokens must be less than 4095"))
		}

		err := validator.Validate()
		if err != nil {
			return err
		}

		o.maxTokens = maxTokens

		return nil
	}
}

// WithTemperature sets the temperature
func WithTemperature(temperature float32) ClientOption {
	return func(o *options) error {
		err := validation.NewValidator().
			Rule(validation.FloatBetween(float64(temperature), 0, 1, verrors.InvalidArgument, "temperature must be between 0 and 1")).
			Validate()
		if err != nil {
			return err
		}

		o.temperature = temperature

		return nil
	}
}

// WithModel sets the model to use
func WithModel(model Model) ClientOption {
	return func(o *options) error {
		err := validation.NewValidator().
			Rule(validation.StringInSlice(string(model),
				[]string{string(GPT4), string(GPT3Dot5Turbo), string(GPT4Omni), string(GPT4OmniMini), string(GPT4Dot1), string(GPT4Dot1Mini), string(GPT4Dot1Nano)},
				verrors.InvalidArgument, "must be one of gpt-3.5-turbo, gpt-4o, gpt-4o-mini, gpt-4.1, gpt-4.1-mini or gpt-4.1-nano")).
			Validate()
		if err != nil {
			return err
		}

		o.model = string(model)
		return nil
	}
}

// ChatCompletionWindower is a function that incrementally removes or modifies a list of messages
type ChatCompletionWindower func(messages []ChatCompletionMessageRequest) ([]ChatCompletionMessageRequest, error)

// TrimFirstNonSystemMessage will trim the first non-system message from the list of messages
// This is useful when system messages are being used to control the conversation or provide context/facts
func TrimFirstNonSystemMessage(messages []ChatCompletionMessageRequest) ([]ChatCompletionMessageRequest, error) {
	var out []ChatCompletionMessageRequest
	hasTrimmed := false
	for _, message := range messages {
		// Skip the oldest non-system message, thereby trimming it
		if !hasTrimmed && message.Role != string(System) {
			hasTrimmed = true
			continue
		}
		out = append(out, message)
	}
	if !hasTrimmed {
		return nil, verrors.New(verrors.InvalidArgument, "cannot trim oldest non-system message, no non-system messages found")
	}
	return out, nil
}

// WithTokenTrimming will trim chat request messages to stay under the associated model's max token count
//
//		windowFunc is a function that incrementally removes or modifies a list of messages
//		This is called when the model's max token count is exceeded and as many times as required until the max token count is satisfied
//	 The function should return an error if it is unable to trim a message
//		You can use the provided TrimFirstNonSystemMessage function or provide your own
//
// The token limit enforced is the max token limit of the model minus the number of completion tokens allocated by the MaxTokens option
// Note: This only works for Chat Completions and Assistants
func WithTokenTrimming(windowFunc ChatCompletionWindower) ClientOption {
	return func(o *options) error {
		o.trimmer = windowFunc
		return nil
	}
}

// WithFunction sets a function that the model can use
// Note: Can only be used for Chat Completions and Assistants
func WithFunction(funcDefn FunctionDefinition) ClientOption {
	return func(o *options) error {
		if funcDefn.Parameters != nil && reflect.TypeOf(funcDefn.Parameters).Kind() == reflect.Ptr {
			funcDefn.Parameters = reflect.ValueOf(funcDefn.Parameters).Elem().Interface()
		}

		err := funcDefn.Validate()
		if err != nil {
			return err
		}

		o.tools = append(o.tools, ToolDefinition{
			Type:     FunctionType,
			Function: funcDefn,
		})
		// SchemaParameters is always an object at the top level
		o.functionParamTypeMap[funcDefn.Name] = map[string]interface{}{}
		if funcDefn.Parameters != nil {
			o.functionParamTypeMap[funcDefn.Name] = funcDefn.Parameters
		}

		return nil
	}
}

// WithResponseFormat sets the response format for the completion
func WithResponseFormat(responseFormat ResponseFormat) ClientOption {
	return func(o *options) error {
		err := validation.NewValidator().
			Rule(rules.StringInSlice(string(responseFormat),
				[]string{string(ResponseFormatText), string(ResponseFormatJsonObject), string(ResponseFormatJsonSchema)},
				"must be one of text, json_object or json_schema")).
			Validate()
		if err != nil {
			return err
		}

		o.responseFormat = string(responseFormat)
		return nil
	}
}

// WithResponseFormatJSONSchema sets the response format to json_schema and provides the schema definition
// It is only supported for gpt-4o and later models
func WithResponseFormatJSONSchema(schemaDefn JSONSchemaDefinition) ClientOption {
	return func(o *options) error {
		if schemaDefn.Parameters != nil && reflect.TypeOf(schemaDefn.Parameters).Kind() == reflect.Ptr {
			schemaDefn.Parameters = reflect.ValueOf(schemaDefn.Parameters).Elem().Interface()
		}

		err := schemaDefn.Validate()
		if err != nil {
			return err
		}

		o.responseFormat = string(ResponseFormatJsonSchema)
		o.responseFormatJSONSchemaDefn = &schemaDefn
		return nil
	}
}

// WithSecretKey sets the secret key to use for the secret manager
func WithSecretKey(secretKey string) ClientOption {
	return func(o *options) error {

		err := validation.NewValidator().
			Rule(rules.StringNotEmpty(secretKey, "secretKey is required")).
			Validate()
		if err != nil {
			return err
		}

		o.secretKey = secretKey

		return nil
	}
}

// WithProjectID sets the project ID to use for the secret manager
func WithProjectID(projectID string) ClientOption {
	return func(o *options) error {
		err := validation.NewValidator().
			Rule(rules.StringNotEmpty(projectID, "projectID is required")).
			Validate()
		if err != nil {
			return err
		}

		o.projectID = projectID
		return nil
	}
}

func WithToolChoice(toolChoice ToolChoice) ClientOption {
	return func(o *options) error {
		err := validation.NewValidator().
			Rule(rules.StringNotEmpty(string(toolChoice.Choice), "choice is required")).
			Validate()
		if err != nil {
			return err
		}

		switch toolChoice.Choice {
		case ToolChoiceAuto:
			o.toolChoice = "auto"
		case ToolChoiceRequired:
			o.toolChoice = "required"
		case ToolChoiceNone:
			o.toolChoice = "none"
		case ToolChoiceForce:
			if len(toolChoice.FunctionNames) == 0 {
				return verrors.New(verrors.InvalidArgument, "functionNames must be provided when using force tool choice")
			} else {
				var tc []*gogpt.ToolChoice
				for _, funcName := range toolChoice.FunctionNames {
					tc = append(tc, &gogpt.ToolChoice{
						Type: gogpt.ToolTypeFunction,
						Function: gogpt.ToolFunction{
							Name: funcName,
						},
					})
				}
				o.toolChoice = tc
			}
		default:
			o.toolChoice = "auto"
		}

		return nil
	}
}

// WithTools sets the tools to use for GetNumberOfTokens
func WithTools(tools []ToolDefinition) ClientOption {
	return func(o *options) error {
		o.tools = tools
		return nil
	}
}

// WithMaxRetries sets the maximum number of times CreateChatCompletion will retry on failure
func WithMaxRetries(maxRetries int) ClientOption {
	return func(o *options) error {
		o.maxRetries = maxRetries
		return nil
	}
}

// WithAttributionID sets an id to attribute openai usage to.
// Normally this usage is associated to the secretKey, but if the call is being made on behalf of another application then
// the attribution id should be specified
func WithAttributionID(attributionID string) ClientOption {
	return func(o *options) error {
		o.attributionID = attributionID
		return nil
	}
}

// WithParallelToolCalls sets whether more than one tool call can be requested by the chat completion response
// This can only be provided when WithTools or WithFunction is also provided.
// If not provided, it will default as though it is true.
func WithParallelToolCalls(parallelToolCalls bool) ClientOption {
	return func(o *options) error {
		o.parallelToolCalls = &parallelToolCalls
		return nil
	}
}
