package aioauditresultservice

import (
	"context"

	model "github.com/vendasta/listing-products/internal/seo/aioauditresult"
)

// Service can create and get AIOAuditResults.
// This should be the interface that the API layer uses to interact with the AIOAuditResult model.
//
//go:generate mockgen -destination mock.go -source=interface.go -package=aioauditresultservice
type Service interface {
	// Create will create a new AIOAuditResult if it does not exist yet.
	// If the AIOAuditResult already exists, a verrors.AlreadyExists error will be returned.
	// TODO: Add more parameters to this method - it is likely that the primary key alone may not be enough to create a valid AIOAuditResult
	Create(ctx context.Context, businessID string, websiteURL string, auditDate string) error

	// Get will return the existing AIOAuditResult specified by the given identifiers, or an verrors.NotFound error
	// if the targeted AIOAuditResult does not exist.
	Get(ctx context.Context, businessID string, websiteURL string, auditDate string) (*model.AIOAuditResult, error)

	// List will return a page of AIOAuditResults with the given keys and filters
	List(ctx context.Context /* TODO add filterable parameters here */, cursorIn string, pageSize int64) (results []*model.AIOAuditResult, cursor string, hasMore bool, err error)

	// Delete will delete the specified AIOAuditResult.
	// If the AIOAuditResult does not exist or has already been deleted, a verrors.NotFound error will be returned.
	Delete(ctx context.Context, businessID string, websiteURL string, auditDate string) error

	// Upsert will update the specified AIOAuditResult.
	// If the AIOAuditResult does not exist, a verrors.NotFound error will be returned.
	UpsertAIOAuditResult(ctx context.Context, businessID string, websiteURL string, auditDate string, update model.AIOAuditResult) error

	// UpdateAIOAuditResult will update the specified AIOAuditResult.
	// If the AIOAuditResult does not exist, a verrors.NotFound error will be returned.
	UpdateAIOAuditResult(ctx context.Context, businessID string, websiteURL string, auditDate string, update model.AIOAuditResult) error

	// GetAIOAuditResult will return the audit score for the specified AIOAuditResult.
	// If the AIOAuditResult does not exist, a verrors.NotFound error will be returned.
	GetAIOAuditResult(ctx context.Context, businessID string, websiteURL string, auditDate string) (*model.AIOAuditResult, error)

	// GetAuditPageResult will return the audit score for the specified AIOAuditResult.
	// If the AIOAuditResult does not exist, a verrors.NotFound error will be returned.
	GetAuditPageResult(ctx context.Context, businessID string, websiteURL string, auditDate string, pageURL string) ([]*model.AuditPageData, error)

	// UpsertAuditPageResult will update the specified AuditPageResult.
	// If the AuditPageResult does not exist, a verrors.NotFound error will be returned.
	UpsertAuditPageResult(ctx context.Context, businessID string, websiteURL string, auditDate string, pageURL string, update model.AuditPageData) error

	// UpdateAuditAllPages will update the specified AuditPageResult.
	// If the AuditPageResult does not exist, a verrors.NotFound error will be returned.
	UpdateAuditAllPages(ctx context.Context, businessID string, websiteURL string, auditDate string, update []*model.AuditPageData) error

	// GetAuditSummary returns the audit summary for the specified AIOAuditResult.
	// Returns verrors.NotFound if the string does not exist.
	GetAuditSummary(ctx context.Context, businessID, websiteURL, auditDate string) (string, error)

	// UpdateAuditStatus will update the audit status for the specified AIOAuditResult.
	// If the AIOAuditResult does not exist, a verrors.NotFound error will be returned.
	UpdateAuditStatus(ctx context.Context, businessID string, websiteURL string, auditDate string, status model.AuditStatus) error

	// GetAuditStatus will return the audit status for the specified AIOAuditResult.
	// If the AIOAuditResult does not exist, a verrors.NotFound error will be returned.
	GetAuditStatus(ctx context.Context, businessID string, websiteURL string, auditDate string) (model.AuditStatus, error)

	// UpdateAuditScoreResult will update the audit score for the specified AIOAuditResult.
	// If the AIOAuditResult does not exist, a verrors.NotFound error will be returned.
	UpdateAuditScoreResult(ctx context.Context, businessID string, websiteURL string, auditDate string, update []model.AuditScoreResults) error

	// GetAuditScoreResult will return the audit score for the specified AIOAuditResult.
	// If the AIOAuditResult does not exist, a verrors.NotFound error will be returned.
	GetAuditScoreResult(ctx context.Context, businessID string, websiteURL string, auditDate string) ([]*model.AuditScoreResults, error)

	// GetALLAIOAuditResult will return the audit score for the specified AIOAuditResult.
	// If the AIOAuditResult does not exist, a verrors.NotFound error will be returned.
	GetALLAIOAuditResult(ctx context.Context, businessID string) ([]*model.AIOAuditResult, error)
}
