// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.30.0
// source: listing_products/v1/seo.proto

package listing_products_v1

import (
	vendasta_types "github.com/vendasta/generated-protos-go/vendasta_types"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Vicinity int32

const (
	Vicinity_VICINITY_UNDEFINED Vicinity = 0
	Vicinity_VICINITY_CITY      Vicinity = 1
	Vicinity_VICINITY_A1        Vicinity = 2
	Vicinity_VICINITY_A2        Vicinity = 3
	Vicinity_VICINITY_A3        Vicinity = 4
	Vicinity_VICINITY_A4        Vicinity = 5
	Vicinity_VICINITY_A5        Vicinity = 6
	Vicinity_VICINITY_B1        Vicinity = 7
	Vicinity_VICINITY_B2        Vicinity = 8
	Vicinity_VICINITY_B3        Vicinity = 9
	Vicinity_VICINITY_B4        Vicinity = 10
	Vicinity_VICINITY_B5        Vicinity = 11
	Vicinity_VICINITY_C1        Vicinity = 12
	Vicinity_VICINITY_C2        Vicinity = 13
	Vicinity_VICINITY_C3        Vicinity = 14
	Vicinity_VICINITY_C4        Vicinity = 15
	Vicinity_VICINITY_C5        Vicinity = 16
	Vicinity_VICINITY_D1        Vicinity = 17
	Vicinity_VICINITY_D2        Vicinity = 18
	Vicinity_VICINITY_D3        Vicinity = 19
	Vicinity_VICINITY_D4        Vicinity = 20
	Vicinity_VICINITY_D5        Vicinity = 21
	Vicinity_VICINITY_E1        Vicinity = 22
	Vicinity_VICINITY_E2        Vicinity = 23
	Vicinity_VICINITY_E3        Vicinity = 24
	Vicinity_VICINITY_E4        Vicinity = 25
	Vicinity_VICINITY_E5        Vicinity = 26
)

// Enum value maps for Vicinity.
var (
	Vicinity_name = map[int32]string{
		0:  "VICINITY_UNDEFINED",
		1:  "VICINITY_CITY",
		2:  "VICINITY_A1",
		3:  "VICINITY_A2",
		4:  "VICINITY_A3",
		5:  "VICINITY_A4",
		6:  "VICINITY_A5",
		7:  "VICINITY_B1",
		8:  "VICINITY_B2",
		9:  "VICINITY_B3",
		10: "VICINITY_B4",
		11: "VICINITY_B5",
		12: "VICINITY_C1",
		13: "VICINITY_C2",
		14: "VICINITY_C3",
		15: "VICINITY_C4",
		16: "VICINITY_C5",
		17: "VICINITY_D1",
		18: "VICINITY_D2",
		19: "VICINITY_D3",
		20: "VICINITY_D4",
		21: "VICINITY_D5",
		22: "VICINITY_E1",
		23: "VICINITY_E2",
		24: "VICINITY_E3",
		25: "VICINITY_E4",
		26: "VICINITY_E5",
	}
	Vicinity_value = map[string]int32{
		"VICINITY_UNDEFINED": 0,
		"VICINITY_CITY":      1,
		"VICINITY_A1":        2,
		"VICINITY_A2":        3,
		"VICINITY_A3":        4,
		"VICINITY_A4":        5,
		"VICINITY_A5":        6,
		"VICINITY_B1":        7,
		"VICINITY_B2":        8,
		"VICINITY_B3":        9,
		"VICINITY_B4":        10,
		"VICINITY_B5":        11,
		"VICINITY_C1":        12,
		"VICINITY_C2":        13,
		"VICINITY_C3":        14,
		"VICINITY_C4":        15,
		"VICINITY_C5":        16,
		"VICINITY_D1":        17,
		"VICINITY_D2":        18,
		"VICINITY_D3":        19,
		"VICINITY_D4":        20,
		"VICINITY_D5":        21,
		"VICINITY_E1":        22,
		"VICINITY_E2":        23,
		"VICINITY_E3":        24,
		"VICINITY_E4":        25,
		"VICINITY_E5":        26,
	}
)

func (x Vicinity) Enum() *Vicinity {
	p := new(Vicinity)
	*p = x
	return p
}

func (x Vicinity) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Vicinity) Descriptor() protoreflect.EnumDescriptor {
	return file_listing_products_v1_seo_proto_enumTypes[0].Descriptor()
}

func (Vicinity) Type() protoreflect.EnumType {
	return &file_listing_products_v1_seo_proto_enumTypes[0]
}

func (x Vicinity) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Vicinity.Descriptor instead.
func (Vicinity) EnumDescriptor() ([]byte, []int) {
	return file_listing_products_v1_seo_proto_rawDescGZIP(), []int{0}
}

type GBPClaimStatus int32

const (
	GBPClaimStatus_GBP_CLAIM_STATUS_INVALID   GBPClaimStatus = 0
	GBPClaimStatus_GBP_CLAIM_STATUS_UNKNOWN   GBPClaimStatus = 1
	GBPClaimStatus_GBP_CLAIM_STATUS_CLAIMED   GBPClaimStatus = 2
	GBPClaimStatus_GBP_CLAIM_STATUS_UNCLAIMED GBPClaimStatus = 3
)

// Enum value maps for GBPClaimStatus.
var (
	GBPClaimStatus_name = map[int32]string{
		0: "GBP_CLAIM_STATUS_INVALID",
		1: "GBP_CLAIM_STATUS_UNKNOWN",
		2: "GBP_CLAIM_STATUS_CLAIMED",
		3: "GBP_CLAIM_STATUS_UNCLAIMED",
	}
	GBPClaimStatus_value = map[string]int32{
		"GBP_CLAIM_STATUS_INVALID":   0,
		"GBP_CLAIM_STATUS_UNKNOWN":   1,
		"GBP_CLAIM_STATUS_CLAIMED":   2,
		"GBP_CLAIM_STATUS_UNCLAIMED": 3,
	}
)

func (x GBPClaimStatus) Enum() *GBPClaimStatus {
	p := new(GBPClaimStatus)
	*p = x
	return p
}

func (x GBPClaimStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GBPClaimStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_listing_products_v1_seo_proto_enumTypes[1].Descriptor()
}

func (GBPClaimStatus) Type() protoreflect.EnumType {
	return &file_listing_products_v1_seo_proto_enumTypes[1]
}

func (x GBPClaimStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GBPClaimStatus.Descriptor instead.
func (GBPClaimStatus) EnumDescriptor() ([]byte, []int) {
	return file_listing_products_v1_seo_proto_rawDescGZIP(), []int{1}
}

type AddonActivation_AddonActivationStatus int32

const (
	AddonActivation_ACTIVATION_STATUS_NOT_SPECIFIED AddonActivation_AddonActivationStatus = 0
	AddonActivation_ACTIVATION_STATUS_ACTIVATED     AddonActivation_AddonActivationStatus = 1
	AddonActivation_ACTIVATION_STATUS_PENDING       AddonActivation_AddonActivationStatus = 2
	AddonActivation_ACTIVATION_STATUS_CANCELED      AddonActivation_AddonActivationStatus = 3
	AddonActivation_ACTIVATION_STATUS_DEACTIVATED   AddonActivation_AddonActivationStatus = 4
)

// Enum value maps for AddonActivation_AddonActivationStatus.
var (
	AddonActivation_AddonActivationStatus_name = map[int32]string{
		0: "ACTIVATION_STATUS_NOT_SPECIFIED",
		1: "ACTIVATION_STATUS_ACTIVATED",
		2: "ACTIVATION_STATUS_PENDING",
		3: "ACTIVATION_STATUS_CANCELED",
		4: "ACTIVATION_STATUS_DEACTIVATED",
	}
	AddonActivation_AddonActivationStatus_value = map[string]int32{
		"ACTIVATION_STATUS_NOT_SPECIFIED": 0,
		"ACTIVATION_STATUS_ACTIVATED":     1,
		"ACTIVATION_STATUS_PENDING":       2,
		"ACTIVATION_STATUS_CANCELED":      3,
		"ACTIVATION_STATUS_DEACTIVATED":   4,
	}
)

func (x AddonActivation_AddonActivationStatus) Enum() *AddonActivation_AddonActivationStatus {
	p := new(AddonActivation_AddonActivationStatus)
	*p = x
	return p
}

func (x AddonActivation_AddonActivationStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AddonActivation_AddonActivationStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_listing_products_v1_seo_proto_enumTypes[2].Descriptor()
}

func (AddonActivation_AddonActivationStatus) Type() protoreflect.EnumType {
	return &file_listing_products_v1_seo_proto_enumTypes[2]
}

func (x AddonActivation_AddonActivationStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AddonActivation_AddonActivationStatus.Descriptor instead.
func (AddonActivation_AddonActivationStatus) EnumDescriptor() ([]byte, []int) {
	return file_listing_products_v1_seo_proto_rawDescGZIP(), []int{24, 0}
}

type SEODataSummary struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Keyword       string                 `protobuf:"bytes,1,opt,name=keyword,proto3" json:"keyword,omitempty"`
	Date          *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=date,proto3" json:"date,omitempty"`
	LocalRank     float64                `protobuf:"fixed64,3,opt,name=local_rank,json=localRank,proto3" json:"local_rank,omitempty"`
	OrganicRank   float64                `protobuf:"fixed64,4,opt,name=organic_rank,json=organicRank,proto3" json:"organic_rank,omitempty"`
	Difficulty    int64                  `protobuf:"varint,5,opt,name=difficulty,proto3" json:"difficulty,omitempty"`
	SearchVolume  int64                  `protobuf:"varint,6,opt,name=search_volume,json=searchVolume,proto3" json:"search_volume,omitempty"`
	SearchRadius  float64                `protobuf:"fixed64,7,opt,name=search_radius,json=searchRadius,proto3" json:"search_radius,omitempty"`
	WorkflowUrl   string                 `protobuf:"bytes,8,opt,name=workflow_url,json=workflowUrl,proto3" json:"workflow_url,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SEODataSummary) Reset() {
	*x = SEODataSummary{}
	mi := &file_listing_products_v1_seo_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SEODataSummary) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SEODataSummary) ProtoMessage() {}

func (x *SEODataSummary) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_seo_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SEODataSummary.ProtoReflect.Descriptor instead.
func (*SEODataSummary) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_seo_proto_rawDescGZIP(), []int{0}
}

func (x *SEODataSummary) GetKeyword() string {
	if x != nil {
		return x.Keyword
	}
	return ""
}

func (x *SEODataSummary) GetDate() *timestamppb.Timestamp {
	if x != nil {
		return x.Date
	}
	return nil
}

func (x *SEODataSummary) GetLocalRank() float64 {
	if x != nil {
		return x.LocalRank
	}
	return 0
}

func (x *SEODataSummary) GetOrganicRank() float64 {
	if x != nil {
		return x.OrganicRank
	}
	return 0
}

func (x *SEODataSummary) GetDifficulty() int64 {
	if x != nil {
		return x.Difficulty
	}
	return 0
}

func (x *SEODataSummary) GetSearchVolume() int64 {
	if x != nil {
		return x.SearchVolume
	}
	return 0
}

func (x *SEODataSummary) GetSearchRadius() float64 {
	if x != nil {
		return x.SearchRadius
	}
	return 0
}

func (x *SEODataSummary) GetWorkflowUrl() string {
	if x != nil {
		return x.WorkflowUrl
	}
	return ""
}

// Deprecated: use SEODataSummary & LocalSearchData
type SEOData struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Deprecated: use SEODataSummary
	Keyword string `protobuf:"bytes,1,opt,name=keyword,proto3" json:"keyword,omitempty"`
	// Deprecated: use SEODataSummary
	Date *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=date,proto3" json:"date,omitempty"`
	// Deprecated: use SEODataSummary
	LocalRank float64 `protobuf:"fixed64,3,opt,name=local_rank,json=localRank,proto3" json:"local_rank,omitempty"`
	// Deprecated: use SEODataSummary
	OrganicRank float64 `protobuf:"fixed64,4,opt,name=organic_rank,json=organicRank,proto3" json:"organic_rank,omitempty"`
	// Deprecated: use SEODataSummary
	Difficulty int64 `protobuf:"varint,5,opt,name=difficulty,proto3" json:"difficulty,omitempty"`
	// Deprecated: use SEODataSummary
	SearchVolume int64 `protobuf:"varint,6,opt,name=search_volume,json=searchVolume,proto3" json:"search_volume,omitempty"`
	// Deprecated: use LocalSearchData
	LocalSearches []*LocalSearchData `protobuf:"bytes,7,rep,name=local_searches,json=localSearches,proto3" json:"local_searches,omitempty"`
	// Deprecated: use SEODataSummary
	SearchRadius float64 `protobuf:"fixed64,8,opt,name=search_radius,json=searchRadius,proto3" json:"search_radius,omitempty"`
	// Deprecated: use SEODataSummary
	WorkflowUrl   string `protobuf:"bytes,9,opt,name=workflow_url,json=workflowUrl,proto3" json:"workflow_url,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SEOData) Reset() {
	*x = SEOData{}
	mi := &file_listing_products_v1_seo_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SEOData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SEOData) ProtoMessage() {}

func (x *SEOData) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_seo_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SEOData.ProtoReflect.Descriptor instead.
func (*SEOData) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_seo_proto_rawDescGZIP(), []int{1}
}

func (x *SEOData) GetKeyword() string {
	if x != nil {
		return x.Keyword
	}
	return ""
}

func (x *SEOData) GetDate() *timestamppb.Timestamp {
	if x != nil {
		return x.Date
	}
	return nil
}

func (x *SEOData) GetLocalRank() float64 {
	if x != nil {
		return x.LocalRank
	}
	return 0
}

func (x *SEOData) GetOrganicRank() float64 {
	if x != nil {
		return x.OrganicRank
	}
	return 0
}

func (x *SEOData) GetDifficulty() int64 {
	if x != nil {
		return x.Difficulty
	}
	return 0
}

func (x *SEOData) GetSearchVolume() int64 {
	if x != nil {
		return x.SearchVolume
	}
	return 0
}

func (x *SEOData) GetLocalSearches() []*LocalSearchData {
	if x != nil {
		return x.LocalSearches
	}
	return nil
}

func (x *SEOData) GetSearchRadius() float64 {
	if x != nil {
		return x.SearchRadius
	}
	return 0
}

func (x *SEOData) GetWorkflowUrl() string {
	if x != nil {
		return x.WorkflowUrl
	}
	return ""
}

type LocalSearchData struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Keyword        string                 `protobuf:"bytes,1,opt,name=keyword,proto3" json:"keyword,omitempty"`
	Vicinity       Vicinity               `protobuf:"varint,2,opt,name=vicinity,proto3,enum=listing_products.v1.Vicinity" json:"vicinity,omitempty"`
	SearchLocation *Geo                   `protobuf:"bytes,3,opt,name=search_location,json=searchLocation,proto3" json:"search_location,omitempty"`
	Results        []*LocalSearchResult   `protobuf:"bytes,4,rep,name=results,proto3" json:"results,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *LocalSearchData) Reset() {
	*x = LocalSearchData{}
	mi := &file_listing_products_v1_seo_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LocalSearchData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LocalSearchData) ProtoMessage() {}

func (x *LocalSearchData) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_seo_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LocalSearchData.ProtoReflect.Descriptor instead.
func (*LocalSearchData) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_seo_proto_rawDescGZIP(), []int{2}
}

func (x *LocalSearchData) GetKeyword() string {
	if x != nil {
		return x.Keyword
	}
	return ""
}

func (x *LocalSearchData) GetVicinity() Vicinity {
	if x != nil {
		return x.Vicinity
	}
	return Vicinity_VICINITY_UNDEFINED
}

func (x *LocalSearchData) GetSearchLocation() *Geo {
	if x != nil {
		return x.SearchLocation
	}
	return nil
}

func (x *LocalSearchData) GetResults() []*LocalSearchResult {
	if x != nil {
		return x.Results
	}
	return nil
}

type LocalSearchResult struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	BusinessName   string                 `protobuf:"bytes,1,opt,name=business_name,json=businessName,proto3" json:"business_name,omitempty"`
	Address        string                 `protobuf:"bytes,2,opt,name=address,proto3" json:"address,omitempty"`
	Url            string                 `protobuf:"bytes,3,opt,name=url,proto3" json:"url,omitempty"`
	Rank           string                 `protobuf:"bytes,4,opt,name=rank,proto3" json:"rank,omitempty"`
	IsMainBusiness bool                   `protobuf:"varint,5,opt,name=is_main_business,json=isMainBusiness,proto3" json:"is_main_business,omitempty"`
	Reviews        *LocalSearchReviews    `protobuf:"bytes,6,opt,name=reviews,proto3" json:"reviews,omitempty"`
	PhoneNumber    string                 `protobuf:"bytes,7,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	ClaimStatus    GBPClaimStatus         `protobuf:"varint,8,opt,name=claim_status,json=claimStatus,proto3,enum=listing_products.v1.GBPClaimStatus" json:"claim_status,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *LocalSearchResult) Reset() {
	*x = LocalSearchResult{}
	mi := &file_listing_products_v1_seo_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LocalSearchResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LocalSearchResult) ProtoMessage() {}

func (x *LocalSearchResult) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_seo_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LocalSearchResult.ProtoReflect.Descriptor instead.
func (*LocalSearchResult) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_seo_proto_rawDescGZIP(), []int{3}
}

func (x *LocalSearchResult) GetBusinessName() string {
	if x != nil {
		return x.BusinessName
	}
	return ""
}

func (x *LocalSearchResult) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *LocalSearchResult) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *LocalSearchResult) GetRank() string {
	if x != nil {
		return x.Rank
	}
	return ""
}

func (x *LocalSearchResult) GetIsMainBusiness() bool {
	if x != nil {
		return x.IsMainBusiness
	}
	return false
}

func (x *LocalSearchResult) GetReviews() *LocalSearchReviews {
	if x != nil {
		return x.Reviews
	}
	return nil
}

func (x *LocalSearchResult) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

func (x *LocalSearchResult) GetClaimStatus() GBPClaimStatus {
	if x != nil {
		return x.ClaimStatus
	}
	return GBPClaimStatus_GBP_CLAIM_STATUS_INVALID
}

type LocalSearchReviews struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Rating        float64                `protobuf:"fixed64,1,opt,name=rating,proto3" json:"rating,omitempty"`
	Count         string                 `protobuf:"bytes,2,opt,name=count,proto3" json:"count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LocalSearchReviews) Reset() {
	*x = LocalSearchReviews{}
	mi := &file_listing_products_v1_seo_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LocalSearchReviews) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LocalSearchReviews) ProtoMessage() {}

func (x *LocalSearchReviews) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_seo_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LocalSearchReviews.ProtoReflect.Descriptor instead.
func (*LocalSearchReviews) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_seo_proto_rawDescGZIP(), []int{4}
}

func (x *LocalSearchReviews) GetRating() float64 {
	if x != nil {
		return x.Rating
	}
	return 0
}

func (x *LocalSearchReviews) GetCount() string {
	if x != nil {
		return x.Count
	}
	return ""
}

type GetSEODataRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BusinessId    string                 `protobuf:"bytes,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	Keywords      []string               `protobuf:"bytes,2,rep,name=keywords,proto3" json:"keywords,omitempty"`
	StartDate     *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	EndDate       *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSEODataRequest) Reset() {
	*x = GetSEODataRequest{}
	mi := &file_listing_products_v1_seo_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSEODataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSEODataRequest) ProtoMessage() {}

func (x *GetSEODataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_seo_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSEODataRequest.ProtoReflect.Descriptor instead.
func (*GetSEODataRequest) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_seo_proto_rawDescGZIP(), []int{5}
}

func (x *GetSEODataRequest) GetBusinessId() string {
	if x != nil {
		return x.BusinessId
	}
	return ""
}

func (x *GetSEODataRequest) GetKeywords() []string {
	if x != nil {
		return x.Keywords
	}
	return nil
}

func (x *GetSEODataRequest) GetStartDate() *timestamppb.Timestamp {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *GetSEODataRequest) GetEndDate() *timestamppb.Timestamp {
	if x != nil {
		return x.EndDate
	}
	return nil
}

type GetSEODataSummaryRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BusinessId    string                 `protobuf:"bytes,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	Keywords      []string               `protobuf:"bytes,2,rep,name=keywords,proto3" json:"keywords,omitempty"`
	StartDate     *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	EndDate       *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSEODataSummaryRequest) Reset() {
	*x = GetSEODataSummaryRequest{}
	mi := &file_listing_products_v1_seo_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSEODataSummaryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSEODataSummaryRequest) ProtoMessage() {}

func (x *GetSEODataSummaryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_seo_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSEODataSummaryRequest.ProtoReflect.Descriptor instead.
func (*GetSEODataSummaryRequest) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_seo_proto_rawDescGZIP(), []int{6}
}

func (x *GetSEODataSummaryRequest) GetBusinessId() string {
	if x != nil {
		return x.BusinessId
	}
	return ""
}

func (x *GetSEODataSummaryRequest) GetKeywords() []string {
	if x != nil {
		return x.Keywords
	}
	return nil
}

func (x *GetSEODataSummaryRequest) GetStartDate() *timestamppb.Timestamp {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *GetSEODataSummaryRequest) GetEndDate() *timestamppb.Timestamp {
	if x != nil {
		return x.EndDate
	}
	return nil
}

type GetAllAIOAuditRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BusinessId    string                 `protobuf:"bytes,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAllAIOAuditRequest) Reset() {
	*x = GetAllAIOAuditRequest{}
	mi := &file_listing_products_v1_seo_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllAIOAuditRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllAIOAuditRequest) ProtoMessage() {}

func (x *GetAllAIOAuditRequest) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_seo_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllAIOAuditRequest.ProtoReflect.Descriptor instead.
func (*GetAllAIOAuditRequest) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_seo_proto_rawDescGZIP(), []int{7}
}

func (x *GetAllAIOAuditRequest) GetBusinessId() string {
	if x != nil {
		return x.BusinessId
	}
	return ""
}

// AIO Audit APIs
type GetAllAIOAuditResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Audit         []*AIOAuditResults     `protobuf:"bytes,1,rep,name=audit,proto3" json:"audit,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAllAIOAuditResponse) Reset() {
	*x = GetAllAIOAuditResponse{}
	mi := &file_listing_products_v1_seo_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllAIOAuditResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllAIOAuditResponse) ProtoMessage() {}

func (x *GetAllAIOAuditResponse) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_seo_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllAIOAuditResponse.ProtoReflect.Descriptor instead.
func (*GetAllAIOAuditResponse) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_seo_proto_rawDescGZIP(), []int{8}
}

func (x *GetAllAIOAuditResponse) GetAudit() []*AIOAuditResults {
	if x != nil {
		return x.Audit
	}
	return nil
}

type AIOAuditResults struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	BusinessId        string                 `protobuf:"bytes,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	BrandName         string                 `protobuf:"bytes,2,opt,name=brand_name,json=brandName,proto3" json:"brand_name,omitempty"`
	WebsiteUrl        string                 `protobuf:"bytes,3,opt,name=website_url,json=websiteUrl,proto3" json:"website_url,omitempty"`
	AuditDate         string                 `protobuf:"bytes,4,opt,name=audit_date,json=auditDate,proto3" json:"audit_date,omitempty"`
	StartDate         *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	TotalPages        int64                  `protobuf:"varint,6,opt,name=total_pages,json=totalPages,proto3" json:"total_pages,omitempty"`
	AuditStatus       string                 `protobuf:"bytes,7,opt,name=audit_status,json=auditStatus,proto3" json:"audit_status,omitempty"`
	AuditSummary      string                 `protobuf:"bytes,8,opt,name=audit_summary,json=auditSummary,proto3" json:"audit_summary,omitempty"`
	AuditPages        []*AuditPageData       `protobuf:"bytes,9,rep,name=audit_pages,json=auditPages,proto3" json:"audit_pages,omitempty"`
	AuditScoreResults []*AuditScoreResults   `protobuf:"bytes,10,rep,name=audit_score_results,json=auditScoreResults,proto3" json:"audit_score_results,omitempty"`
	Keyword           string                 `protobuf:"bytes,11,opt,name=keyword,proto3" json:"keyword,omitempty"`
	AuditId           string                 `protobuf:"bytes,12,opt,name=audit_id,json=auditId,proto3" json:"audit_id,omitempty"`
	AuditUrl          string                 `protobuf:"bytes,13,opt,name=audit_url,json=auditUrl,proto3" json:"audit_url,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *AIOAuditResults) Reset() {
	*x = AIOAuditResults{}
	mi := &file_listing_products_v1_seo_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AIOAuditResults) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AIOAuditResults) ProtoMessage() {}

func (x *AIOAuditResults) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_seo_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AIOAuditResults.ProtoReflect.Descriptor instead.
func (*AIOAuditResults) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_seo_proto_rawDescGZIP(), []int{9}
}

func (x *AIOAuditResults) GetBusinessId() string {
	if x != nil {
		return x.BusinessId
	}
	return ""
}

func (x *AIOAuditResults) GetBrandName() string {
	if x != nil {
		return x.BrandName
	}
	return ""
}

func (x *AIOAuditResults) GetWebsiteUrl() string {
	if x != nil {
		return x.WebsiteUrl
	}
	return ""
}

func (x *AIOAuditResults) GetAuditDate() string {
	if x != nil {
		return x.AuditDate
	}
	return ""
}

func (x *AIOAuditResults) GetStartDate() *timestamppb.Timestamp {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *AIOAuditResults) GetTotalPages() int64 {
	if x != nil {
		return x.TotalPages
	}
	return 0
}

func (x *AIOAuditResults) GetAuditStatus() string {
	if x != nil {
		return x.AuditStatus
	}
	return ""
}

func (x *AIOAuditResults) GetAuditSummary() string {
	if x != nil {
		return x.AuditSummary
	}
	return ""
}

func (x *AIOAuditResults) GetAuditPages() []*AuditPageData {
	if x != nil {
		return x.AuditPages
	}
	return nil
}

func (x *AIOAuditResults) GetAuditScoreResults() []*AuditScoreResults {
	if x != nil {
		return x.AuditScoreResults
	}
	return nil
}

func (x *AIOAuditResults) GetKeyword() string {
	if x != nil {
		return x.Keyword
	}
	return ""
}

func (x *AIOAuditResults) GetAuditId() string {
	if x != nil {
		return x.AuditId
	}
	return ""
}

func (x *AIOAuditResults) GetAuditUrl() string {
	if x != nil {
		return x.AuditUrl
	}
	return ""
}

type AuditPageData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PageUrl       string                 `protobuf:"bytes,1,opt,name=page_url,json=pageUrl,proto3" json:"page_url,omitempty"`
	PageData      string                 `protobuf:"bytes,2,opt,name=page_data,json=pageData,proto3" json:"page_data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AuditPageData) Reset() {
	*x = AuditPageData{}
	mi := &file_listing_products_v1_seo_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AuditPageData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuditPageData) ProtoMessage() {}

func (x *AuditPageData) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_seo_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuditPageData.ProtoReflect.Descriptor instead.
func (*AuditPageData) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_seo_proto_rawDescGZIP(), []int{10}
}

func (x *AuditPageData) GetPageUrl() string {
	if x != nil {
		return x.PageUrl
	}
	return ""
}

func (x *AuditPageData) GetPageData() string {
	if x != nil {
		return x.PageData
	}
	return ""
}

type AuditScores struct {
	state                     protoimpl.MessageState `protogen:"open.v1"`
	AuditScoreScopeName       string                 `protobuf:"bytes,1,opt,name=audit_score_scope_name,json=auditScoreScopeName,proto3" json:"audit_score_scope_name,omitempty"`
	AuditScoreScopeValue      int64                  `protobuf:"varint,2,opt,name=audit_score_scope_value,json=auditScoreScopeValue,proto3" json:"audit_score_scope_value,omitempty"`
	AuditScoreScopeSummary    []string               `protobuf:"bytes,3,rep,name=audit_score_scope_summary,json=auditScoreScopeSummary,proto3" json:"audit_score_scope_summary,omitempty"`
	AuditScoreRecommendations []string               `protobuf:"bytes,4,rep,name=audit_score_recommendations,json=auditScoreRecommendations,proto3" json:"audit_score_recommendations,omitempty"`
	unknownFields             protoimpl.UnknownFields
	sizeCache                 protoimpl.SizeCache
}

func (x *AuditScores) Reset() {
	*x = AuditScores{}
	mi := &file_listing_products_v1_seo_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AuditScores) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuditScores) ProtoMessage() {}

func (x *AuditScores) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_seo_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuditScores.ProtoReflect.Descriptor instead.
func (*AuditScores) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_seo_proto_rawDescGZIP(), []int{11}
}

func (x *AuditScores) GetAuditScoreScopeName() string {
	if x != nil {
		return x.AuditScoreScopeName
	}
	return ""
}

func (x *AuditScores) GetAuditScoreScopeValue() int64 {
	if x != nil {
		return x.AuditScoreScopeValue
	}
	return 0
}

func (x *AuditScores) GetAuditScoreScopeSummary() []string {
	if x != nil {
		return x.AuditScoreScopeSummary
	}
	return nil
}

func (x *AuditScores) GetAuditScoreRecommendations() []string {
	if x != nil {
		return x.AuditScoreRecommendations
	}
	return nil
}

type AuditScoreResults struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AuditPageUrl  string                 `protobuf:"bytes,1,opt,name=audit_page_url,json=auditPageUrl,proto3" json:"audit_page_url,omitempty"`
	AuditScores   []*AuditScores         `protobuf:"bytes,2,rep,name=audit_scores,json=auditScores,proto3" json:"audit_scores,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AuditScoreResults) Reset() {
	*x = AuditScoreResults{}
	mi := &file_listing_products_v1_seo_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AuditScoreResults) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuditScoreResults) ProtoMessage() {}

func (x *AuditScoreResults) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_seo_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuditScoreResults.ProtoReflect.Descriptor instead.
func (*AuditScoreResults) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_seo_proto_rawDescGZIP(), []int{12}
}

func (x *AuditScoreResults) GetAuditPageUrl() string {
	if x != nil {
		return x.AuditPageUrl
	}
	return ""
}

func (x *AuditScoreResults) GetAuditScores() []*AuditScores {
	if x != nil {
		return x.AuditScores
	}
	return nil
}

type GetAIOAuditRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BusinessId    string                 `protobuf:"bytes,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	BrandName     string                 `protobuf:"bytes,2,opt,name=brand_name,json=brandName,proto3" json:"brand_name,omitempty"`
	WebsiteUrl    string                 `protobuf:"bytes,3,opt,name=website_url,json=websiteUrl,proto3" json:"website_url,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAIOAuditRequest) Reset() {
	*x = GetAIOAuditRequest{}
	mi := &file_listing_products_v1_seo_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAIOAuditRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAIOAuditRequest) ProtoMessage() {}

func (x *GetAIOAuditRequest) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_seo_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAIOAuditRequest.ProtoReflect.Descriptor instead.
func (*GetAIOAuditRequest) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_seo_proto_rawDescGZIP(), []int{13}
}

func (x *GetAIOAuditRequest) GetBusinessId() string {
	if x != nil {
		return x.BusinessId
	}
	return ""
}

func (x *GetAIOAuditRequest) GetBrandName() string {
	if x != nil {
		return x.BrandName
	}
	return ""
}

func (x *GetAIOAuditRequest) GetWebsiteUrl() string {
	if x != nil {
		return x.WebsiteUrl
	}
	return ""
}

type GetAIOAuditResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Audit         *AIOAuditResults       `protobuf:"bytes,1,opt,name=audit,proto3" json:"audit,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAIOAuditResponse) Reset() {
	*x = GetAIOAuditResponse{}
	mi := &file_listing_products_v1_seo_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAIOAuditResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAIOAuditResponse) ProtoMessage() {}

func (x *GetAIOAuditResponse) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_seo_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAIOAuditResponse.ProtoReflect.Descriptor instead.
func (*GetAIOAuditResponse) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_seo_proto_rawDescGZIP(), []int{14}
}

func (x *GetAIOAuditResponse) GetAudit() *AIOAuditResults {
	if x != nil {
		return x.Audit
	}
	return nil
}

type GetAllAIOAuditScoreResultsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BusinessId    string                 `protobuf:"bytes,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	BrandName     string                 `protobuf:"bytes,2,opt,name=brand_name,json=brandName,proto3" json:"brand_name,omitempty"`
	WebsiteUrl    string                 `protobuf:"bytes,3,opt,name=website_url,json=websiteUrl,proto3" json:"website_url,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAllAIOAuditScoreResultsRequest) Reset() {
	*x = GetAllAIOAuditScoreResultsRequest{}
	mi := &file_listing_products_v1_seo_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllAIOAuditScoreResultsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllAIOAuditScoreResultsRequest) ProtoMessage() {}

func (x *GetAllAIOAuditScoreResultsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_seo_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllAIOAuditScoreResultsRequest.ProtoReflect.Descriptor instead.
func (*GetAllAIOAuditScoreResultsRequest) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_seo_proto_rawDescGZIP(), []int{15}
}

func (x *GetAllAIOAuditScoreResultsRequest) GetBusinessId() string {
	if x != nil {
		return x.BusinessId
	}
	return ""
}

func (x *GetAllAIOAuditScoreResultsRequest) GetBrandName() string {
	if x != nil {
		return x.BrandName
	}
	return ""
}

func (x *GetAllAIOAuditScoreResultsRequest) GetWebsiteUrl() string {
	if x != nil {
		return x.WebsiteUrl
	}
	return ""
}

type GetAllAIOAuditScoreResultsResponse struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	AuditScoreResults []*AuditScoreResults   `protobuf:"bytes,1,rep,name=audit_score_results,json=auditScoreResults,proto3" json:"audit_score_results,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *GetAllAIOAuditScoreResultsResponse) Reset() {
	*x = GetAllAIOAuditScoreResultsResponse{}
	mi := &file_listing_products_v1_seo_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllAIOAuditScoreResultsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllAIOAuditScoreResultsResponse) ProtoMessage() {}

func (x *GetAllAIOAuditScoreResultsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_seo_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllAIOAuditScoreResultsResponse.ProtoReflect.Descriptor instead.
func (*GetAllAIOAuditScoreResultsResponse) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_seo_proto_rawDescGZIP(), []int{16}
}

func (x *GetAllAIOAuditScoreResultsResponse) GetAuditScoreResults() []*AuditScoreResults {
	if x != nil {
		return x.AuditScoreResults
	}
	return nil
}

type GetAIOAuditStatusRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BusinessId    string                 `protobuf:"bytes,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	BrandName     string                 `protobuf:"bytes,2,opt,name=brand_name,json=brandName,proto3" json:"brand_name,omitempty"`
	WebsiteUrl    string                 `protobuf:"bytes,3,opt,name=website_url,json=websiteUrl,proto3" json:"website_url,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAIOAuditStatusRequest) Reset() {
	*x = GetAIOAuditStatusRequest{}
	mi := &file_listing_products_v1_seo_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAIOAuditStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAIOAuditStatusRequest) ProtoMessage() {}

func (x *GetAIOAuditStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_seo_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAIOAuditStatusRequest.ProtoReflect.Descriptor instead.
func (*GetAIOAuditStatusRequest) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_seo_proto_rawDescGZIP(), []int{17}
}

func (x *GetAIOAuditStatusRequest) GetBusinessId() string {
	if x != nil {
		return x.BusinessId
	}
	return ""
}

func (x *GetAIOAuditStatusRequest) GetBrandName() string {
	if x != nil {
		return x.BrandName
	}
	return ""
}

func (x *GetAIOAuditStatusRequest) GetWebsiteUrl() string {
	if x != nil {
		return x.WebsiteUrl
	}
	return ""
}

type GetAIOAuditStatusResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AuditStatus   string                 `protobuf:"bytes,1,opt,name=audit_status,json=auditStatus,proto3" json:"audit_status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAIOAuditStatusResponse) Reset() {
	*x = GetAIOAuditStatusResponse{}
	mi := &file_listing_products_v1_seo_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAIOAuditStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAIOAuditStatusResponse) ProtoMessage() {}

func (x *GetAIOAuditStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_seo_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAIOAuditStatusResponse.ProtoReflect.Descriptor instead.
func (*GetAIOAuditStatusResponse) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_seo_proto_rawDescGZIP(), []int{18}
}

func (x *GetAIOAuditStatusResponse) GetAuditStatus() string {
	if x != nil {
		return x.AuditStatus
	}
	return ""
}

type TriggerAIOAuditRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BusinessId    string                 `protobuf:"bytes,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	BrandName     string                 `protobuf:"bytes,2,opt,name=brand_name,json=brandName,proto3" json:"brand_name,omitempty"`
	WebsiteUrl    string                 `protobuf:"bytes,3,opt,name=website_url,json=websiteUrl,proto3" json:"website_url,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TriggerAIOAuditRequest) Reset() {
	*x = TriggerAIOAuditRequest{}
	mi := &file_listing_products_v1_seo_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TriggerAIOAuditRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TriggerAIOAuditRequest) ProtoMessage() {}

func (x *TriggerAIOAuditRequest) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_seo_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TriggerAIOAuditRequest.ProtoReflect.Descriptor instead.
func (*TriggerAIOAuditRequest) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_seo_proto_rawDescGZIP(), []int{19}
}

func (x *TriggerAIOAuditRequest) GetBusinessId() string {
	if x != nil {
		return x.BusinessId
	}
	return ""
}

func (x *TriggerAIOAuditRequest) GetBrandName() string {
	if x != nil {
		return x.BrandName
	}
	return ""
}

func (x *TriggerAIOAuditRequest) GetWebsiteUrl() string {
	if x != nil {
		return x.WebsiteUrl
	}
	return ""
}

type TriggerAIOAuditResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BusinessId    string                 `protobuf:"bytes,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	BrandName     string                 `protobuf:"bytes,2,opt,name=brand_name,json=brandName,proto3" json:"brand_name,omitempty"`
	WebsiteUrl    string                 `protobuf:"bytes,3,opt,name=website_url,json=websiteUrl,proto3" json:"website_url,omitempty"`
	Message       string                 `protobuf:"bytes,4,opt,name=message,proto3" json:"message,omitempty"`
	AuditStatus   string                 `protobuf:"bytes,5,opt,name=audit_status,json=auditStatus,proto3" json:"audit_status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TriggerAIOAuditResponse) Reset() {
	*x = TriggerAIOAuditResponse{}
	mi := &file_listing_products_v1_seo_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TriggerAIOAuditResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TriggerAIOAuditResponse) ProtoMessage() {}

func (x *TriggerAIOAuditResponse) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_seo_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TriggerAIOAuditResponse.ProtoReflect.Descriptor instead.
func (*TriggerAIOAuditResponse) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_seo_proto_rawDescGZIP(), []int{20}
}

func (x *TriggerAIOAuditResponse) GetBusinessId() string {
	if x != nil {
		return x.BusinessId
	}
	return ""
}

func (x *TriggerAIOAuditResponse) GetBrandName() string {
	if x != nil {
		return x.BrandName
	}
	return ""
}

func (x *TriggerAIOAuditResponse) GetWebsiteUrl() string {
	if x != nil {
		return x.WebsiteUrl
	}
	return ""
}

func (x *TriggerAIOAuditResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *TriggerAIOAuditResponse) GetAuditStatus() string {
	if x != nil {
		return x.AuditStatus
	}
	return ""
}

type GetSEODataSummaryResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Data is the most recent entry for the business for each keyword.
	Data []*SEODataSummary `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	// Previous data is the oldest data entry for the business for each keyword within the date range.
	PreviousData  []*SEODataSummary `protobuf:"bytes,2,rep,name=previous_data,json=previousData,proto3" json:"previous_data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSEODataSummaryResponse) Reset() {
	*x = GetSEODataSummaryResponse{}
	mi := &file_listing_products_v1_seo_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSEODataSummaryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSEODataSummaryResponse) ProtoMessage() {}

func (x *GetSEODataSummaryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_seo_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSEODataSummaryResponse.ProtoReflect.Descriptor instead.
func (*GetSEODataSummaryResponse) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_seo_proto_rawDescGZIP(), []int{21}
}

func (x *GetSEODataSummaryResponse) GetData() []*SEODataSummary {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *GetSEODataSummaryResponse) GetPreviousData() []*SEODataSummary {
	if x != nil {
		return x.PreviousData
	}
	return nil
}

type GetLocalSearchSEODataRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BusinessId    string                 `protobuf:"bytes,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	Keyword       string                 `protobuf:"bytes,2,opt,name=keyword,proto3" json:"keyword,omitempty"`
	StartDate     *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	EndDate       *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetLocalSearchSEODataRequest) Reset() {
	*x = GetLocalSearchSEODataRequest{}
	mi := &file_listing_products_v1_seo_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetLocalSearchSEODataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLocalSearchSEODataRequest) ProtoMessage() {}

func (x *GetLocalSearchSEODataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_seo_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLocalSearchSEODataRequest.ProtoReflect.Descriptor instead.
func (*GetLocalSearchSEODataRequest) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_seo_proto_rawDescGZIP(), []int{22}
}

func (x *GetLocalSearchSEODataRequest) GetBusinessId() string {
	if x != nil {
		return x.BusinessId
	}
	return ""
}

func (x *GetLocalSearchSEODataRequest) GetKeyword() string {
	if x != nil {
		return x.Keyword
	}
	return ""
}

func (x *GetLocalSearchSEODataRequest) GetStartDate() *timestamppb.Timestamp {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *GetLocalSearchSEODataRequest) GetEndDate() *timestamppb.Timestamp {
	if x != nil {
		return x.EndDate
	}
	return nil
}

type GetLocalSearchSEODataResponse struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Keyword         string                 `protobuf:"bytes,1,opt,name=keyword,proto3" json:"keyword,omitempty"`
	LocalSearchData []*LocalSearchData     `protobuf:"bytes,2,rep,name=local_search_data,json=localSearchData,proto3" json:"local_search_data,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *GetLocalSearchSEODataResponse) Reset() {
	*x = GetLocalSearchSEODataResponse{}
	mi := &file_listing_products_v1_seo_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetLocalSearchSEODataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLocalSearchSEODataResponse) ProtoMessage() {}

func (x *GetLocalSearchSEODataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_seo_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLocalSearchSEODataResponse.ProtoReflect.Descriptor instead.
func (*GetLocalSearchSEODataResponse) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_seo_proto_rawDescGZIP(), []int{23}
}

func (x *GetLocalSearchSEODataResponse) GetKeyword() string {
	if x != nil {
		return x.Keyword
	}
	return ""
}

func (x *GetLocalSearchSEODataResponse) GetLocalSearchData() []*LocalSearchData {
	if x != nil {
		return x.LocalSearchData
	}
	return nil
}

// AddonActivation is a representation of a addon activation in the Vendasta platform
type AddonActivation struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// A prerequisite ID representing the customer/business.
	BusinessId string `protobuf:"bytes,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// A prerequisite marketplace vendor's ID's of the app the addons belong to.
	AppId string `protobuf:"bytes,2,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	// A prerequisite marketplace vendor's ID's of the addon activated.
	AddonId string `protobuf:"bytes,3,opt,name=addon_id,json=addonId,proto3" json:"addon_id,omitempty"`
	// UTC time the addon was activated.
	Activated *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=activated,proto3" json:"activated,omitempty"`
	// UTC time the addon was or will be deactivated, if ever.
	Deactivated *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=deactivated,proto3" json:"deactivated,omitempty"`
	// The state of activation to determine if the addon is active or cancelled
	Status AddonActivation_AddonActivationStatus `protobuf:"varint,7,opt,name=status,proto3,enum=listing_products.v1.AddonActivation_AddonActivationStatus" json:"status,omitempty"`
	// A flag determining whether or not the activation is in trial
	IsTrial bool `protobuf:"varint,8,opt,name=is_trial,json=isTrial,proto3" json:"is_trial,omitempty"`
	// Determines the number of activations for a given add-on
	Count         int32 `protobuf:"varint,9,opt,name=count,proto3" json:"count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddonActivation) Reset() {
	*x = AddonActivation{}
	mi := &file_listing_products_v1_seo_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddonActivation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddonActivation) ProtoMessage() {}

func (x *AddonActivation) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_seo_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddonActivation.ProtoReflect.Descriptor instead.
func (*AddonActivation) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_seo_proto_rawDescGZIP(), []int{24}
}

func (x *AddonActivation) GetBusinessId() string {
	if x != nil {
		return x.BusinessId
	}
	return ""
}

func (x *AddonActivation) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *AddonActivation) GetAddonId() string {
	if x != nil {
		return x.AddonId
	}
	return ""
}

func (x *AddonActivation) GetActivated() *timestamppb.Timestamp {
	if x != nil {
		return x.Activated
	}
	return nil
}

func (x *AddonActivation) GetDeactivated() *timestamppb.Timestamp {
	if x != nil {
		return x.Deactivated
	}
	return nil
}

func (x *AddonActivation) GetStatus() AddonActivation_AddonActivationStatus {
	if x != nil {
		return x.Status
	}
	return AddonActivation_ACTIVATION_STATUS_NOT_SPECIFIED
}

func (x *AddonActivation) GetIsTrial() bool {
	if x != nil {
		return x.IsTrial
	}
	return false
}

func (x *AddonActivation) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

type GetSEODataResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Data is the most recent entry for the business for each keyword.
	Data []*SEOData `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	// Previous data is the oldest data entry for the business for each keyword within the date range.
	PreviousData  []*SEOData `protobuf:"bytes,2,rep,name=previous_data,json=previousData,proto3" json:"previous_data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSEODataResponse) Reset() {
	*x = GetSEODataResponse{}
	mi := &file_listing_products_v1_seo_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSEODataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSEODataResponse) ProtoMessage() {}

func (x *GetSEODataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_seo_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSEODataResponse.ProtoReflect.Descriptor instead.
func (*GetSEODataResponse) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_seo_proto_rawDescGZIP(), []int{25}
}

func (x *GetSEODataResponse) GetData() []*SEOData {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *GetSEODataResponse) GetPreviousData() []*SEOData {
	if x != nil {
		return x.PreviousData
	}
	return nil
}

type BusinessKeywords struct {
	state      protoimpl.MessageState `protogen:"open.v1"`
	BusinessId string                 `protobuf:"bytes,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// Optional. If not included, the business's stored keywords will be used.
	Keywords      []string `protobuf:"bytes,3,rep,name=keywords,proto3" json:"keywords,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BusinessKeywords) Reset() {
	*x = BusinessKeywords{}
	mi := &file_listing_products_v1_seo_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BusinessKeywords) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessKeywords) ProtoMessage() {}

func (x *BusinessKeywords) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_seo_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessKeywords.ProtoReflect.Descriptor instead.
func (*BusinessKeywords) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_seo_proto_rawDescGZIP(), []int{26}
}

func (x *BusinessKeywords) GetBusinessId() string {
	if x != nil {
		return x.BusinessId
	}
	return ""
}

func (x *BusinessKeywords) GetKeywords() []string {
	if x != nil {
		return x.Keywords
	}
	return nil
}

type StartLocalSEODataWorkflowRequest struct {
	state      protoimpl.MessageState `protogen:"open.v1"`
	Businesses []*BusinessKeywords    `protobuf:"bytes,1,rep,name=businesses,proto3" json:"businesses,omitempty"`
	// force_serp_workflow flag will skip date checks and run the SERP workflow
	ForceSerpWorkflow bool `protobuf:"varint,2,opt,name=force_serp_workflow,json=forceSerpWorkflow,proto3" json:"force_serp_workflow,omitempty"`
	// Optional: If included, the workflow will save results for the provided date instead of the current date
	Date *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=date,proto3" json:"date,omitempty"`
	// force_keyword_info_workflow flag will skip date checks and run the Keyword Info workflow
	ForceKeywordInfoWorkflow bool `protobuf:"varint,4,opt,name=force_keyword_info_workflow,json=forceKeywordInfoWorkflow,proto3" json:"force_keyword_info_workflow,omitempty"`
	// force_serp_workflow flag will skip date checks and run the Suggested Keywords workflow
	ForceSuggestedKeywordsWorkflow bool `protobuf:"varint,5,opt,name=force_suggested_keywords_workflow,json=forceSuggestedKeywordsWorkflow,proto3" json:"force_suggested_keywords_workflow,omitempty"`
	// ignore_data_lake_results will re-fetch data from DataForSEO even if we have it stored in our data lake already
	IgnoreDataLakeResults bool `protobuf:"varint,6,opt,name=ignore_data_lake_results,json=ignoreDataLakeResults,proto3" json:"ignore_data_lake_results,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *StartLocalSEODataWorkflowRequest) Reset() {
	*x = StartLocalSEODataWorkflowRequest{}
	mi := &file_listing_products_v1_seo_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StartLocalSEODataWorkflowRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartLocalSEODataWorkflowRequest) ProtoMessage() {}

func (x *StartLocalSEODataWorkflowRequest) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_seo_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartLocalSEODataWorkflowRequest.ProtoReflect.Descriptor instead.
func (*StartLocalSEODataWorkflowRequest) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_seo_proto_rawDescGZIP(), []int{27}
}

func (x *StartLocalSEODataWorkflowRequest) GetBusinesses() []*BusinessKeywords {
	if x != nil {
		return x.Businesses
	}
	return nil
}

func (x *StartLocalSEODataWorkflowRequest) GetForceSerpWorkflow() bool {
	if x != nil {
		return x.ForceSerpWorkflow
	}
	return false
}

func (x *StartLocalSEODataWorkflowRequest) GetDate() *timestamppb.Timestamp {
	if x != nil {
		return x.Date
	}
	return nil
}

func (x *StartLocalSEODataWorkflowRequest) GetForceKeywordInfoWorkflow() bool {
	if x != nil {
		return x.ForceKeywordInfoWorkflow
	}
	return false
}

func (x *StartLocalSEODataWorkflowRequest) GetForceSuggestedKeywordsWorkflow() bool {
	if x != nil {
		return x.ForceSuggestedKeywordsWorkflow
	}
	return false
}

func (x *StartLocalSEODataWorkflowRequest) GetIgnoreDataLakeResults() bool {
	if x != nil {
		return x.IgnoreDataLakeResults
	}
	return false
}

type GetSEOSettingsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BusinessId    string                 `protobuf:"bytes,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSEOSettingsRequest) Reset() {
	*x = GetSEOSettingsRequest{}
	mi := &file_listing_products_v1_seo_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSEOSettingsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSEOSettingsRequest) ProtoMessage() {}

func (x *GetSEOSettingsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_seo_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSEOSettingsRequest.ProtoReflect.Descriptor instead.
func (*GetSEOSettingsRequest) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_seo_proto_rawDescGZIP(), []int{28}
}

func (x *GetSEOSettingsRequest) GetBusinessId() string {
	if x != nil {
		return x.BusinessId
	}
	return ""
}

type SaveSEOSettingsRequest struct {
	state               protoimpl.MessageState    `protogen:"open.v1"`
	BusinessId          string                    `protobuf:"bytes,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	LocalSearchRadius   float64                   `protobuf:"fixed64,2,opt,name=local_search_radius,json=localSearchRadius,proto3" json:"local_search_radius,omitempty"`
	FavoriteKeywords    []string                  `protobuf:"bytes,3,rep,name=favorite_keywords,json=favoriteKeywords,proto3" json:"favorite_keywords,omitempty"`
	FieldMask           *vendasta_types.FieldMask `protobuf:"bytes,4,opt,name=field_mask,json=fieldMask,proto3" json:"field_mask,omitempty"`
	IsFullSearchEnabled bool                      `protobuf:"varint,5,opt,name=is_full_search_enabled,json=isFullSearchEnabled,proto3" json:"is_full_search_enabled,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *SaveSEOSettingsRequest) Reset() {
	*x = SaveSEOSettingsRequest{}
	mi := &file_listing_products_v1_seo_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SaveSEOSettingsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveSEOSettingsRequest) ProtoMessage() {}

func (x *SaveSEOSettingsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_seo_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveSEOSettingsRequest.ProtoReflect.Descriptor instead.
func (*SaveSEOSettingsRequest) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_seo_proto_rawDescGZIP(), []int{29}
}

func (x *SaveSEOSettingsRequest) GetBusinessId() string {
	if x != nil {
		return x.BusinessId
	}
	return ""
}

func (x *SaveSEOSettingsRequest) GetLocalSearchRadius() float64 {
	if x != nil {
		return x.LocalSearchRadius
	}
	return 0
}

func (x *SaveSEOSettingsRequest) GetFavoriteKeywords() []string {
	if x != nil {
		return x.FavoriteKeywords
	}
	return nil
}

func (x *SaveSEOSettingsRequest) GetFieldMask() *vendasta_types.FieldMask {
	if x != nil {
		return x.FieldMask
	}
	return nil
}

func (x *SaveSEOSettingsRequest) GetIsFullSearchEnabled() bool {
	if x != nil {
		return x.IsFullSearchEnabled
	}
	return false
}

type SEOSettingsResponse struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	BusinessId          string                 `protobuf:"bytes,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	LocalSearchRadius   float64                `protobuf:"fixed64,2,opt,name=local_search_radius,json=localSearchRadius,proto3" json:"local_search_radius,omitempty"`
	FavoriteKeywords    []string               `protobuf:"bytes,3,rep,name=favorite_keywords,json=favoriteKeywords,proto3" json:"favorite_keywords,omitempty"`
	IsFullSearchEnabled bool                   `protobuf:"varint,4,opt,name=is_full_search_enabled,json=isFullSearchEnabled,proto3" json:"is_full_search_enabled,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *SEOSettingsResponse) Reset() {
	*x = SEOSettingsResponse{}
	mi := &file_listing_products_v1_seo_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SEOSettingsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SEOSettingsResponse) ProtoMessage() {}

func (x *SEOSettingsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_seo_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SEOSettingsResponse.ProtoReflect.Descriptor instead.
func (*SEOSettingsResponse) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_seo_proto_rawDescGZIP(), []int{30}
}

func (x *SEOSettingsResponse) GetBusinessId() string {
	if x != nil {
		return x.BusinessId
	}
	return ""
}

func (x *SEOSettingsResponse) GetLocalSearchRadius() float64 {
	if x != nil {
		return x.LocalSearchRadius
	}
	return 0
}

func (x *SEOSettingsResponse) GetFavoriteKeywords() []string {
	if x != nil {
		return x.FavoriteKeywords
	}
	return nil
}

func (x *SEOSettingsResponse) GetIsFullSearchEnabled() bool {
	if x != nil {
		return x.IsFullSearchEnabled
	}
	return false
}

type GetActiveSEOAddonsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// A prerequisite ID representing the customer/business.
	BusinessId    string `protobuf:"bytes,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetActiveSEOAddonsRequest) Reset() {
	*x = GetActiveSEOAddonsRequest{}
	mi := &file_listing_products_v1_seo_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetActiveSEOAddonsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetActiveSEOAddonsRequest) ProtoMessage() {}

func (x *GetActiveSEOAddonsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_seo_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetActiveSEOAddonsRequest.ProtoReflect.Descriptor instead.
func (*GetActiveSEOAddonsRequest) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_seo_proto_rawDescGZIP(), []int{31}
}

func (x *GetActiveSEOAddonsRequest) GetBusinessId() string {
	if x != nil {
		return x.BusinessId
	}
	return ""
}

type GetActiveSEOAddonsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ActiveAddons  []*AddonActivation     `protobuf:"bytes,1,rep,name=active_addons,json=activeAddons,proto3" json:"active_addons,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetActiveSEOAddonsResponse) Reset() {
	*x = GetActiveSEOAddonsResponse{}
	mi := &file_listing_products_v1_seo_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetActiveSEOAddonsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetActiveSEOAddonsResponse) ProtoMessage() {}

func (x *GetActiveSEOAddonsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_seo_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetActiveSEOAddonsResponse.ProtoReflect.Descriptor instead.
func (*GetActiveSEOAddonsResponse) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_seo_proto_rawDescGZIP(), []int{32}
}

func (x *GetActiveSEOAddonsResponse) GetActiveAddons() []*AddonActivation {
	if x != nil {
		return x.ActiveAddons
	}
	return nil
}

type StartSEOCategoryWorkflowRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BusinessIds   []string               `protobuf:"bytes,1,rep,name=business_ids,json=businessIds,proto3" json:"business_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StartSEOCategoryWorkflowRequest) Reset() {
	*x = StartSEOCategoryWorkflowRequest{}
	mi := &file_listing_products_v1_seo_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StartSEOCategoryWorkflowRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartSEOCategoryWorkflowRequest) ProtoMessage() {}

func (x *StartSEOCategoryWorkflowRequest) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_seo_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartSEOCategoryWorkflowRequest.ProtoReflect.Descriptor instead.
func (*StartSEOCategoryWorkflowRequest) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_seo_proto_rawDescGZIP(), []int{33}
}

func (x *StartSEOCategoryWorkflowRequest) GetBusinessIds() []string {
	if x != nil {
		return x.BusinessIds
	}
	return nil
}

type GetDataForSEOCategoryRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// A prerequisite ID representing the customer/business
	BusinessId    string `protobuf:"bytes,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetDataForSEOCategoryRequest) Reset() {
	*x = GetDataForSEOCategoryRequest{}
	mi := &file_listing_products_v1_seo_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDataForSEOCategoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDataForSEOCategoryRequest) ProtoMessage() {}

func (x *GetDataForSEOCategoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_seo_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDataForSEOCategoryRequest.ProtoReflect.Descriptor instead.
func (*GetDataForSEOCategoryRequest) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_seo_proto_rawDescGZIP(), []int{34}
}

func (x *GetDataForSEOCategoryRequest) GetBusinessId() string {
	if x != nil {
		return x.BusinessId
	}
	return ""
}

type GetDataForSEOCategoryResponse struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	BusinessId        string                 `protobuf:"bytes,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	PrimaryCategoryId string                 `protobuf:"bytes,2,opt,name=primary_category_id,json=primaryCategoryId,proto3" json:"primary_category_id,omitempty"`
	CategoryIds       []string               `protobuf:"bytes,3,rep,name=category_ids,json=categoryIds,proto3" json:"category_ids,omitempty"`
	TaskId            string                 `protobuf:"bytes,4,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	RawResponse       string                 `protobuf:"bytes,5,opt,name=raw_response,json=rawResponse,proto3" json:"raw_response,omitempty"`
	Created           *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=created,proto3" json:"created,omitempty"`
	Updated           *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=updated,proto3" json:"updated,omitempty"`
	Deleted           *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=deleted,proto3" json:"deleted,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *GetDataForSEOCategoryResponse) Reset() {
	*x = GetDataForSEOCategoryResponse{}
	mi := &file_listing_products_v1_seo_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDataForSEOCategoryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDataForSEOCategoryResponse) ProtoMessage() {}

func (x *GetDataForSEOCategoryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_listing_products_v1_seo_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDataForSEOCategoryResponse.ProtoReflect.Descriptor instead.
func (*GetDataForSEOCategoryResponse) Descriptor() ([]byte, []int) {
	return file_listing_products_v1_seo_proto_rawDescGZIP(), []int{35}
}

func (x *GetDataForSEOCategoryResponse) GetBusinessId() string {
	if x != nil {
		return x.BusinessId
	}
	return ""
}

func (x *GetDataForSEOCategoryResponse) GetPrimaryCategoryId() string {
	if x != nil {
		return x.PrimaryCategoryId
	}
	return ""
}

func (x *GetDataForSEOCategoryResponse) GetCategoryIds() []string {
	if x != nil {
		return x.CategoryIds
	}
	return nil
}

func (x *GetDataForSEOCategoryResponse) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *GetDataForSEOCategoryResponse) GetRawResponse() string {
	if x != nil {
		return x.RawResponse
	}
	return ""
}

func (x *GetDataForSEOCategoryResponse) GetCreated() *timestamppb.Timestamp {
	if x != nil {
		return x.Created
	}
	return nil
}

func (x *GetDataForSEOCategoryResponse) GetUpdated() *timestamppb.Timestamp {
	if x != nil {
		return x.Updated
	}
	return nil
}

func (x *GetDataForSEOCategoryResponse) GetDeleted() *timestamppb.Timestamp {
	if x != nil {
		return x.Deleted
	}
	return nil
}

var File_listing_products_v1_seo_proto protoreflect.FileDescriptor

const file_listing_products_v1_seo_proto_rawDesc = "" +
	"\n" +
	"\x1dlisting_products/v1/seo.proto\x12\x13listing_products.v1\x1a\x1fgoogle/protobuf/timestamp.proto\x1a)listing_products/v1/listing_profile.proto\x1a\x1fvendasta_types/field_mask.proto\"\xa9\x02\n" +
	"\x0eSEODataSummary\x12\x18\n" +
	"\akeyword\x18\x01 \x01(\tR\akeyword\x12.\n" +
	"\x04date\x18\x02 \x01(\v2\x1a.google.protobuf.TimestampR\x04date\x12\x1d\n" +
	"\n" +
	"local_rank\x18\x03 \x01(\x01R\tlocalRank\x12!\n" +
	"\forganic_rank\x18\x04 \x01(\x01R\vorganicRank\x12\x1e\n" +
	"\n" +
	"difficulty\x18\x05 \x01(\x03R\n" +
	"difficulty\x12#\n" +
	"\rsearch_volume\x18\x06 \x01(\x03R\fsearchVolume\x12#\n" +
	"\rsearch_radius\x18\a \x01(\x01R\fsearchRadius\x12!\n" +
	"\fworkflow_url\x18\b \x01(\tR\vworkflowUrl\"\xef\x02\n" +
	"\aSEOData\x12\x18\n" +
	"\akeyword\x18\x01 \x01(\tR\akeyword\x12.\n" +
	"\x04date\x18\x02 \x01(\v2\x1a.google.protobuf.TimestampR\x04date\x12\x1d\n" +
	"\n" +
	"local_rank\x18\x03 \x01(\x01R\tlocalRank\x12!\n" +
	"\forganic_rank\x18\x04 \x01(\x01R\vorganicRank\x12\x1e\n" +
	"\n" +
	"difficulty\x18\x05 \x01(\x03R\n" +
	"difficulty\x12#\n" +
	"\rsearch_volume\x18\x06 \x01(\x03R\fsearchVolume\x12K\n" +
	"\x0elocal_searches\x18\a \x03(\v2$.listing_products.v1.LocalSearchDataR\rlocalSearches\x12#\n" +
	"\rsearch_radius\x18\b \x01(\x01R\fsearchRadius\x12!\n" +
	"\fworkflow_url\x18\t \x01(\tR\vworkflowUrl\"\xeb\x01\n" +
	"\x0fLocalSearchData\x12\x18\n" +
	"\akeyword\x18\x01 \x01(\tR\akeyword\x129\n" +
	"\bvicinity\x18\x02 \x01(\x0e2\x1d.listing_products.v1.VicinityR\bvicinity\x12A\n" +
	"\x0fsearch_location\x18\x03 \x01(\v2\x18.listing_products.v1.GeoR\x0esearchLocation\x12@\n" +
	"\aresults\x18\x04 \x03(\v2&.listing_products.v1.LocalSearchResultR\aresults\"\xd0\x02\n" +
	"\x11LocalSearchResult\x12#\n" +
	"\rbusiness_name\x18\x01 \x01(\tR\fbusinessName\x12\x18\n" +
	"\aaddress\x18\x02 \x01(\tR\aaddress\x12\x10\n" +
	"\x03url\x18\x03 \x01(\tR\x03url\x12\x12\n" +
	"\x04rank\x18\x04 \x01(\tR\x04rank\x12(\n" +
	"\x10is_main_business\x18\x05 \x01(\bR\x0eisMainBusiness\x12A\n" +
	"\areviews\x18\x06 \x01(\v2'.listing_products.v1.LocalSearchReviewsR\areviews\x12!\n" +
	"\fphone_number\x18\a \x01(\tR\vphoneNumber\x12F\n" +
	"\fclaim_status\x18\b \x01(\x0e2#.listing_products.v1.GBPClaimStatusR\vclaimStatus\"B\n" +
	"\x12LocalSearchReviews\x12\x16\n" +
	"\x06rating\x18\x01 \x01(\x01R\x06rating\x12\x14\n" +
	"\x05count\x18\x02 \x01(\tR\x05count\"\xc2\x01\n" +
	"\x11GetSEODataRequest\x12\x1f\n" +
	"\vbusiness_id\x18\x01 \x01(\tR\n" +
	"businessId\x12\x1a\n" +
	"\bkeywords\x18\x02 \x03(\tR\bkeywords\x129\n" +
	"\n" +
	"start_date\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\tstartDate\x125\n" +
	"\bend_date\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\aendDate\"\xc9\x01\n" +
	"\x18GetSEODataSummaryRequest\x12\x1f\n" +
	"\vbusiness_id\x18\x01 \x01(\tR\n" +
	"businessId\x12\x1a\n" +
	"\bkeywords\x18\x02 \x03(\tR\bkeywords\x129\n" +
	"\n" +
	"start_date\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\tstartDate\x125\n" +
	"\bend_date\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\aendDate\"8\n" +
	"\x15GetAllAIOAuditRequest\x12\x1f\n" +
	"\vbusiness_id\x18\x01 \x01(\tR\n" +
	"businessId\"T\n" +
	"\x16GetAllAIOAuditResponse\x12:\n" +
	"\x05audit\x18\x01 \x03(\v2$.listing_products.v1.AIOAuditResultsR\x05audit\"\xa4\x04\n" +
	"\x0fAIOAuditResults\x12\x1f\n" +
	"\vbusiness_id\x18\x01 \x01(\tR\n" +
	"businessId\x12\x1d\n" +
	"\n" +
	"brand_name\x18\x02 \x01(\tR\tbrandName\x12\x1f\n" +
	"\vwebsite_url\x18\x03 \x01(\tR\n" +
	"websiteUrl\x12\x1d\n" +
	"\n" +
	"audit_date\x18\x04 \x01(\tR\tauditDate\x129\n" +
	"\n" +
	"start_date\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\tstartDate\x12\x1f\n" +
	"\vtotal_pages\x18\x06 \x01(\x03R\n" +
	"totalPages\x12!\n" +
	"\faudit_status\x18\a \x01(\tR\vauditStatus\x12#\n" +
	"\raudit_summary\x18\b \x01(\tR\fauditSummary\x12C\n" +
	"\vaudit_pages\x18\t \x03(\v2\".listing_products.v1.AuditPageDataR\n" +
	"auditPages\x12V\n" +
	"\x13audit_score_results\x18\n" +
	" \x03(\v2&.listing_products.v1.AuditScoreResultsR\x11auditScoreResults\x12\x18\n" +
	"\akeyword\x18\v \x01(\tR\akeyword\x12\x19\n" +
	"\baudit_id\x18\f \x01(\tR\aauditId\x12\x1b\n" +
	"\taudit_url\x18\r \x01(\tR\bauditUrl\"G\n" +
	"\rAuditPageData\x12\x19\n" +
	"\bpage_url\x18\x01 \x01(\tR\apageUrl\x12\x1b\n" +
	"\tpage_data\x18\x02 \x01(\tR\bpageData\"\xf4\x01\n" +
	"\vAuditScores\x123\n" +
	"\x16audit_score_scope_name\x18\x01 \x01(\tR\x13auditScoreScopeName\x125\n" +
	"\x17audit_score_scope_value\x18\x02 \x01(\x03R\x14auditScoreScopeValue\x129\n" +
	"\x19audit_score_scope_summary\x18\x03 \x03(\tR\x16auditScoreScopeSummary\x12>\n" +
	"\x1baudit_score_recommendations\x18\x04 \x03(\tR\x19auditScoreRecommendations\"~\n" +
	"\x11AuditScoreResults\x12$\n" +
	"\x0eaudit_page_url\x18\x01 \x01(\tR\fauditPageUrl\x12C\n" +
	"\faudit_scores\x18\x02 \x03(\v2 .listing_products.v1.AuditScoresR\vauditScores\"u\n" +
	"\x12GetAIOAuditRequest\x12\x1f\n" +
	"\vbusiness_id\x18\x01 \x01(\tR\n" +
	"businessId\x12\x1d\n" +
	"\n" +
	"brand_name\x18\x02 \x01(\tR\tbrandName\x12\x1f\n" +
	"\vwebsite_url\x18\x03 \x01(\tR\n" +
	"websiteUrl\"Q\n" +
	"\x13GetAIOAuditResponse\x12:\n" +
	"\x05audit\x18\x01 \x01(\v2$.listing_products.v1.AIOAuditResultsR\x05audit\"\x84\x01\n" +
	"!GetAllAIOAuditScoreResultsRequest\x12\x1f\n" +
	"\vbusiness_id\x18\x01 \x01(\tR\n" +
	"businessId\x12\x1d\n" +
	"\n" +
	"brand_name\x18\x02 \x01(\tR\tbrandName\x12\x1f\n" +
	"\vwebsite_url\x18\x03 \x01(\tR\n" +
	"websiteUrl\"|\n" +
	"\"GetAllAIOAuditScoreResultsResponse\x12V\n" +
	"\x13audit_score_results\x18\x01 \x03(\v2&.listing_products.v1.AuditScoreResultsR\x11auditScoreResults\"{\n" +
	"\x18GetAIOAuditStatusRequest\x12\x1f\n" +
	"\vbusiness_id\x18\x01 \x01(\tR\n" +
	"businessId\x12\x1d\n" +
	"\n" +
	"brand_name\x18\x02 \x01(\tR\tbrandName\x12\x1f\n" +
	"\vwebsite_url\x18\x03 \x01(\tR\n" +
	"websiteUrl\">\n" +
	"\x19GetAIOAuditStatusResponse\x12!\n" +
	"\faudit_status\x18\x01 \x01(\tR\vauditStatus\"y\n" +
	"\x16TriggerAIOAuditRequest\x12\x1f\n" +
	"\vbusiness_id\x18\x01 \x01(\tR\n" +
	"businessId\x12\x1d\n" +
	"\n" +
	"brand_name\x18\x02 \x01(\tR\tbrandName\x12\x1f\n" +
	"\vwebsite_url\x18\x03 \x01(\tR\n" +
	"websiteUrl\"\xb7\x01\n" +
	"\x17TriggerAIOAuditResponse\x12\x1f\n" +
	"\vbusiness_id\x18\x01 \x01(\tR\n" +
	"businessId\x12\x1d\n" +
	"\n" +
	"brand_name\x18\x02 \x01(\tR\tbrandName\x12\x1f\n" +
	"\vwebsite_url\x18\x03 \x01(\tR\n" +
	"websiteUrl\x12\x18\n" +
	"\amessage\x18\x04 \x01(\tR\amessage\x12!\n" +
	"\faudit_status\x18\x05 \x01(\tR\vauditStatus\"\x9e\x01\n" +
	"\x19GetSEODataSummaryResponse\x127\n" +
	"\x04data\x18\x01 \x03(\v2#.listing_products.v1.SEODataSummaryR\x04data\x12H\n" +
	"\rprevious_data\x18\x02 \x03(\v2#.listing_products.v1.SEODataSummaryR\fpreviousData\"\xcb\x01\n" +
	"\x1cGetLocalSearchSEODataRequest\x12\x1f\n" +
	"\vbusiness_id\x18\x01 \x01(\tR\n" +
	"businessId\x12\x18\n" +
	"\akeyword\x18\x02 \x01(\tR\akeyword\x129\n" +
	"\n" +
	"start_date\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\tstartDate\x125\n" +
	"\bend_date\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\aendDate\"\x8b\x01\n" +
	"\x1dGetLocalSearchSEODataResponse\x12\x18\n" +
	"\akeyword\x18\x01 \x01(\tR\akeyword\x12P\n" +
	"\x11local_search_data\x18\x02 \x03(\v2$.listing_products.v1.LocalSearchDataR\x0flocalSearchData\"\xa3\x04\n" +
	"\x0fAddonActivation\x12\x1f\n" +
	"\vbusiness_id\x18\x01 \x01(\tR\n" +
	"businessId\x12\x15\n" +
	"\x06app_id\x18\x02 \x01(\tR\x05appId\x12\x19\n" +
	"\baddon_id\x18\x03 \x01(\tR\aaddonId\x128\n" +
	"\tactivated\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\tactivated\x12<\n" +
	"\vdeactivated\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\vdeactivated\x12R\n" +
	"\x06status\x18\a \x01(\x0e2:.listing_products.v1.AddonActivation.AddonActivationStatusR\x06status\x12\x19\n" +
	"\bis_trial\x18\b \x01(\bR\aisTrial\x12\x14\n" +
	"\x05count\x18\t \x01(\x05R\x05count\"\xbf\x01\n" +
	"\x15AddonActivationStatus\x12#\n" +
	"\x1fACTIVATION_STATUS_NOT_SPECIFIED\x10\x00\x12\x1f\n" +
	"\x1bACTIVATION_STATUS_ACTIVATED\x10\x01\x12\x1d\n" +
	"\x19ACTIVATION_STATUS_PENDING\x10\x02\x12\x1e\n" +
	"\x1aACTIVATION_STATUS_CANCELED\x10\x03\x12!\n" +
	"\x1dACTIVATION_STATUS_DEACTIVATED\x10\x04\"\x89\x01\n" +
	"\x12GetSEODataResponse\x120\n" +
	"\x04data\x18\x01 \x03(\v2\x1c.listing_products.v1.SEODataR\x04data\x12A\n" +
	"\rprevious_data\x18\x02 \x03(\v2\x1c.listing_products.v1.SEODataR\fpreviousData\"O\n" +
	"\x10BusinessKeywords\x12\x1f\n" +
	"\vbusiness_id\x18\x01 \x01(\tR\n" +
	"businessId\x12\x1a\n" +
	"\bkeywords\x18\x03 \x03(\tR\bkeywords\"\x8c\x03\n" +
	" StartLocalSEODataWorkflowRequest\x12E\n" +
	"\n" +
	"businesses\x18\x01 \x03(\v2%.listing_products.v1.BusinessKeywordsR\n" +
	"businesses\x12.\n" +
	"\x13force_serp_workflow\x18\x02 \x01(\bR\x11forceSerpWorkflow\x12.\n" +
	"\x04date\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\x04date\x12=\n" +
	"\x1bforce_keyword_info_workflow\x18\x04 \x01(\bR\x18forceKeywordInfoWorkflow\x12I\n" +
	"!force_suggested_keywords_workflow\x18\x05 \x01(\bR\x1eforceSuggestedKeywordsWorkflow\x127\n" +
	"\x18ignore_data_lake_results\x18\x06 \x01(\bR\x15ignoreDataLakeResults\"8\n" +
	"\x15GetSEOSettingsRequest\x12\x1f\n" +
	"\vbusiness_id\x18\x01 \x01(\tR\n" +
	"businessId\"\x84\x02\n" +
	"\x16SaveSEOSettingsRequest\x12\x1f\n" +
	"\vbusiness_id\x18\x01 \x01(\tR\n" +
	"businessId\x12.\n" +
	"\x13local_search_radius\x18\x02 \x01(\x01R\x11localSearchRadius\x12+\n" +
	"\x11favorite_keywords\x18\x03 \x03(\tR\x10favoriteKeywords\x127\n" +
	"\n" +
	"field_mask\x18\x04 \x01(\v2\x18.vendastatypes.FieldMaskR\tfieldMask\x123\n" +
	"\x16is_full_search_enabled\x18\x05 \x01(\bR\x13isFullSearchEnabled\"\xc8\x01\n" +
	"\x13SEOSettingsResponse\x12\x1f\n" +
	"\vbusiness_id\x18\x01 \x01(\tR\n" +
	"businessId\x12.\n" +
	"\x13local_search_radius\x18\x02 \x01(\x01R\x11localSearchRadius\x12+\n" +
	"\x11favorite_keywords\x18\x03 \x03(\tR\x10favoriteKeywords\x123\n" +
	"\x16is_full_search_enabled\x18\x04 \x01(\bR\x13isFullSearchEnabled\"<\n" +
	"\x19GetActiveSEOAddonsRequest\x12\x1f\n" +
	"\vbusiness_id\x18\x01 \x01(\tR\n" +
	"businessId\"g\n" +
	"\x1aGetActiveSEOAddonsResponse\x12I\n" +
	"\ractive_addons\x18\x01 \x03(\v2$.listing_products.v1.AddonActivationR\factiveAddons\"D\n" +
	"\x1fStartSEOCategoryWorkflowRequest\x12!\n" +
	"\fbusiness_ids\x18\x01 \x03(\tR\vbusinessIds\"?\n" +
	"\x1cGetDataForSEOCategoryRequest\x12\x1f\n" +
	"\vbusiness_id\x18\x01 \x01(\tR\n" +
	"businessId\"\xf1\x02\n" +
	"\x1dGetDataForSEOCategoryResponse\x12\x1f\n" +
	"\vbusiness_id\x18\x01 \x01(\tR\n" +
	"businessId\x12.\n" +
	"\x13primary_category_id\x18\x02 \x01(\tR\x11primaryCategoryId\x12!\n" +
	"\fcategory_ids\x18\x03 \x03(\tR\vcategoryIds\x12\x17\n" +
	"\atask_id\x18\x04 \x01(\tR\x06taskId\x12!\n" +
	"\fraw_response\x18\x05 \x01(\tR\vrawResponse\x124\n" +
	"\acreated\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\acreated\x124\n" +
	"\aupdated\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\aupdated\x124\n" +
	"\adeleted\x18\b \x01(\v2\x1a.google.protobuf.TimestampR\adeleted*\xde\x03\n" +
	"\bVicinity\x12\x16\n" +
	"\x12VICINITY_UNDEFINED\x10\x00\x12\x11\n" +
	"\rVICINITY_CITY\x10\x01\x12\x0f\n" +
	"\vVICINITY_A1\x10\x02\x12\x0f\n" +
	"\vVICINITY_A2\x10\x03\x12\x0f\n" +
	"\vVICINITY_A3\x10\x04\x12\x0f\n" +
	"\vVICINITY_A4\x10\x05\x12\x0f\n" +
	"\vVICINITY_A5\x10\x06\x12\x0f\n" +
	"\vVICINITY_B1\x10\a\x12\x0f\n" +
	"\vVICINITY_B2\x10\b\x12\x0f\n" +
	"\vVICINITY_B3\x10\t\x12\x0f\n" +
	"\vVICINITY_B4\x10\n" +
	"\x12\x0f\n" +
	"\vVICINITY_B5\x10\v\x12\x0f\n" +
	"\vVICINITY_C1\x10\f\x12\x0f\n" +
	"\vVICINITY_C2\x10\r\x12\x0f\n" +
	"\vVICINITY_C3\x10\x0e\x12\x0f\n" +
	"\vVICINITY_C4\x10\x0f\x12\x0f\n" +
	"\vVICINITY_C5\x10\x10\x12\x0f\n" +
	"\vVICINITY_D1\x10\x11\x12\x0f\n" +
	"\vVICINITY_D2\x10\x12\x12\x0f\n" +
	"\vVICINITY_D3\x10\x13\x12\x0f\n" +
	"\vVICINITY_D4\x10\x14\x12\x0f\n" +
	"\vVICINITY_D5\x10\x15\x12\x0f\n" +
	"\vVICINITY_E1\x10\x16\x12\x0f\n" +
	"\vVICINITY_E2\x10\x17\x12\x0f\n" +
	"\vVICINITY_E3\x10\x18\x12\x0f\n" +
	"\vVICINITY_E4\x10\x19\x12\x0f\n" +
	"\vVICINITY_E5\x10\x1a*\x8a\x01\n" +
	"\x0eGBPClaimStatus\x12\x1c\n" +
	"\x18GBP_CLAIM_STATUS_INVALID\x10\x00\x12\x1c\n" +
	"\x18GBP_CLAIM_STATUS_UNKNOWN\x10\x01\x12\x1c\n" +
	"\x18GBP_CLAIM_STATUS_CLAIMED\x10\x02\x12\x1e\n" +
	"\x1aGBP_CLAIM_STATUS_UNCLAIMED\x10\x03B\x86\x01\n" +
	")com.vendasta.listingproducts.v1.generatedB\bSeoProtoZOgithub.com/vendasta/generated-protos-go/listing_products/v1;listing_products_v1b\x06proto3"

var (
	file_listing_products_v1_seo_proto_rawDescOnce sync.Once
	file_listing_products_v1_seo_proto_rawDescData []byte
)

func file_listing_products_v1_seo_proto_rawDescGZIP() []byte {
	file_listing_products_v1_seo_proto_rawDescOnce.Do(func() {
		file_listing_products_v1_seo_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_listing_products_v1_seo_proto_rawDesc), len(file_listing_products_v1_seo_proto_rawDesc)))
	})
	return file_listing_products_v1_seo_proto_rawDescData
}

var file_listing_products_v1_seo_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_listing_products_v1_seo_proto_msgTypes = make([]protoimpl.MessageInfo, 36)
var file_listing_products_v1_seo_proto_goTypes = []any{
	(Vicinity)(0),       // 0: listing_products.v1.Vicinity
	(GBPClaimStatus)(0), // 1: listing_products.v1.GBPClaimStatus
	(AddonActivation_AddonActivationStatus)(0), // 2: listing_products.v1.AddonActivation.AddonActivationStatus
	(*SEODataSummary)(nil),                     // 3: listing_products.v1.SEODataSummary
	(*SEOData)(nil),                            // 4: listing_products.v1.SEOData
	(*LocalSearchData)(nil),                    // 5: listing_products.v1.LocalSearchData
	(*LocalSearchResult)(nil),                  // 6: listing_products.v1.LocalSearchResult
	(*LocalSearchReviews)(nil),                 // 7: listing_products.v1.LocalSearchReviews
	(*GetSEODataRequest)(nil),                  // 8: listing_products.v1.GetSEODataRequest
	(*GetSEODataSummaryRequest)(nil),           // 9: listing_products.v1.GetSEODataSummaryRequest
	(*GetAllAIOAuditRequest)(nil),              // 10: listing_products.v1.GetAllAIOAuditRequest
	(*GetAllAIOAuditResponse)(nil),             // 11: listing_products.v1.GetAllAIOAuditResponse
	(*AIOAuditResults)(nil),                    // 12: listing_products.v1.AIOAuditResults
	(*AuditPageData)(nil),                      // 13: listing_products.v1.AuditPageData
	(*AuditScores)(nil),                        // 14: listing_products.v1.AuditScores
	(*AuditScoreResults)(nil),                  // 15: listing_products.v1.AuditScoreResults
	(*GetAIOAuditRequest)(nil),                 // 16: listing_products.v1.GetAIOAuditRequest
	(*GetAIOAuditResponse)(nil),                // 17: listing_products.v1.GetAIOAuditResponse
	(*GetAllAIOAuditScoreResultsRequest)(nil),  // 18: listing_products.v1.GetAllAIOAuditScoreResultsRequest
	(*GetAllAIOAuditScoreResultsResponse)(nil), // 19: listing_products.v1.GetAllAIOAuditScoreResultsResponse
	(*GetAIOAuditStatusRequest)(nil),           // 20: listing_products.v1.GetAIOAuditStatusRequest
	(*GetAIOAuditStatusResponse)(nil),          // 21: listing_products.v1.GetAIOAuditStatusResponse
	(*TriggerAIOAuditRequest)(nil),             // 22: listing_products.v1.TriggerAIOAuditRequest
	(*TriggerAIOAuditResponse)(nil),            // 23: listing_products.v1.TriggerAIOAuditResponse
	(*GetSEODataSummaryResponse)(nil),          // 24: listing_products.v1.GetSEODataSummaryResponse
	(*GetLocalSearchSEODataRequest)(nil),       // 25: listing_products.v1.GetLocalSearchSEODataRequest
	(*GetLocalSearchSEODataResponse)(nil),      // 26: listing_products.v1.GetLocalSearchSEODataResponse
	(*AddonActivation)(nil),                    // 27: listing_products.v1.AddonActivation
	(*GetSEODataResponse)(nil),                 // 28: listing_products.v1.GetSEODataResponse
	(*BusinessKeywords)(nil),                   // 29: listing_products.v1.BusinessKeywords
	(*StartLocalSEODataWorkflowRequest)(nil),   // 30: listing_products.v1.StartLocalSEODataWorkflowRequest
	(*GetSEOSettingsRequest)(nil),              // 31: listing_products.v1.GetSEOSettingsRequest
	(*SaveSEOSettingsRequest)(nil),             // 32: listing_products.v1.SaveSEOSettingsRequest
	(*SEOSettingsResponse)(nil),                // 33: listing_products.v1.SEOSettingsResponse
	(*GetActiveSEOAddonsRequest)(nil),          // 34: listing_products.v1.GetActiveSEOAddonsRequest
	(*GetActiveSEOAddonsResponse)(nil),         // 35: listing_products.v1.GetActiveSEOAddonsResponse
	(*StartSEOCategoryWorkflowRequest)(nil),    // 36: listing_products.v1.StartSEOCategoryWorkflowRequest
	(*GetDataForSEOCategoryRequest)(nil),       // 37: listing_products.v1.GetDataForSEOCategoryRequest
	(*GetDataForSEOCategoryResponse)(nil),      // 38: listing_products.v1.GetDataForSEOCategoryResponse
	(*timestamppb.Timestamp)(nil),              // 39: google.protobuf.Timestamp
	(*Geo)(nil),                                // 40: listing_products.v1.Geo
	(*vendasta_types.FieldMask)(nil),           // 41: vendastatypes.FieldMask
}
var file_listing_products_v1_seo_proto_depIdxs = []int32{
	39, // 0: listing_products.v1.SEODataSummary.date:type_name -> google.protobuf.Timestamp
	39, // 1: listing_products.v1.SEOData.date:type_name -> google.protobuf.Timestamp
	5,  // 2: listing_products.v1.SEOData.local_searches:type_name -> listing_products.v1.LocalSearchData
	0,  // 3: listing_products.v1.LocalSearchData.vicinity:type_name -> listing_products.v1.Vicinity
	40, // 4: listing_products.v1.LocalSearchData.search_location:type_name -> listing_products.v1.Geo
	6,  // 5: listing_products.v1.LocalSearchData.results:type_name -> listing_products.v1.LocalSearchResult
	7,  // 6: listing_products.v1.LocalSearchResult.reviews:type_name -> listing_products.v1.LocalSearchReviews
	1,  // 7: listing_products.v1.LocalSearchResult.claim_status:type_name -> listing_products.v1.GBPClaimStatus
	39, // 8: listing_products.v1.GetSEODataRequest.start_date:type_name -> google.protobuf.Timestamp
	39, // 9: listing_products.v1.GetSEODataRequest.end_date:type_name -> google.protobuf.Timestamp
	39, // 10: listing_products.v1.GetSEODataSummaryRequest.start_date:type_name -> google.protobuf.Timestamp
	39, // 11: listing_products.v1.GetSEODataSummaryRequest.end_date:type_name -> google.protobuf.Timestamp
	12, // 12: listing_products.v1.GetAllAIOAuditResponse.audit:type_name -> listing_products.v1.AIOAuditResults
	39, // 13: listing_products.v1.AIOAuditResults.start_date:type_name -> google.protobuf.Timestamp
	13, // 14: listing_products.v1.AIOAuditResults.audit_pages:type_name -> listing_products.v1.AuditPageData
	15, // 15: listing_products.v1.AIOAuditResults.audit_score_results:type_name -> listing_products.v1.AuditScoreResults
	14, // 16: listing_products.v1.AuditScoreResults.audit_scores:type_name -> listing_products.v1.AuditScores
	12, // 17: listing_products.v1.GetAIOAuditResponse.audit:type_name -> listing_products.v1.AIOAuditResults
	15, // 18: listing_products.v1.GetAllAIOAuditScoreResultsResponse.audit_score_results:type_name -> listing_products.v1.AuditScoreResults
	3,  // 19: listing_products.v1.GetSEODataSummaryResponse.data:type_name -> listing_products.v1.SEODataSummary
	3,  // 20: listing_products.v1.GetSEODataSummaryResponse.previous_data:type_name -> listing_products.v1.SEODataSummary
	39, // 21: listing_products.v1.GetLocalSearchSEODataRequest.start_date:type_name -> google.protobuf.Timestamp
	39, // 22: listing_products.v1.GetLocalSearchSEODataRequest.end_date:type_name -> google.protobuf.Timestamp
	5,  // 23: listing_products.v1.GetLocalSearchSEODataResponse.local_search_data:type_name -> listing_products.v1.LocalSearchData
	39, // 24: listing_products.v1.AddonActivation.activated:type_name -> google.protobuf.Timestamp
	39, // 25: listing_products.v1.AddonActivation.deactivated:type_name -> google.protobuf.Timestamp
	2,  // 26: listing_products.v1.AddonActivation.status:type_name -> listing_products.v1.AddonActivation.AddonActivationStatus
	4,  // 27: listing_products.v1.GetSEODataResponse.data:type_name -> listing_products.v1.SEOData
	4,  // 28: listing_products.v1.GetSEODataResponse.previous_data:type_name -> listing_products.v1.SEOData
	29, // 29: listing_products.v1.StartLocalSEODataWorkflowRequest.businesses:type_name -> listing_products.v1.BusinessKeywords
	39, // 30: listing_products.v1.StartLocalSEODataWorkflowRequest.date:type_name -> google.protobuf.Timestamp
	41, // 31: listing_products.v1.SaveSEOSettingsRequest.field_mask:type_name -> vendastatypes.FieldMask
	27, // 32: listing_products.v1.GetActiveSEOAddonsResponse.active_addons:type_name -> listing_products.v1.AddonActivation
	39, // 33: listing_products.v1.GetDataForSEOCategoryResponse.created:type_name -> google.protobuf.Timestamp
	39, // 34: listing_products.v1.GetDataForSEOCategoryResponse.updated:type_name -> google.protobuf.Timestamp
	39, // 35: listing_products.v1.GetDataForSEOCategoryResponse.deleted:type_name -> google.protobuf.Timestamp
	36, // [36:36] is the sub-list for method output_type
	36, // [36:36] is the sub-list for method input_type
	36, // [36:36] is the sub-list for extension type_name
	36, // [36:36] is the sub-list for extension extendee
	0,  // [0:36] is the sub-list for field type_name
}

func init() { file_listing_products_v1_seo_proto_init() }
func file_listing_products_v1_seo_proto_init() {
	if File_listing_products_v1_seo_proto != nil {
		return
	}
	file_listing_products_v1_listing_profile_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_listing_products_v1_seo_proto_rawDesc), len(file_listing_products_v1_seo_proto_rawDesc)),
			NumEnums:      3,
			NumMessages:   36,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_listing_products_v1_seo_proto_goTypes,
		DependencyIndexes: file_listing_products_v1_seo_proto_depIdxs,
		EnumInfos:         file_listing_products_v1_seo_proto_enumTypes,
		MessageInfos:      file_listing_products_v1_seo_proto_msgTypes,
	}.Build()
	File_listing_products_v1_seo_proto = out.File
	file_listing_products_v1_seo_proto_goTypes = nil
	file_listing_products_v1_seo_proto_depIdxs = nil
}
